/**
 * Comprehensive test suite for session management system
 */

import { describe, it, expect, beforeEach, afterEach, jest } from '@jest/globals'
import { RedisSessionRepository } from '@/lib/data/repositories/redis-session-repository'
import { SessionUseCases } from '@/lib/domain/usecases/session'
import { CreateSessionDTO, SessionFilter } from '@/lib/domain/entities/session'
import { generateDeviceId, generateSessionMetadata } from '@/lib/utils/session'

// Mock Redis cache
const mockCache = {
  get: jest.fn(),
  set: jest.fn(),
  del: jest.fn(),
}

// Mock repositories
const mockStudentRepo = {
  findById: jest.fn().mockResolvedValue({
    id: 1,
    name: 'Test Student',
    uniqueCode: 'test-code',
    googleEmail: '<EMAIL>',
    username: 'testuser',
    passwordHash: 'hash',
    role: 'student',
    createdAt: new Date()
  })
}

const mockAdminRepo = {
  findById: jest.fn().mockResolvedValue({
    id: 1,
    username: 'admin',
    name: 'Test Admin',
    passwordHash: 'hash',
    role: 'super_admin',
    createdAt: new Date()
  })
}

describe('Session Management System', () => {
  let sessionRepo: RedisSessionRepository
  let sessionUseCases: SessionUseCases

  beforeEach(() => {
    jest.clearAllMocks()
    sessionRepo = new RedisSessionRepository(mockCache as any)
    sessionUseCases = new SessionUseCases(
      sessionRepo,
      mockStudentRepo as any,
      mockAdminRepo as any,
      mockCache as any
    )
  })

  afterEach(() => {
    jest.clearAllMocks()
  })

  describe('Session Creation', () => {
    it('should create a session successfully', async () => {
      const userAgent = 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36'
      const ipAddress = '*************'
      const deviceId = generateDeviceId(userAgent, ipAddress)
      const metadata = generateSessionMetadata(userAgent, ipAddress)

      const sessionData: CreateSessionDTO = {
        userId: 1,
        role: 'student',
        deviceId,
        ipAddress,
        userAgent,
        durationSeconds: 3600,
        metadata
      }

      // Mock Redis operations
      mockCache.get.mockResolvedValue(null) // No existing session
      mockCache.set.mockResolvedValue(undefined)

      const session = await sessionUseCases.createSession(sessionData)

      expect(session).toBeDefined()
      expect(session.userId).toBe(1)
      expect(session.role).toBe('student')
      expect(session.deviceId).toBe(deviceId)
      expect(session.isActive).toBe(true)
      expect(mockCache.set).toHaveBeenCalled()
    })

    it('should invalidate existing session on same device', async () => {
      const userAgent = 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_7_1 like Mac OS X)'
      const ipAddress = '*************'
      const deviceId = generateDeviceId(userAgent, ipAddress)

      const sessionData: CreateSessionDTO = {
        userId: 1,
        role: 'student',
        deviceId,
        ipAddress,
        userAgent,
        durationSeconds: 3600
      }

      // Mock existing session
      const existingSession = {
        sessionId: 'existing-session-id',
        userId: 1,
        role: 'student',
        deviceId,
        ipAddress,
        userAgent,
        createdAt: new Date(),
        lastAccessedAt: new Date(),
        expiresAt: new Date(Date.now() + 3600000),
        isActive: true
      }

      mockCache.get
        .mockResolvedValueOnce('existing-session-id') // Device session mapping
        .mockResolvedValueOnce(JSON.stringify(existingSession)) // Existing session data
        .mockResolvedValue(null) // Other calls

      mockCache.set.mockResolvedValue(undefined)
      mockCache.del.mockResolvedValue(undefined)

      const session = await sessionUseCases.createSession(sessionData)

      expect(session).toBeDefined()
      expect(mockCache.del).toHaveBeenCalled() // Existing session should be deleted
    })

    it('should validate user exists before creating session', async () => {
      mockStudentRepo.findById.mockResolvedValue(null)

      const sessionData: CreateSessionDTO = {
        userId: 999,
        role: 'student',
        deviceId: 'device-id',
        ipAddress: '*************',
        userAgent: 'test-agent',
        durationSeconds: 3600
      }

      await expect(sessionUseCases.createSession(sessionData)).rejects.toThrow('Student not found')
    })
  })

  describe('Session Validation', () => {
    it('should validate active session successfully', async () => {
      const sessionId = 'test-session-id'
      const session = {
        sessionId,
        userId: 1,
        role: 'student',
        deviceId: 'device-id',
        ipAddress: '*************',
        userAgent: 'test-agent',
        createdAt: new Date(),
        lastAccessedAt: new Date(),
        expiresAt: new Date(Date.now() + 3600000), // 1 hour from now
        isActive: true
      }

      mockCache.get.mockResolvedValue(JSON.stringify(session))
      mockCache.set.mockResolvedValue(undefined) // For updating last accessed

      const validation = await sessionUseCases.validateSession(sessionId)

      expect(validation.isValid).toBe(true)
      expect(validation.session).toBeDefined()
      expect(validation.refreshed).toBe(true)
    })

    it('should reject expired session', async () => {
      const sessionId = 'expired-session-id'
      const expiredSession = {
        sessionId,
        userId: 1,
        role: 'student',
        deviceId: 'device-id',
        ipAddress: '*************',
        userAgent: 'test-agent',
        createdAt: new Date(),
        lastAccessedAt: new Date(),
        expiresAt: new Date(Date.now() - 1000), // Expired 1 second ago
        isActive: true
      }

      mockCache.get.mockResolvedValue(JSON.stringify(expiredSession))
      mockCache.del.mockResolvedValue(undefined)

      const validation = await sessionUseCases.validateSession(sessionId)

      expect(validation.isValid).toBe(false)
      expect(validation.error).toBe('Session has expired')
      expect(mockCache.del).toHaveBeenCalled() // Should clean up expired session
    })

    it('should reject non-existent session', async () => {
      mockCache.get.mockResolvedValue(null)

      const validation = await sessionUseCases.validateSession('non-existent-session')

      expect(validation.isValid).toBe(false)
      expect(validation.error).toBe('Session not found')
    })
  })

  describe('Session Invalidation', () => {
    it('should invalidate session successfully', async () => {
      const sessionId = 'test-session-id'
      const session = {
        sessionId,
        userId: 1,
        role: 'student',
        deviceId: 'device-id',
        ipAddress: '*************',
        userAgent: 'test-agent',
        createdAt: new Date(),
        lastAccessedAt: new Date(),
        expiresAt: new Date(Date.now() + 3600000),
        isActive: true
      }

      mockCache.get
        .mockResolvedValueOnce(JSON.stringify(session)) // Get session
        .mockResolvedValue('[]') // User sessions index

      mockCache.del.mockResolvedValue(undefined)
      mockCache.set.mockResolvedValue(undefined)

      const result = await sessionUseCases.invalidateSession(sessionId)

      expect(result).toBe(true)
      expect(mockCache.del).toHaveBeenCalledWith(`session:${sessionId}`)
    })

    it('should return false for non-existent session', async () => {
      mockCache.get.mockResolvedValue(null)

      const result = await sessionUseCases.invalidateSession('non-existent-session')

      expect(result).toBe(false)
    })
  })

  describe('User Session Management', () => {
    it('should get all user sessions', async () => {
      const userId = 1
      const sessionIds = ['session-1', 'session-2']
      const sessions = sessionIds.map(id => ({
        sessionId: id,
        userId,
        role: 'student',
        deviceId: `device-${id}`,
        ipAddress: '*************',
        userAgent: 'test-agent',
        createdAt: new Date(),
        lastAccessedAt: new Date(),
        expiresAt: new Date(Date.now() + 3600000),
        isActive: true
      }))

      mockCache.get
        .mockResolvedValueOnce(JSON.stringify(sessionIds)) // User sessions index
        .mockResolvedValueOnce(JSON.stringify(sessions[0])) // First session
        .mockResolvedValueOnce(JSON.stringify(sessions[1])) // Second session

      const userSessions = await sessionUseCases.getUserSessions(userId)

      expect(userSessions).toHaveLength(2)
      expect(userSessions[0].sessionId).toBe('session-1')
      expect(userSessions[1].sessionId).toBe('session-2')
    })

    it('should force logout all user sessions', async () => {
      const userId = 1
      const sessionIds = ['session-1', 'session-2']
      const sessions = sessionIds.map(id => ({
        sessionId: id,
        userId,
        role: 'student',
        deviceId: `device-${id}`,
        ipAddress: '*************',
        userAgent: 'test-agent',
        createdAt: new Date(),
        lastAccessedAt: new Date(),
        expiresAt: new Date(Date.now() + 3600000),
        isActive: true
      }))

      mockCache.get
        .mockResolvedValueOnce(JSON.stringify(sessionIds)) // User sessions index
        .mockResolvedValueOnce(JSON.stringify(sessions[0])) // First session
        .mockResolvedValueOnce(JSON.stringify(sessions[1])) // Second session
        .mockResolvedValue('[]') // Subsequent calls

      mockCache.del.mockResolvedValue(undefined)
      mockCache.set.mockResolvedValue(undefined)

      const loggedOutCount = await sessionUseCases.forceLogoutUser(userId)

      expect(loggedOutCount).toBe(2)
      expect(mockCache.del).toHaveBeenCalledTimes(6) // 2 sessions × 3 keys each
    })
  })

  describe('Admin Operations', () => {
    it('should allow super admin to list sessions', async () => {
      mockAdminRepo.findById.mockResolvedValue({
        id: 1,
        username: 'superadmin',
        name: 'Super Admin',
        passwordHash: 'hash',
        role: 'super_admin',
        createdAt: new Date()
      })

      const sessionIds = ['session-1']
      const session = {
        sessionId: 'session-1',
        userId: 2,
        role: 'student',
        deviceId: 'device-1',
        ipAddress: '*************',
        userAgent: 'test-agent',
        createdAt: new Date(),
        lastAccessedAt: new Date(),
        expiresAt: new Date(Date.now() + 3600000),
        isActive: true
      }

      mockCache.get
        .mockResolvedValueOnce(JSON.stringify(sessionIds)) // Global session index
        .mockResolvedValueOnce(JSON.stringify(session)) // Session data

      const sessions = await sessionUseCases.listSessions({}, 1)

      expect(sessions).toHaveLength(1)
      expect(sessions[0].sessionId).toBe('session-1')
      expect(sessions[0].userName).toBe('Test Student') // From mock student repo
    })

    it('should reject non-super-admin from listing sessions', async () => {
      mockAdminRepo.findById.mockResolvedValue({
        id: 1,
        username: 'admin',
        name: 'Regular Admin',
        passwordHash: 'hash',
        role: 'admin',
        createdAt: new Date()
      })

      await expect(sessionUseCases.listSessions({}, 1)).rejects.toThrow('Super admin access required')
    })

    it('should prevent admin from invalidating own session', async () => {
      const sessionId = 'admin-session-id'
      const session = {
        sessionId,
        userId: 1, // Same as requesting admin
        role: 'super_admin',
        deviceId: 'device-id',
        ipAddress: '*************',
        userAgent: 'test-agent',
        createdAt: new Date(),
        lastAccessedAt: new Date(),
        expiresAt: new Date(Date.now() + 3600000),
        isActive: true
      }

      mockCache.get.mockResolvedValue(JSON.stringify(session))

      await expect(sessionUseCases.adminInvalidateSession(sessionId, 1))
        .rejects.toThrow('Cannot invalidate your own session')
    })
  })

  describe('Device Fingerprinting', () => {
    it('should generate consistent device ID for same user agent', () => {
      const userAgent = 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36'
      const ipAddress = '*************'

      const deviceId1 = generateDeviceId(userAgent, ipAddress)
      const deviceId2 = generateDeviceId(userAgent, ipAddress)

      // Device IDs should have same prefix but different suffix (due to UUID)
      expect(deviceId1.split('-')[0]).toBe(deviceId2.split('-')[0])
    })

    it('should generate different device IDs for different user agents', () => {
      const ipAddress = '*************'
      const desktopUA = 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36'
      const mobileUA = 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_7_1 like Mac OS X)'

      const desktopDeviceId = generateDeviceId(desktopUA, ipAddress)
      const mobileDeviceId = generateDeviceId(mobileUA, ipAddress)

      expect(desktopDeviceId.split('-')[0]).not.toBe(mobileDeviceId.split('-')[0])
    })

    it('should extract device metadata correctly', () => {
      const userAgent = 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_7_1 like Mac OS X) AppleWebKit/605.1.15'
      const ipAddress = '*************'

      const metadata = generateSessionMetadata(userAgent, ipAddress)

      expect(metadata.deviceType).toBe('mobile')
      expect(metadata.browser).toBe('Mobile Safari')
      expect(metadata.os).toBe('iOS')
    })
  })

  describe('Session Filtering', () => {
    it('should filter sessions by role', async () => {
      const filter: SessionFilter = { role: 'student' }
      
      // Mock implementation would filter sessions
      mockCache.get.mockResolvedValue(JSON.stringify(['session-1']))
      mockCache.get.mockResolvedValue(JSON.stringify({
        sessionId: 'session-1',
        userId: 1,
        role: 'student',
        deviceId: 'device-1',
        ipAddress: '*************',
        userAgent: 'test-agent',
        createdAt: new Date(),
        lastAccessedAt: new Date(),
        expiresAt: new Date(Date.now() + 3600000),
        isActive: true
      }))

      const sessions = await sessionUseCases.listSessions(filter, 1)
      
      expect(sessions.every(s => s.role === 'student')).toBe(true)
    })
  })
})

describe('Session Utilities', () => {
  describe('Device ID Generation', () => {
    it('should generate valid device ID format', () => {
      const userAgent = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
      const ipAddress = '*************'

      const deviceId = generateDeviceId(userAgent, ipAddress)

      expect(deviceId).toMatch(/^[a-zA-Z0-9]{16}-[a-f0-9]{8}$/)
    })
  })

  describe('Session Metadata', () => {
    it('should parse desktop user agent correctly', () => {
      const userAgent = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
      const ipAddress = '*************'

      const metadata = generateSessionMetadata(userAgent, ipAddress)

      expect(metadata.deviceType).toBe('desktop')
      expect(metadata.browser).toBe('Chrome')
      expect(metadata.os).toBe('Windows')
    })

    it('should parse mobile user agent correctly', () => {
      const userAgent = 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_7_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.2 Mobile/15E148 Safari/604.1'
      const ipAddress = '*************'

      const metadata = generateSessionMetadata(userAgent, ipAddress)

      expect(metadata.deviceType).toBe('mobile')
      expect(metadata.browser).toBe('Mobile Safari')
      expect(metadata.os).toBe('iOS')
    })
  })
})
