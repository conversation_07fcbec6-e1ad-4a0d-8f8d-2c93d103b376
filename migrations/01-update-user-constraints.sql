-- Migration untuk mengubah constraint users agar student tidak perlu googleEmail
-- dan mendukung autentikasi dengan username/password

-- 1. Hapus constraint yang ada
ALTER TABLE users DROP CONSTRAINT IF EXISTS chk_role_data;

-- 2. Tambahkan constraint baru yang:
--    - Tidak mengharuskan googleEmail untuk student
--    - Mengharuskan uniqueCode, username, passwordHash untuk student
--    - Mengharuskan username dan passwordHash untuk admin, uniqueCode NULL, googleEmail NULL
ALTER TABLE users ADD CONSTRAINT chk_role_data CHECK (
  (role = 'student' AND unique_code IS NOT NULL AND username IS NOT NULL AND password_hash IS NOT NULL) OR 
  (role = 'admin' AND google_email IS NULL AND unique_code IS NULL AND username IS NOT NULL AND password_hash IS NOT NULL)
);

-- 3. Buat index untuk mendukung query login dengan username
CREATE INDEX IF NOT EXISTS idx_users_username_role ON users(username, role);

-- Catatan: Constraint ini memastikan bahwa semua student memiliki uniqueCode, username, dan passwordHash,
-- sementara admin hanya memiliki username dan passwordHash. Data lain seperti googleEmail menjadi opsional untuk student. 