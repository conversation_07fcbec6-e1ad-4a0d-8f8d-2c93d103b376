const { drizzle } = require('drizzle-orm/postgres-js');
const { migrate } = require('drizzle-orm/postgres-js/migrator');
const postgres = require('postgres');
const dotenv = require('dotenv');

// Load environment variables from .env and .env.local
try {
  dotenv.config({ path: '.env' });
  dotenv.config({ path: '.env.local', override: true });
} catch (error) {
  console.error('Error loading environment variables:', error);
}

// Get database connection string from environment variables
// For development, we're using SSH tunneling to connect to the database
// The tunnel script (scripts/dev-tunnel.sh) sets up the connection and updates .env.local
const connectionString = process.env.DATABASE_URL;

if (!connectionString) {
  console.error('Error: DATABASE_URL is not set in environment variables.');
  console.error('Please run the tunnel script: npm run tunnel');
  console.error('Or set DATABASE_URL manually in .env.local');
  console.error('\nFormat DATABASE_URL: postgres://username:password@host:port/database');
  console.error('Example: postgres://postgres:postgres@localhost:5432/website');
  process.exit(1);
}

// Create a postgres client
const client = postgres(connectionString);

// Create a drizzle instance
const db = drizzle(client);

// Get the materialized view SQL from the schema
let createAttendanceSummaryViewSQL = '';
let refreshAttendanceSummaryViewSQL = '';

// We'll use hardcoded SQL for the materialized view
// This avoids issues with importing TypeScript files from JavaScript

// SQL for creating the materialized view
createAttendanceSummaryViewSQL = `
CREATE MATERIALIZED VIEW IF NOT EXISTS attendance_summary AS
WITH pivoted AS (
  SELECT
    DATE(recorded_at) AS summary_date,
    unique_code,
    BOOL_OR(CASE WHEN type = 'Zuhr' THEN TRUE ELSE FALSE END) AS zuhr,
    BOOL_OR(CASE WHEN type = 'Asr' THEN TRUE ELSE FALSE END) AS asr,
    BOOL_OR(CASE WHEN type = 'Pulang' THEN TRUE ELSE FALSE END) AS pulang,
    BOOL_OR(CASE WHEN type = 'Ijin' THEN TRUE ELSE FALSE END) AS ijin,
    MAX(recorded_at) AS last_updated
  FROM absences
  GROUP BY DATE(recorded_at), unique_code
)
SELECT
  p.summary_date,
  u.unique_code,
  u.name,
  c.name AS class_name,
  p.zuhr,
  p.asr,
  p.pulang,
  p.ijin,
  p.last_updated AS updated_at
FROM pivoted p
JOIN users u ON p.unique_code = u.unique_code
JOIN classes c ON u.class_id = c.id
WITH NO DATA;

CREATE INDEX IF NOT EXISTS idx_attendance_summary_date ON attendance_summary(summary_date);
`;

// SQL for refreshing the materialized view
refreshAttendanceSummaryViewSQL = `
REFRESH MATERIALIZED VIEW attendance_summary;
`;

// Run migrations and create materialized view
async function main() {
  console.log('Running migrations...');

  try {
    // Run Drizzle migrations
    await migrate(db, { migrationsFolder: 'drizzle/migrations' });
    console.log('Migrations completed successfully');

    // Drop existing materialized view
    console.log('Dropping existing attendance_summary materialized view...');
    try {
      await client.unsafe(`DROP MATERIALIZED VIEW IF EXISTS attendance_summary;`);
      console.log('Materialized view dropped successfully');
    } catch (error) {
      console.error('Error dropping materialized view:', error);
      // Continue even if there's an error
    }

    // Create materialized view
    console.log('Creating attendance_summary materialized view...');
    await client.unsafe(createAttendanceSummaryViewSQL);
    console.log('Materialized view created successfully');

    // Refresh materialized view
    console.log('Refreshing attendance_summary materialized view...');
    await client.unsafe(refreshAttendanceSummaryViewSQL);
    console.log('Materialized view refreshed successfully');

    console.log('Database setup completed successfully');
  } catch (error) {
    console.error('Error setting up database:', error);
    process.exit(1);
  } finally {
    await client.end();
  }
}

main();
