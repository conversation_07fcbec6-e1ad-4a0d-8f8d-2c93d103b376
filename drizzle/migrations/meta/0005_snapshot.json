{"id": "6c3f4901-a911-4b3c-9827-ce25e3ba4d4c", "prevId": "09eb47de-ce2a-43a2-b1c9-3fdeca7b82f6", "version": "6", "dialect": "postgresql", "tables": {"public.absences": {"name": "absences", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "unique_code": {"name": "unique_code", "type": "<PERSON><PERSON><PERSON>(36)", "primaryKey": false, "notNull": true}, "type": {"name": "type", "type": "attendance_type", "typeSchema": "public", "primaryKey": false, "notNull": true}, "recorded_at": {"name": "recorded_at", "type": "timestamp", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"absences_unique_code_users_unique_code_fk": {"name": "absences_unique_code_users_unique_code_fk", "tableFrom": "absences", "tableTo": "users", "columnsFrom": ["unique_code"], "columnsTo": ["unique_code"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.classes": {"name": "classes", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"classes_name_unique": {"name": "classes_name_unique", "nullsNotDistinct": false, "columns": ["name"]}}}, "public.users": {"name": "users", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "role": {"name": "role", "type": "user_role", "typeSchema": "public", "primaryKey": false, "notNull": true}, "unique_code": {"name": "unique_code", "type": "<PERSON><PERSON><PERSON>(36)", "primaryKey": false, "notNull": false}, "google_email": {"name": "google_email", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "nis": {"name": "nis", "type": "<PERSON><PERSON><PERSON>(10)", "primaryKey": false, "notNull": false}, "username": {"name": "username", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "whatsapp": {"name": "whatsapp", "type": "<PERSON><PERSON><PERSON>(15)", "primaryKey": false, "notNull": false}, "class_id": {"name": "class_id", "type": "integer", "primaryKey": false, "notNull": false}, "password_hash": {"name": "password_hash", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"users_unique_code_unique": {"name": "users_unique_code_unique", "nullsNotDistinct": false, "columns": ["unique_code"]}, "users_google_email_unique": {"name": "users_google_email_unique", "nullsNotDistinct": false, "columns": ["google_email"]}, "users_username_unique": {"name": "users_username_unique", "nullsNotDistinct": false, "columns": ["username"]}}}}, "enums": {"public.attendance_type": {"name": "attendance_type", "schema": "public", "values": ["Zuhr", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Ijin"]}, "public.user_role": {"name": "user_role", "schema": "public", "values": ["student", "admin"]}}, "schemas": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}