-- Drop the existing constraint
ALTER TABLE "users" DROP CONSTRAINT IF EXISTS "chk_role_data";

-- Add a new constraint that allows students to have username/password authentication
ALTER TABLE "users" ADD CONSTRAINT "chk_role_data" CHECK (
  (role = 'student' AND unique_code IS NOT NULL AND (
    google_email IS NOT NULL OR (username IS NOT NULL AND password_hash IS NOT NULL)
  )) OR
  (role = 'admin' AND google_email IS NULL AND unique_code IS NULL AND username IS NOT NULL AND password_hash IS NOT NULL)
);

-- Add index for username lookups to improve performance
CREATE INDEX IF NOT EXISTS "idx_users_username" ON "users" ("username");

-- Add index for whatsapp lookups to improve performance
CREATE INDEX IF NOT EXISTS "idx_users_whatsapp" ON "users" ("whatsapp"); 