-- Drop the existing constraint
ALTER TABLE "users" DROP CONSTRAINT IF EXISTS "chk_role_data";

-- Add updated constraint that makes googleEmail optional for students but requires username and password
ALTER TABLE "users" ADD CONSTRAINT "chk_role_data" CHECK (
  (role = 'student' AND unique_code IS NOT NULL AND username IS NOT NULL AND password_hash IS NOT NULL) OR 
  (role = 'admin' AND google_email IS NULL AND unique_code IS NULL AND username IS NOT NULL AND password_hash IS NOT NULL)
);

-- Create or update index for username-based login
CREATE INDEX IF NOT EXISTS idx_users_username_role ON users(username, role); 