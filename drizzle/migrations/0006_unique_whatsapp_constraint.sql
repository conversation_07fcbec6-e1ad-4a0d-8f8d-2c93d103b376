-- Migration to add a unique constraint to the whatsapp column
-- This ensures that a WhatsApp number can only be used by one student account

-- Step 1: First clean up any NULL whatsapp values to ensure the constraint works properly
UPDATE users
SET whatsapp = NULL
WHERE whatsapp = '';

-- Step 2: Add a partial unique index on whatsapp for student role
-- This allows NULL values (unverified) but prevents duplicates for non-NULL values
-- The partial index only applies to students, allowing admins to have the same WhatsApp if needed
CREATE UNIQUE INDEX IF NOT EXISTS unique_student_whatsapp 
ON users (whatsapp)
WHERE whatsapp IS NOT NULL AND role = 'student';

-- Step 3: Add a comment to document the constraint
COMMENT ON INDEX unique_student_whatsapp IS 'Ensures WhatsApp numbers are unique among students'; 