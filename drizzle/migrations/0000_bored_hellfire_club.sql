DO $$ BEGIN
 CREATE TYPE "public"."attendance_type" AS ENUM('Zuhr', 'Asr', 'Pulang');
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 CREATE TYPE "public"."user_role" AS ENUM('student', 'admin');
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
CREATE TABLE IF NOT EXISTS "absences" (
	"id" serial PRIMARY KEY NOT NULL,
	"unique_code" varchar(36) NOT NULL,
	"type" "attendance_type" NOT NULL,
	"recorded_at" timestamp NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE IF NOT EXISTS "classes" (
	"id" serial PRIMARY KEY NOT NULL,
	"name" varchar(10) NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL,
	CONSTRAINT "classes_name_unique" UNIQUE("name")
);
--> statement-breakpoint
CREATE TABLE IF NOT EXISTS "users" (
	"id" serial PRIMARY KEY NOT NULL,
	"role" "user_role" NOT NULL,
	"unique_code" varchar(36),
	"google_email" varchar(255),
	"nis" varchar(10),
	"username" varchar(50),
	"name" varchar(100) NOT NULL,
	"whatsapp" varchar(15),
	"class_id" serial NOT NULL,
	"password_hash" varchar(255),
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp,
	CONSTRAINT "users_unique_code_unique" UNIQUE("unique_code"),
	CONSTRAINT "users_google_email_unique" UNIQUE("google_email"),
	CONSTRAINT "users_username_unique" UNIQUE("username")
);
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "absences" ADD CONSTRAINT "absences_unique_code_users_unique_code_fk" FOREIGN KEY ("unique_code") REFERENCES "public"."users"("unique_code") ON DELETE restrict ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "users" ADD CONSTRAINT "users_class_id_classes_id_fk" FOREIGN KEY ("class_id") REFERENCES "public"."classes"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
-- Add check constraint for class name format (e.g., "XII IPA 1")
ALTER TABLE "classes" ADD CONSTRAINT "chk_class_name" CHECK (name ~ '^[XVI]{1,2}\s(IPA|IPS)\s[1-3]$');
--> statement-breakpoint
-- Add check constraint for NIS format
ALTER TABLE "users" ADD CONSTRAINT "chk_nis_format" CHECK (nis IS NULL OR nis ~ '^[A-Za-z0-9]{1,10}$');
--> statement-breakpoint
-- Add check constraint for role-specific data
ALTER TABLE "users" ADD CONSTRAINT "chk_role_data" CHECK (
  (role = 'student' AND google_email IS NOT NULL AND unique_code IS NOT NULL AND username IS NULL AND password_hash IS NULL) OR
  (role = 'admin' AND google_email IS NULL AND unique_code IS NULL AND username IS NOT NULL AND password_hash IS NOT NULL AND class_id IS NULL)
);
--> statement-breakpoint
-- Add indexes for better performance
CREATE INDEX IF NOT EXISTS "idx_users_role" ON "users" ("role");
CREATE INDEX IF NOT EXISTS "idx_users_class_id" ON "users" ("class_id");
CREATE INDEX IF NOT EXISTS "idx_users_unique_code" ON "users" ("unique_code");
CREATE INDEX IF NOT EXISTS "idx_absences_unique_code_recorded_at" ON "absences" ("unique_code", "recorded_at");
