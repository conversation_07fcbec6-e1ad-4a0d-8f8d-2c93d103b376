'use client'

import { useState, useEffect, Suspense } from 'react'
import { useRouter, useSearchParams } from 'next/navigation'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { ThemeToggle } from '@/components/theme-toggle'
import { useToast } from '@/components/ui/use-toast'
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert'
import { AlertCircle, AlertTriangle, Clock, Shield } from 'lucide-react'

function VerifyOTPContent() {
  const router = useRouter()
  const searchParams = useSearchParams()
  const { toast } = useToast()

  const [loading, setLoading] = useState(false)
  const [otp, setOtp] = useState('')
  const [error, setError] = useState<{
    message: string
    type: 'rateLimit' | 'otpInvalid' | 'general'
  } | null>(null)
  const [countdown, setCountdown] = useState<number | null>(null)
  const [progress, setProgress] = useState(100)
  const [otpExpiry, setOtpExpiry] = useState(5 * 60) // 5 minutes in seconds

  // Get the WhatsApp number from the URL
  const whatsapp = searchParams.get('whatsapp')

  // If no WhatsApp number is provided, redirect to forgot-password
  useEffect(() => {
    if (!whatsapp) {
      toast({
        title: 'Error',
        description: 'WhatsApp tidak tersedia. Silakan mulai proses dari awal.',
        variant: 'destructive',
      })
      router.push('/student/forgot-password')
    }
  }, [whatsapp, router, toast])

  // OTP expiry countdown
  useEffect(() => {
    if (otpExpiry > 0) {
      const timer = setTimeout(() => {
        setOtpExpiry(otpExpiry - 1)
        setProgress(((otpExpiry - 1) / (5 * 60)) * 100)
      }, 1000)
      return () => clearTimeout(timer)
    } else if (otpExpiry === 0) {
      setError({
        message: 'Kode OTP telah kedaluwarsa. Silakan minta OTP baru.',
        type: 'otpInvalid',
      })
    }
  }, [otpExpiry])

  // Rate limit countdown timer
  useEffect(() => {
    if (countdown && countdown > 0) {
      const timer = setTimeout(() => {
        setCountdown(countdown - 1)
      }, 1000)
      return () => clearTimeout(timer)
    } else if (countdown === 0) {
      setError(null)
      setCountdown(null)
    }
  }, [countdown])

  const formatTime = (seconds: number): string => {
    const mins = Math.floor(seconds / 60)
    const secs = seconds % 60
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
  }

  // Request a new OTP if the current one expires
  const handleRequestNewOTP = async () => {
    if (!whatsapp) return

    setLoading(true)
    try {
      const response = await fetch('/api/auth/password-reset/request', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ whatsapp }),
      })

      const data = await response.json()

      if (response.ok) {
        toast({
          title: 'OTP Baru Dikirim',
          description: 'Silakan periksa WhatsApp Anda untuk kode OTP baru',
        })
        setOtp('')
        setOtpExpiry(5 * 60) // Reset timer
        setProgress(100)
        setError(null)
      } else {
        // Handle rate limit or other errors
        if (
          response.status === 429 ||
          data.error?.toLowerCase().includes('too many') ||
          data.error?.toLowerCase().includes('coba lagi nanti')
        ) {
          setError({
            message: data.error || 'Terlalu banyak permintaan OTP. Silakan tunggu beberapa saat.',
            type: 'rateLimit',
          })
          // Set countdown to 60 minutes (or whatever the actual rate limit period is)
          setCountdown(60 * 60)
        } else {
          setError({
            message: data.error || 'Gagal mengirim OTP baru',
            type: 'general',
          })
        }

        toast({
          title: 'Gagal',
          description: data.error || 'Gagal mengirim OTP baru',
          variant: 'destructive',
        })
      }
    } catch (error) {
      setError({
        message: 'Terjadi kesalahan saat mengirim OTP baru',
        type: 'general',
      })
      toast({
        title: 'Gagal',
        description: 'Terjadi kesalahan saat mengirim OTP baru',
        variant: 'destructive',
      })
    } finally {
      setLoading(false)
    }
  }

  // Verify the OTP
  const handleVerifyOTP = async (e: React.FormEvent) => {
    e.preventDefault()

    if (otp.length !== 6) {
      setError({
        message: 'Kode OTP harus 6 digit',
        type: 'otpInvalid',
      })
      return
    }

    setLoading(true)
    setError(null)

    try {
      // For now, we'll just check if the OTP is valid
      // We don't actually reset the password yet
      const response = await fetch('/api/auth/password-reset/check-otp', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ whatsapp, otp }),
      })

      const data = await response.json()

      if (response.ok) {
        toast({
          title: 'OTP Terverifikasi',
          description: 'OTP valid. Silakan reset password Anda.',
        })

        // Ensure whatsapp is a string before using it in the URL
        if (typeof whatsapp === 'string') {
          router.push(
            `/student/reset-password?method=whatsapp&whatsapp=${encodeURIComponent(whatsapp)}&otp=${otp}`
          )
        } else {
          // Handle case where whatsapp is unexpectedly null, though the useEffect should prevent this
          setError({
            message: 'Nomor WhatsApp tidak valid. Silakan coba lagi dari awal.',
            type: 'general',
          })
          toast({
            title: 'Error',
            description: 'Nomor WhatsApp tidak valid.',
            variant: 'destructive',
          })
        }
      } else {
        // Handle specific error types
        if (response.status === 429 || data.error?.toLowerCase().includes('too many')) {
          setError({
            message: data.error || 'Terlalu banyak percobaan gagal. Silakan minta OTP baru.',
            type: 'rateLimit',
          })
          setCountdown(10 * 60) // 10 minutes
        } else if (
          data.error?.toLowerCase().includes('invalid otp') ||
          data.error?.toLowerCase().includes('otp expired')
        ) {
          setError({
            message:
              data.error || 'OTP tidak valid atau sudah kedaluwarsa. Silakan minta OTP baru.',
            type: 'otpInvalid',
          })
        } else {
          setError({
            message: data.error || 'Terjadi kesalahan, silakan coba lagi',
            type: 'general',
          })
        }

        toast({
          title: 'Verifikasi Gagal',
          description: data.error || 'Terjadi kesalahan, silakan coba lagi',
          variant: 'destructive',
        })
      }
    } catch (error) {
      setError({
        message: 'Terjadi kesalahan, silakan coba lagi',
        type: 'general',
      })
      toast({
        title: 'Gagal',
        description: 'Terjadi kesalahan, silakan coba lagi',
        variant: 'destructive',
      })
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="flex min-h-screen items-center justify-center bg-gradient-to-b from-indigo-50 to-white p-4 dark:from-slate-900 dark:to-slate-800">
      <div className="absolute right-4 top-4">
        <ThemeToggle />
      </div>

      <Card className="w-full max-w-md border-indigo-100 shadow-md dark:border-slate-700">
        <CardHeader className="space-y-1 text-center">
          <CardTitle className="text-2xl font-bold">Verifikasi OTP</CardTitle>
          <CardDescription className="text-center">
            {whatsapp && (
              <span>
                Masukkan kode OTP yang dikirim ke WhatsApp{' '}
                <span className="font-medium">
                  {whatsapp.substring(0, 4)}****{whatsapp.substring(whatsapp.length - 4)}
                </span>
              </span>
            )}
          </CardDescription>
        </CardHeader>
        <CardContent>
          {error && (
            <Alert
              variant={error.type === 'rateLimit' ? 'destructive' : 'default'}
              className="mb-4"
            >
              <AlertCircle className="h-4 w-4" />
              <AlertTitle>
                {error.type === 'rateLimit'
                  ? 'Batas Percobaan Terlampaui'
                  : error.type === 'otpInvalid'
                    ? 'OTP Tidak Valid'
                    : 'Error'}
              </AlertTitle>
              <AlertDescription>
                {error.message}
                {error.type === 'rateLimit' && countdown && (
                  <div className="mt-2 flex items-center gap-1 text-xs">
                    <Clock className="h-3 w-3" /> Silakan coba lagi dalam: {formatTime(countdown)}
                  </div>
                )}
              </AlertDescription>
            </Alert>
          )}

          <form onSubmit={handleVerifyOTP} className="space-y-4">
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <Label htmlFor="otp">Kode OTP</Label>
                <div className="flex items-center gap-1 text-xs text-slate-500">
                  <Clock className="h-3 w-3" />
                  <span>{otpExpiry > 0 ? formatTime(otpExpiry) : 'Kedaluwarsa'}</span>
                </div>
              </div>

              <Input
                id="otp"
                value={otp}
                onChange={e => {
                  // Only allow digits and limit to 6 characters
                  const val = e.target.value.replace(/\D/g, '').substring(0, 6)
                  setOtp(val)
                }}
                placeholder="Masukkan 6 digit kode OTP"
                maxLength={6}
                className="text-center text-lg tracking-widest"
                required
              />

              <div className="relative h-1 w-full overflow-hidden rounded-full bg-slate-200">
                <div
                  className={`absolute left-0 top-0 h-full rounded-full ${
                    progress > 60 ? 'bg-green-500' : progress > 30 ? 'bg-yellow-500' : 'bg-red-500'
                  }`}
                  style={{ width: `${progress}%` }}
                />
              </div>

              <div className="mt-1 flex justify-between">
                <p className="text-xs text-slate-500">Kode OTP berlaku selama 5 menit</p>
                <Button
                  variant="link"
                  size="sm"
                  onClick={handleRequestNewOTP}
                  disabled={loading || countdown !== null}
                  className="h-auto p-0 text-xs text-indigo-600 dark:text-indigo-400"
                  type="button"
                >
                  Kirim OTP Baru
                </Button>
              </div>
            </div>

            <Alert variant="default" className="py-2">
              <Shield className="h-4 w-4" />
              <AlertDescription className="text-xs">
                Masukkan kode 6 digit yang dikirimkan ke WhatsApp Anda. Jangan bagikan kode ini
                dengan siapapun.
              </AlertDescription>
            </Alert>

            <Button
              type="submit"
              className="w-full bg-indigo-600 text-white hover:bg-indigo-700"
              disabled={loading || otp.length !== 6 || countdown !== null || otpExpiry <= 0}
            >
              {loading ? 'Memproses...' : 'Verifikasi OTP'}
            </Button>

            <Button
              variant="outline"
              className="w-full"
              onClick={() => router.push('/student/forgot-password')}
              type="button"
            >
              Kembali
            </Button>
          </form>
        </CardContent>
      </Card>
    </div>
  )
}

export default function VerifyOTP() {
  return (
    <Suspense
      fallback={
        <div className="flex min-h-screen items-center justify-center bg-gradient-to-b from-indigo-50 to-white p-4 dark:from-slate-900 dark:to-slate-800">
          <Card className="w-full max-w-md border-indigo-100 shadow-md dark:border-slate-700">
            <CardHeader className="space-y-1 text-center">
              <CardTitle className="text-2xl font-bold">Memuat...</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex justify-center py-8">
                <div className="h-8 w-8 animate-spin rounded-full border-4 border-indigo-600 border-t-transparent"></div>
              </div>
            </CardContent>
          </Card>
        </div>
      }
    >
      <VerifyOTPContent />
    </Suspense>
  )
}
