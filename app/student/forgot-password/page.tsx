'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { ThemeToggle } from '@/components/theme-toggle'
import { useToast } from '@/components/ui/use-toast'
import { AlertCircle, AlertTriangle, Phone, Info } from 'lucide-react'
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert'
import { formatPhoneNumber } from '@/lib/utils/otp'
import { Progress } from '@/components/ui/progress'

export default function ForgotPassword() {
  const router = useRouter()
  const { toast } = useToast()
  const [loading, setLoading] = useState(false)
  const [whatsapp, setWhatsapp] = useState('')
  const [error, setError] = useState<{ message: string; isRateLimit: boolean } | null>(null)

  const validateWhatsAppFormat = (number: string): boolean => {
    // Check if it's a valid Indonesian number format
    // Should start with 62 and have 10-13 digits total
    return /^62[0-9]{8,13}$/.test(number)
  }

  const formatWhatsAppDisplay = (number: string): string => {
    if (!number || number.length < 3) return number

    // Format as: +62 812-3456-7890
    const withoutPrefix = number.startsWith('62') ? number.substring(2) : number

    // Add dashes for better readability
    if (withoutPrefix.length > 8) {
      return `+62 ${withoutPrefix.substring(0, 3)}-${withoutPrefix.substring(3, 7)}-${withoutPrefix.substring(7)}`
    } else if (withoutPrefix.length > 4) {
      return `+62 ${withoutPrefix.substring(0, 3)}-${withoutPrefix.substring(3)}`
    }

    return `+62 ${withoutPrefix}`
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)
    setError(null)

    // Additional validation before sending
    if (!validateWhatsAppFormat(whatsapp)) {
      setError({
        message: 'Format nomor WhatsApp tidak valid. Gunakan format: 628123456789',
        isRateLimit: false,
      })
      setLoading(false)
      return
    }

    try {
      const formattedWhatsApp = formatPhoneNumber(whatsapp)

      const response = await fetch('/api/auth/password-reset/request', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ whatsapp: formattedWhatsApp }),
      })

      const data = await response.json()

      if (response.ok) {
        toast({
          title: 'OTP dikirim',
          description: 'Silakan periksa WhatsApp Anda untuk kode OTP',
        })

        // Redirect to the verify OTP page instead of reset password
        router.push(`/student/verify-otp?whatsapp=${encodeURIComponent(formattedWhatsApp)}`)
      } else {
        // Check if it's a rate limit error (429 status code or specific message)
        const isRateLimit =
          response.status === 429 ||
          data.error?.includes('rate limit') ||
          data.error?.includes('too many') ||
          data.error?.toLowerCase().includes('coba lagi nanti')

        if (isRateLimit) {
          setError({
            message: data.error || 'Terlalu banyak percobaan. Silakan coba lagi dalam 1 jam.',
            isRateLimit: true,
          })
        } else {
          setError({
            message: data.error || 'Terjadi kesalahan, silakan coba lagi',
            isRateLimit: false,
          })
        }

        toast({
          title: isRateLimit ? 'Batas Percobaan Terlampaui' : 'Gagal',
          description: data.error || 'Terjadi kesalahan, silakan coba lagi',
          variant: 'destructive',
        })
      }
    } catch (error) {
      setError({
        message: 'Terjadi kesalahan, silakan coba lagi',
        isRateLimit: false,
      })

      toast({
        title: 'Gagal',
        description: 'Terjadi kesalahan, silakan coba lagi',
        variant: 'destructive',
      })
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="flex min-h-screen items-center justify-center bg-gradient-to-b from-indigo-50 to-white p-4 dark:from-slate-900 dark:to-slate-800">
      <div className="absolute right-4 top-4">
        <ThemeToggle />
      </div>

      <Card className="w-full max-w-md border-indigo-100 shadow-md dark:border-slate-700">
        <CardHeader className="space-y-1 text-center">
          <CardTitle className="text-2xl font-bold">Lupa Password</CardTitle>
          <div className="text-sm text-slate-500 dark:text-slate-400">
            Masukkan nomor WhatsApp terdaftar untuk reset password
          </div>
        </CardHeader>
        <CardContent>
          {error && (
            <Alert variant={error.isRateLimit ? 'destructive' : 'default'} className="mb-4">
              <AlertCircle className="h-4 w-4" />
              <AlertTitle>{error.isRateLimit ? 'Batas Percobaan Terlampaui' : 'Error'}</AlertTitle>
              <AlertDescription>
                {error.message}
                {error.isRateLimit && (
                  <div className="mt-2 text-xs">
                    Anda telah melebihi batas maksimum 5 permintaan OTP dalam 1 jam. Silakan tunggu
                    beberapa saat sebelum mencoba kembali.
                  </div>
                )}
              </AlertDescription>
            </Alert>
          )}

          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="whatsapp">Nomor WhatsApp</Label>
              <div className="flex items-center gap-2">
                <div className="relative flex-1">
                  <div className="flex">
                    <div className="flex items-center rounded-l-md border border-r-0 border-input bg-slate-100 px-3 py-2 text-sm dark:bg-slate-800">
                      +62
                    </div>
                    <Input
                      id="whatsapp"
                      name="whatsapp"
                      type="tel"
                      placeholder="8123456789"
                      value={whatsapp.startsWith('62') ? whatsapp.substring(2) : whatsapp}
                      onChange={e => {
                        // Remove non-numeric characters
                        const numericValue = e.target.value.replace(/\D/g, '')
                        // Ensure the value starts with "62"
                        const formattedValue = numericValue.startsWith('62')
                          ? numericValue
                          : `62${numericValue}`
                        setWhatsapp(formattedValue)
                      }}
                      className="rounded-l-none"
                      required
                    />
                  </div>
                </div>
              </div>
              <div className="flex items-start justify-between">
                <p className="text-xs text-slate-500 dark:text-slate-400">
                  Format: 628123456789 (tanpa tanda +)
                </p>
                {whatsapp.length > 2 && (
                  <div className="text-xs font-medium text-slate-700 dark:text-slate-300">
                    Format: {formatWhatsAppDisplay(whatsapp)}
                  </div>
                )}
              </div>

              {!validateWhatsAppFormat(whatsapp) && whatsapp.length > 3 && (
                <Alert variant="warning" className="py-2">
                  <Info className="h-4 w-4" />
                  <AlertDescription className="text-xs">
                    Nomor tidak valid. Pastikan format: 628123456789
                  </AlertDescription>
                </Alert>
              )}
            </div>

            <Alert variant="info" className="py-2">
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription className="text-xs">
                Maksimal 5 permintaan reset password dalam 1 jam untuk setiap nomor WhatsApp.
              </AlertDescription>
            </Alert>

            <Button
              type="submit"
              className="w-full bg-indigo-600 text-white hover:bg-indigo-700"
              disabled={loading || error?.isRateLimit || !validateWhatsAppFormat(whatsapp)}
              aria-disabled={loading || error?.isRateLimit || !validateWhatsAppFormat(whatsapp)}
            >
              {loading ? 'Memproses...' : 'Kirim Reset Password'}
            </Button>
            <Button
              variant="outline"
              className="mt-2 w-full"
              onClick={() => router.push('/student')}
              type="button"
            >
              Kembali ke Login
            </Button>
          </form>
        </CardContent>
      </Card>
    </div>
  )
}
