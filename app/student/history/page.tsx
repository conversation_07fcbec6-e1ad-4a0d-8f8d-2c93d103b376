"use client"

import { useState } from "react"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON> } from "@/components/ui/card"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { ThemeToggle } from "@/components/theme-toggle"
import { StudentBottomNav } from "@/components/student-bottom-nav"
import { Calendar, CheckCircle, XCircle } from "lucide-react"

// Mock history data
const mockHistory = [
  {
    date: "29 Apr 2025",
    day: "Selasa",
    zuhur: true,
    zuhurTime: "12:15 WITA",
    asr: false,
    asrTime: null,
    dismissal: false,
    dismissalTime: null,
  },
  {
    date: "28 Apr 2025",
    day: "Senin",
    zuhur: true,
    zuhurTime: "12:10 WITA",
    asr: true,
    asrTime: "15:05 WITA",
    dismissal: true,
    dismissalTime: "16:30 WITA",
  },
  {
    date: "25 Apr 2025",
    day: "Ju<PERSON>",
    zuhur: true,
    zuhurTime: "12:18 WITA",
    asr: true,
    asrTime: "15:12 WITA",
    dismissal: true,
    dismissalTime: "16:35 WITA",
  },
  {
    date: "24 Apr 2025",
    day: "Kamis",
    zuhur: true,
    zuhurTime: "12:05 WITA",
    asr: false,
    asrTime: null,
    dismissal: true,
    dismissalTime: "16:32 WITA",
  },
  {
    date: "23 Apr 2025",
    day: "Rabu",
    zuhur: false,
    zuhurTime: null,
    asr: true,
    asrTime: "15:08 WITA",
    dismissal: true,
    dismissalTime: "16:30 WITA",
  },
]

export default function StudentHistory() {
  const [period, setPeriod] = useState("week")

  return (
    <div className="min-h-screen bg-slate-50 dark:bg-slate-900 pb-16">
      <header className="md:hidden p-4 flex justify-between items-center">
        <h1 className="text-xl font-bold text-slate-800 dark:text-slate-100">Riwayat Absensi</h1>
        <ThemeToggle />
      </header>

      <main className="container mx-auto px-4 max-w-lg">
        <Card className="shadow-md mb-6">
          <CardHeader className="pb-2">
            <CardTitle className="text-lg flex items-center gap-2">
              <Calendar className="h-5 w-5" />
              Riwayat Kehadiran
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="mb-4">
              <Select value={period} onValueChange={setPeriod}>
                <SelectTrigger>
                  <SelectValue placeholder="Pilih periode" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="week">Minggu Ini</SelectItem>
                  <SelectItem value="month">Bulan Ini</SelectItem>
                  <SelectItem value="semester">Semester Ini</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="rounded-md border overflow-hidden">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead className="w-[100px]">Tanggal</TableHead>
                    <TableHead className="text-center">Zuhur</TableHead>
                    <TableHead className="text-center">Asr</TableHead>
                    <TableHead className="text-center">Pulang</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {mockHistory.map((record, index) => (
                    <TableRow key={index} className="hover:bg-indigo-50 dark:hover:bg-slate-800">
                      <TableCell className="font-medium">
                        <div>{record.date}</div>
                        <div className="text-xs text-slate-500">{record.day}</div>
                      </TableCell>
                      <TableCell className="text-center">
                        {record.zuhur ? (
                          <div className="flex flex-col items-center">
                            <CheckCircle className="h-5 w-5 text-green-500 mb-1" />
                            <span className="text-xs text-slate-500">{record.zuhurTime?.split(" ")[0]}</span>
                          </div>
                        ) : (
                          <XCircle className="h-5 w-5 text-slate-400 mx-auto" />
                        )}
                      </TableCell>
                      <TableCell className="text-center">
                        {record.asr ? (
                          <div className="flex flex-col items-center">
                            <CheckCircle className="h-5 w-5 text-green-500 mb-1" />
                            <span className="text-xs text-slate-500">{record.asrTime?.split(" ")[0]}</span>
                          </div>
                        ) : (
                          <XCircle className="h-5 w-5 text-slate-400 mx-auto" />
                        )}
                      </TableCell>
                      <TableCell className="text-center">
                        {record.dismissal ? (
                          <div className="flex flex-col items-center">
                            <CheckCircle className="h-5 w-5 text-green-500 mb-1" />
                            <span className="text-xs text-slate-500">{record.dismissalTime?.split(" ")[0]}</span>
                          </div>
                        ) : (
                          <XCircle className="h-5 w-5 text-slate-400 mx-auto" />
                        )}
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          </CardContent>
        </Card>

        <Card className="shadow-md">
          <CardHeader className="pb-2">
            <CardTitle className="text-lg">Ringkasan</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-3 gap-4">
              <div className="bg-indigo-50 dark:bg-indigo-900/30 p-3 rounded-lg text-center">
                <div className="text-2xl font-bold text-indigo-600 dark:text-indigo-400">80%</div>
                <div className="text-xs text-slate-500 dark:text-slate-400">Zuhur</div>
              </div>
              <div className="bg-indigo-50 dark:bg-indigo-900/30 p-3 rounded-lg text-center">
                <div className="text-2xl font-bold text-indigo-600 dark:text-indigo-400">60%</div>
                <div className="text-xs text-slate-500 dark:text-slate-400">Asr</div>
              </div>
              <div className="bg-indigo-50 dark:bg-indigo-900/30 p-3 rounded-lg text-center">
                <div className="text-2xl font-bold text-indigo-600 dark:text-indigo-400">80%</div>
                <div className="text-xs text-slate-500 dark:text-slate-400">Pulang</div>
              </div>
            </div>
          </CardContent>
        </Card>
      </main>

      {/* <StudentBottomNav activeTab="history" /> */}
    </div>
  )
}
