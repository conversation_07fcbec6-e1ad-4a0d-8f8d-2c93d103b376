'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { useToast } from '@/components/ui/use-toast'
import { AlertTriangle, Phone } from 'lucide-react'

export default function WhatsAppOTP() {
  const router = useRouter()
  const { toast } = useToast()
  const [step, setStep] = useState<'phone' | 'otp'>('phone')
  const [loading, setLoading] = useState(false)
  const [checking, setChecking] = useState(false)
  const [whatsapp, setWhatsapp] = useState('')
  const [otp, setOtp] = useState('')
  const [error, setError] = useState('')
  const [debounceTimeout, setDebounceTimeout] = useState<NodeJS.Timeout | null>(null)
  const [isWhatsappAvailable, setIsWhatsappAvailable] = useState<boolean | null>(null)

  // Reset availability check when WhatsApp number changes
  useEffect(() => {
    setIsWhatsappAvailable(null)
    setError('')

    // Clear previous timeout
    if (debounceTimeout) {
      clearTimeout(debounceTimeout)
    }

    // Only check if the whatsapp number is in a valid format
    if (whatsapp && whatsapp.length >= 10) {
      setChecking(true)

      // Create new timeout
      const timeout = setTimeout(() => {
        checkWhatsappAvailability(whatsapp)
      }, 500) // 500ms debounce

      setDebounceTimeout(timeout)
    }

    // Cleanup on unmount
    return () => {
      if (debounceTimeout) {
        clearTimeout(debounceTimeout)
      }
    }
  }, [whatsapp])

  const checkWhatsappAvailability = async (number: string) => {
    if (!number) return

    try {
      // Format the number correctly (ensuring it starts with 62)
      let formattedNumber = number
      if (!formattedNumber.startsWith('+') && !formattedNumber.startsWith('62')) {
        formattedNumber = `62${formattedNumber}`
      } else if (formattedNumber.startsWith('+')) {
        formattedNumber = formattedNumber.substring(1)
      }

      const response = await fetch('/api/student/whatsapp/check', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ whatsapp: formattedNumber }),
      })

      const data = await response.json()

      setIsWhatsappAvailable(data.available)

      if (!data.available) {
        setError('Nomor WhatsApp ini sudah terdaftar pada akun lain')
      }
    } catch (error) {
      console.error('Error checking WhatsApp availability:', error)
    } finally {
      setChecking(false)
    }
  }

  const handleSendOTP = async (e: React.FormEvent) => {
    e.preventDefault()

    // Final check before sending OTP
    if (isWhatsappAvailable === false) {
      setError('Nomor WhatsApp ini sudah terdaftar pada akun lain')
      toast({
        title: 'Error',
        description: 'Nomor WhatsApp ini sudah terdaftar pada akun lain',
        variant: 'destructive',
      })
      return
    }

    setLoading(true)
    setError('')

    try {
      // Format the number correctly (ensuring it starts with 62)
      let formattedWhatsapp = whatsapp
      if (!formattedWhatsapp.startsWith('+') && !formattedWhatsapp.startsWith('62')) {
        formattedWhatsapp = `62${formattedWhatsapp}`
      } else if (formattedWhatsapp.startsWith('+')) {
        formattedWhatsapp = formattedWhatsapp.substring(1)
      }

      // Call the API to send OTP
      const response = await fetch('/api/student/whatsapp/send-otp', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ whatsapp: formattedWhatsapp }),
      })

      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.error || data.details || 'Failed to send OTP')
      }

      // Success
      setStep('otp')
      toast({
        title: 'OTP dikirim',
        description: 'Silakan periksa WhatsApp Anda untuk kode OTP',
      })
    } catch (error) {
      console.error('Error sending OTP:', error)
      setError(error instanceof Error ? error.message : 'Failed to send OTP')
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'Failed to send OTP',
        variant: 'destructive',
      })
    } finally {
      setLoading(false)
    }
  }

  const handleVerifyOTP = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)
    setError('')

    try {
      // Format the number correctly (ensuring it starts with 62)
      let formattedWhatsapp = whatsapp
      if (!formattedWhatsapp.startsWith('+') && !formattedWhatsapp.startsWith('62')) {
        formattedWhatsapp = `62${formattedWhatsapp}`
      } else if (formattedWhatsapp.startsWith('+')) {
        formattedWhatsapp = formattedWhatsapp.substring(1)
      }

      // Call the API to verify OTP
      const response = await fetch('/api/student/whatsapp/verify-otp', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ whatsapp: formattedWhatsapp, otp }),
      })

      const data = await response.json()

      if (!response.ok) {
        // Special handling for 409 Conflict status (already registered)
        if (response.status === 409) {
          throw new Error('Nomor WhatsApp ini sudah terdaftar pada akun lain')
        }
        throw new Error(data.error || data.details || 'Failed to verify OTP')
      }

      // Success
      toast({
        title: 'WhatsApp terverifikasi',
        description: 'Nomor WhatsApp Anda telah diverifikasi',
      })

      // Redirect to home page
      router.push('/student/home')
    } catch (error) {
      console.error('Error verifying OTP:', error)
      setError(error instanceof Error ? error.message : 'Failed to verify OTP')
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'Failed to verify OTP',
        variant: 'destructive',
      })
    } finally {
      setLoading(false)
    }
  }

  const formatWhatsappDisplay = (number: string) => {
    // Convert 628123456789 to +62 8123456789 for display
    if (number.startsWith('62')) {
      return `+62 ${number.substring(2)}`
    } else if (number.startsWith('+62')) {
      return `+62 ${number.substring(3)}`
    } else if (number.startsWith('0')) {
      return `+62 ${number.substring(1)}`
    } else {
      return `+62 ${number}`
    }
  }

  return (
    <div className="container mx-auto max-w-md py-8">
      <Card className="border-indigo-100 shadow-md dark:border-slate-700">
        <CardHeader className="space-y-1 text-center">
          <CardTitle className="text-2xl font-bold">
            {step === 'phone' ? 'Verifikasi WhatsApp' : 'Masukkan OTP'}
          </CardTitle>
        </CardHeader>
        <CardContent>
          {error && (
            <div className="mb-4 flex items-start rounded-md border border-red-200 bg-red-50 p-3 text-sm text-red-600">
              <AlertTriangle className="mr-2 mt-0.5 h-4 w-4 flex-shrink-0" />
              <span>{error}</span>
            </div>
          )}

          {step === 'phone' ? (
            <form onSubmit={handleSendOTP} className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="whatsapp">Nomor WhatsApp</Label>
                <div className="relative flex">
                  <div className="flex items-center justify-center rounded-l-md border border-r-0 border-input bg-slate-100 px-3 py-2 text-sm dark:bg-slate-800">
                    +62
                  </div>
                  <Input
                    id="whatsapp"
                    placeholder="812xxxxxxx"
                    className="rounded-l-none"
                    value={whatsapp.startsWith('62') ? whatsapp.substring(2) : whatsapp}
                    onChange={e => {
                      // Remove non-numeric characters
                      const value = e.target.value.replace(/\D/g, '')
                      // Ensure it starts with 62 for API but don't show the prefix in UI
                      setWhatsapp(value.startsWith('62') ? value : `62${value}`)
                    }}
                    required
                  />
                  {checking && (
                    <div className="absolute right-3 top-3">
                      <div className="h-4 w-4 animate-spin rounded-full border-2 border-slate-500 border-t-transparent"></div>
                    </div>
                  )}
                </div>
                {isWhatsappAvailable === false && (
                  <p className="text-xs text-red-500">
                    Nomor WhatsApp ini sudah terdaftar pada akun lain
                  </p>
                )}
                <p className="text-xs text-slate-500 dark:text-slate-400">
                  Masukkan nomor WhatsApp aktif untuk menerima kode OTP (tanpa angka 0 di depan)
                </p>
              </div>
              <Button
                type="submit"
                className="w-full bg-indigo-600 text-white hover:bg-indigo-700"
                disabled={loading || checking || isWhatsappAvailable === false}
              >
                {loading ? 'Memproses...' : checking ? 'Memeriksa...' : 'Kirim OTP'}
              </Button>
              <Button
                variant="outline"
                className="w-full"
                onClick={() => router.back()}
                type="button"
              >
                Kembali
              </Button>
            </form>
          ) : (
            <form onSubmit={handleVerifyOTP} className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="otp">Kode OTP</Label>
                <Input
                  id="otp"
                  placeholder="123456"
                  value={otp}
                  onChange={e => setOtp(e.target.value.replace(/\D/g, '').slice(0, 6))}
                  required
                  maxLength={6}
                  className="text-center text-lg tracking-widest"
                />
                <p className="text-xs text-slate-500 dark:text-slate-400">
                  Masukkan kode 6 digit yang dikirim ke WhatsApp {formatWhatsappDisplay(whatsapp)}
                </p>
              </div>
              <Button
                type="submit"
                className="w-full bg-indigo-600 text-white hover:bg-indigo-700"
                disabled={loading || otp.length < 6}
              >
                {loading ? 'Memproses...' : 'Verifikasi OTP'}
              </Button>
              <Button
                variant="outline"
                className="w-full"
                onClick={() => setStep('phone')}
                type="button"
              >
                Kembali
              </Button>
            </form>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
