'use client'

import type React from 'react'
import { Suspense } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { ThemeToggle } from '@/components/theme-toggle'
import { StudentLoginForm } from '@/components/student-login-form'

export default function StudentLogin() {
  return (
    <div className="flex min-h-screen items-center justify-center bg-gradient-to-b from-indigo-50 to-white p-4 dark:from-slate-900 dark:to-slate-800">
      <div className="absolute right-4 top-4">
        <ThemeToggle />
      </div>

      <Card className="w-full max-w-md border-indigo-100 shadow-md dark:border-slate-700">
        <CardHeader className="space-y-1 text-center">
          <CardTitle className="text-2xl font-bold">Masuk ke Akun <PERSON>wa</CardTitle>
          <div className="text-sm text-slate-500 dark:text-slate-400">
            Login dengan Username & Password
          </div>
        </CardHeader>
        <CardContent className="space-y-4">
          <StudentLoginForm />
          <div className="mt-2 text-center">
            <a
              href="/student/forgot-password"
              className="text-sm font-medium text-indigo-600 underline hover:text-indigo-800 dark:text-indigo-400 dark:hover:text-indigo-200"
            >
              Lupa password?
            </a>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
