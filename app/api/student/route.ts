import { NextRequest, NextResponse } from 'next/server'
import { StudentRepository } from '@/lib/data/repositories/student'
import { getRedisCache } from '@/lib/data/cache/redis'
import { authenticateAdmin, handleAuthError } from '@/lib/middleware/auth'

// Initialize dependencies
const cache = getRedisCache()
const studentRepo = new StudentRepository(cache)

/**
 * GET /api/student
 * Get student information by unique code
 */
export async function GET(req: NextRequest) {
  try {
    // Authenticate the admin
    try {
      await authenticateAdmin(req)
    } catch (authError) {
      return handleAuthError(authError)
    }

    // Get the uniqueCode from query parameters
    const searchParams = req.nextUrl.searchParams
    let uniqueCode = searchParams.get('uniqueCode')

    if (!uniqueCode) {
      return NextResponse.json({ error: 'Missing uniqueCode parameter' }, { status: 400 })
    }

    // Normalize the uniqueCode format if needed
    if (!/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(uniqueCode)) {
      console.warn(`Invalid UUID format in student GET request: ${uniqueCode}`)

      // Try to normalize it - check for UUID without hyphens first
      if (/^[0-9a-f]{32}$/i.test(uniqueCode)) {
        uniqueCode = `${uniqueCode.substring(0, 8)}-${uniqueCode.substring(8, 12)}-${uniqueCode.substring(12, 16)}-${uniqueCode.substring(16, 20)}-${uniqueCode.substring(20)}`
        console.log(`Normalized UUID without hyphens to: ${uniqueCode.substring(0, 8)}...`)
      } else {
        // Try to extract a UUID if embedded in the string
        const match = uniqueCode.match(
          /([0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12})/i
        )
        if (match && match[1]) {
          uniqueCode = match[1]
          console.log(`Extracted UUID from string: ${uniqueCode.substring(0, 8)}...`)
        } else {
          return NextResponse.json(
            {
              error: 'Invalid uniqueCode format',
              message: 'The uniqueCode must be a valid UUID',
            },
            { status: 400 }
          )
        }
      }
    }

    // Get student information
    const student = await studentRepo.findByUniqueCode(uniqueCode)

    if (!student) {
      return NextResponse.json({ error: 'Student not found' }, { status: 404 })
    }

    // Return the student information
    return NextResponse.json({
      id: student.id,
      uniqueCode: student.uniqueCode,
      googleEmail: student.googleEmail,
      name: student.name,
      nis: student.nis,
      whatsapp: student.whatsapp,
      className: student.className,
    })
  } catch (error) {
    console.error('Error getting student information:', error)

    // More specific error handling
    if (error instanceof Error) {
      return NextResponse.json(
        {
          error: 'Failed to get student information',
          message: error.message || 'An unexpected error occurred',
        },
        { status: 500 }
      )
    }

    return NextResponse.json({ error: 'Failed to get student information' }, { status: 500 })
  }
}
