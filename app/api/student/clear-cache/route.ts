import { NextRequest, NextResponse } from 'next/server'
import { getRedisCache } from '@/lib/data/cache/redis'
import { authenticateStudent, handleAuthError } from '@/lib/middleware/auth'
import { eq } from 'drizzle-orm'
import { db } from '@/lib/data/drizzle/db'
import { users } from '@/lib/data/drizzle/schema'

/**
 * POST /api/student/clear-cache
 * Clears Redis cache for a student profile
 * This is a maintenance utility endpoint for administrators or developers
 * to force cache clearing when needed
 */
export async function POST(req: NextRequest) {
  try {
    // Authenticate the student
    const studentId = await authenticateStudent(req)
    

    // Get student data to have all possible keys
    const [student] = await db.select().from(users).where(eq(users.id, studentId)).limit(1)

    if (!student) {
      return NextResponse.json({ error: 'Student not found' }, { status: 404 })
    }

    // Get the cache service
    const cache = getRedisCache()

    // Build a list of all possible cache keys for this student
    const keysToInvalidate = [
      `student:id:${studentId}`,
      `student:profile:${student.uniqueCode}`,
      `student:username:${student.username}`,
      // WhatsApp related cache
      ...(student.whatsapp ? [`student:whatsapp:${student.whatsapp}`] : []),
      // Username related cache
      `student:username:${student.username}`,
      // Google email related cache
      ...(student.googleEmail ? [`student:email:${student.googleEmail}`] : []),
      // Clear any global caches that might include this student
      'users:all',
      `student:count:all`,
    ]

    

    // Delete all cache keys
    const deletePromises = keysToInvalidate.map(key => cache.del(key))
    await Promise.all(deletePromises)

    

    return NextResponse.json({
      success: true,
      message: 'Cache cleared successfully',
      clearedKeys: keysToInvalidate,
    })
  } catch (error) {
    console.error('Error clearing Redis cache:', error)

    // Handle authentication errors
    if (
      error instanceof Error &&
      (error.message.includes('Authentication') ||
        error.message.includes('token') ||
        error.message.includes('Access denied'))
    ) {
      return handleAuthError(error)
    }

    return NextResponse.json(
      {
        success: false,
        error: 'Failed to clear cache',
        details: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    )
  }
}
