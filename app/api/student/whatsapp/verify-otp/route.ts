import { NextRequest, NextResponse } from 'next/server'
import { AuthUseCases } from '@/lib/domain/usecases/auth'
import { StudentRepository } from '@/lib/data/repositories/student'
import { AdminRepository } from '@/lib/data/repositories/admin'
import { getRedisCache } from '@/lib/data/cache/redis'
import { serverConfig } from '@/lib/config'
import { verifyToken } from '@/lib/utils/auth'
import { z } from 'zod'
import { ValidationError, NotFoundError, ConflictError } from '@/lib/domain/errors'

// Initialize dependencies
const cache = getRedisCache()
const studentRepo = new StudentRepository(cache)
const adminRepo = new AdminRepository(cache)
const authUseCases = new AuthUseCases(
  studentRepo,
  adminRepo,
  cache,
  serverConfig.auth.jwtSecret || ''
)

// Validation schema for OTP verification
const otpVerificationSchema = z.object({
  whatsapp: z
    .string()
    .regex(
      /^(\+?)[0-9]{10,15}$/,
      'Invalid WhatsApp number format. Please enter a valid number (e.g. 628123456789).'
    )
    .transform(val => (val.startsWith('+') ? val.substring(1) : val)),
  otp: z
    .string()
    .length(6, 'OTP must be exactly 6 digits')
    .regex(/^\d+$/, 'OTP must contain only digits'),
})

/**
 * POST /api/student/whatsapp/verify-otp
 * Verify OTP and update WhatsApp number
 */
export async function POST(req: NextRequest) {
  try {
    // Authenticate the request
    const authToken = req.cookies.get('student_auth_token')?.value

    if (!authToken) {
      
      return NextResponse.json(
        { error: 'Authentication required', details: 'You must be logged in to verify an OTP' },
        { status: 401 }
      )
    }

    try {
      // Verify the token
      const decoded = verifyToken(authToken, serverConfig.auth.jwtSecret || '')

      if (!decoded || !decoded.id || decoded.role !== 'student') {
        return NextResponse.json(
          { error: 'Invalid or expired session', details: 'Please log in again' },
          { status: 401 }
        )
      }

      // Parse and validate the request body
      const body = await req.json()

      // Validate input
      const result = otpVerificationSchema.safeParse(body)
      if (!result.success) {
        const formattedErrors = result.error.format()

        // Create a user-friendly error message
        let errorDetails = 'Please check your input'

        if (formattedErrors.whatsapp?._errors?.[0]) {
          errorDetails = formattedErrors.whatsapp._errors[0]
        } else if (formattedErrors.otp?._errors?.[0]) {
          errorDetails = formattedErrors.otp._errors[0]
        }

        return NextResponse.json({ error: 'Invalid input', details: errorDetails }, { status: 400 })
      }

      const { whatsapp, otp } = result.data

      // Verify OTP and update WhatsApp number
      await authUseCases.verifyWhatsAppOtp(whatsapp, otp, decoded.id)

      // Return success response
      return NextResponse.json({
        success: true,
        message: 'WhatsApp number verified',
        details: 'Your WhatsApp number has been verified successfully',
      })
    } catch (error) {
      console.error('OTP verification failed:', error)

      // Handle different types of errors
      if (error instanceof ValidationError) {
        return NextResponse.json(
          { error: error.message, details: 'Please try again or request a new OTP' },
          { status: 400 }
        )
      } else if (error instanceof NotFoundError) {
        return NextResponse.json(
          { error: error.message, details: 'Please check your account information' },
          { status: 404 }
        )
      } else if (error instanceof ConflictError) {
        return NextResponse.json(
          {
            error: 'WhatsApp number already registered',
            details: 'This WhatsApp number is already registered to another account',
          },
          { status: 409 }
        )
      }

      // Check for database constraint errors
      const errorMessage = error instanceof Error ? error.message : String(error)
      if (
        errorMessage.includes('unique constraint') ||
        errorMessage.includes('unique_student_whatsapp') ||
        errorMessage.includes('duplicate key value')
      ) {
        return NextResponse.json(
          {
            error: 'WhatsApp number already registered',
            details: 'This WhatsApp number is already registered to another account',
          },
          { status: 409 }
        )
      }

      return NextResponse.json(
        { error: 'Authentication failed', details: 'Please log in again' },
        { status: 401 }
      )
    }
  } catch (error) {
    console.error('Verify OTP error:', error)

    // Provide a user-friendly error message
    let statusCode = 500
    let errorMessage = 'Failed to verify OTP'
    let details = 'Please try again later'

    if (error instanceof Error) {
      const errorMsg = error.message.toLowerCase()

      if (errorMsg.includes('invalid otp')) {
        errorMessage = 'Invalid verification code'
      } else if (errorMsg.includes('expired')) {
        details = 'Your OTP has expired. Please request a new one.'
      } else if (errorMsg.includes('too many failed attempts')) {
        statusCode = 429
        errorMessage = 'Too many attempts'
        details = 'Please request a new OTP after some time'
      } else if (
        errorMsg.includes('unique constraint') ||
        errorMsg.includes('unique_student_whatsapp') ||
        errorMsg.includes('duplicate key value')
      ) {
        statusCode = 409
        errorMessage = 'WhatsApp number already registered'
        details = 'This WhatsApp number is already registered to another account'
      }
    }

    return NextResponse.json({ error: errorMessage, details }, { status: statusCode })
  }
}
