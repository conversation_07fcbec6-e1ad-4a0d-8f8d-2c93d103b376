import { NextRequest, NextResponse } from 'next/server'
import { StudentRepository } from '@/lib/data/repositories/student'
import { getRedisCache } from '@/lib/data/cache/redis'
import { verifyToken } from '@/lib/utils/auth'
import { serverConfig } from '@/lib/config'
import { z } from 'zod'
import { formatPhoneNumber } from '@/lib/utils/otp'

// Initialize dependencies
const cache = getRedisCache()
const studentRepo = new StudentRepository(cache)

// Validation schema for WhatsApp number
const whatsappSchema = z.object({
  whatsapp: z
    .string()
    .regex(
      /^(\+?)[0-9]{10,15}$/,
      'Invalid WhatsApp number format. Please enter a valid number (e.g. 628123456789).'
    )
    .transform(val => (val.startsWith('+') ? val.substring(1) : val)),
})

/**
 * POST /api/student/whatsapp/check
 * Check if a WhatsApp number is already registered
 */
export async function POST(req: NextRequest) {
  try {
    // Authenticate the request
    const authToken = req.cookies.get('student_auth_token')?.value

    if (!authToken) {
      
      return NextResponse.json(
        { error: 'Authentication required', details: 'You must be logged in to check WhatsApp' },
        { status: 401 }
      )
    }

    try {
      // Verify the token
      const decoded = verifyToken(authToken, serverConfig.auth.jwtSecret || '')

      if (!decoded || !decoded.id || decoded.role !== 'student') {
        return NextResponse.json(
          { error: 'Invalid or expired session', details: 'Please log in again' },
          { status: 401 }
        )
      }

      // Parse and validate the request body
      const body = await req.json()

      // Validate input
      const result = whatsappSchema.safeParse(body)
      if (!result.success) {
        const formattedErrors = result.error.format()
        return NextResponse.json(
          {
            error: 'Invalid WhatsApp number',
            details:
              formattedErrors.whatsapp?._errors?.[0] ||
              'Please enter a valid phone number (e.g. 628123456789)',
          },
          { status: 400 }
        )
      }

      const { whatsapp } = result.data
      const normalizedWhatsapp = formatPhoneNumber(whatsapp)

      // Check if WhatsApp number is already registered
      const existingStudent = await studentRepo.findByWhatsApp(normalizedWhatsapp)

      // If the number belongs to this student, it's not a duplicate
      const isAvailable = !existingStudent || existingStudent.id === decoded.id

      // Return availability status
      return NextResponse.json({
        available: isAvailable,
        message: isAvailable
          ? 'WhatsApp number is available'
          : 'WhatsApp number is already registered to another account',
      })
    } catch (error) {
      console.error('WhatsApp check failed:', error)
      return NextResponse.json(
        { error: 'Failed to check WhatsApp number', details: 'Please try again later' },
        { status: 500 }
      )
    }
  } catch (error) {
    console.error('WhatsApp check error:', error)
    return NextResponse.json(
      { error: 'Server error', details: 'Please try again later' },
      { status: 500 }
    )
  }
}
