import { NextRequest, NextResponse } from 'next/server'
import { AuthUseCases } from '@/lib/domain/usecases/auth'
import { StudentRepository } from '@/lib/data/repositories/student'
import { AdminRepository } from '@/lib/data/repositories/admin'
import { getRedisCache } from '@/lib/data/cache/redis'
import { serverConfig } from '@/lib/config'
import { verifyToken } from '@/lib/utils/auth'
import { z } from 'zod'
import { ValidationError } from '@/lib/domain/errors'

// Initialize dependencies
const cache = getRedisCache()
const studentRepo = new StudentRepository(cache)
const adminRepo = new AdminRepository(cache)
const authUseCases = new AuthUseCases(
  studentRepo,
  adminRepo,
  cache,
  serverConfig.auth.jwtSecret || ''
)

// Validation schema for WhatsApp number
const whatsappSchema = z.object({
  whatsapp: z
    .string()
    .regex(
      /^(\+?)[0-9]{10,15}$/,
      'Invalid WhatsApp number format. Please enter a valid number (e.g. 628123456789).'
    )
    .transform(val => (val.startsWith('+') ? val.substring(1) : val)),
})

/**
 * POST /api/student/whatsapp/send-otp
 * Send OTP to WhatsApp number
 */
export async function POST(req: NextRequest) {
  try {
    // Authenticate the request
    const authToken = req.cookies.get('student_auth_token')?.value

    if (!authToken) {
      
      return NextResponse.json(
        { error: 'Authentication required', details: 'You must be logged in to request an OTP' },
        { status: 401 }
      )
    }

    try {
      // Verify the token
      const decoded = verifyToken(authToken, serverConfig.auth.jwtSecret || '')

      if (!decoded || !decoded.id || decoded.role !== 'student') {
        return NextResponse.json(
          { error: 'Invalid or expired session', details: 'Please log in again' },
          { status: 401 }
        )
      }

      // Parse and validate the request body
      const body = await req.json()

      // Validate input
      const result = whatsappSchema.safeParse(body)
      if (!result.success) {
        const formattedErrors = result.error.format()
        return NextResponse.json(
          {
            error: 'Invalid WhatsApp number',
            details:
              formattedErrors.whatsapp?._errors?.[0] ||
              'Please enter a valid phone number (e.g. 628123456789)',
          },
          { status: 400 }
        )
      }

      const { whatsapp } = result.data

      // Send OTP to WhatsApp number
      await authUseCases.sendWhatsAppOtp(whatsapp, decoded.id)

      // Return success response
      return NextResponse.json({
        success: true,
        message: 'OTP sent to WhatsApp',
        details: 'Please check your WhatsApp for the verification code',
      })
    } catch (error) {
      console.error('Token verification or OTP sending failed:', error)

      // Handle specific errors
      if (error instanceof ValidationError) {
        return NextResponse.json(
          { error: error.message, details: 'Please try again later' },
          { status: 429 }
        )
      }

      return NextResponse.json(
        { error: 'Authentication failed', details: 'Please log in again' },
        { status: 401 }
      )
    }
  } catch (error) {
    console.error('Send OTP error:', error)

    // Provide a user-friendly error message
    let statusCode = 500
    let errorMessage = 'Failed to send OTP'
    let details = 'Please try again later'

    if (error instanceof Error) {
      // Custom handling for specific error types
      if (error.message.includes('rate limit')) {
        statusCode = 429
        errorMessage = 'Too many requests'
        details = 'Please wait before requesting another OTP'
      } else if (error.message.includes('WhatsApp service')) {
        statusCode = 503
        errorMessage = 'WhatsApp service unavailable'
        details = 'Our WhatsApp service is temporarily unavailable. Please try again later.'
      }
    }

    return NextResponse.json({ error: errorMessage, details }, { status: statusCode })
  }
}
