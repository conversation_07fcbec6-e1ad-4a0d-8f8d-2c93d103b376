import { NextRequest, NextResponse } from 'next/server'
import { StudentRepository } from '@/lib/data/repositories/student'
import { UserUseCases } from '@/lib/domain/usecases/user'
import { ClassRepository } from '@/lib/data/repositories/class'
import { getRedisCache } from '@/lib/data/cache/redis'
import { authenticateStudent, handleAuthError } from '@/lib/middleware/auth'
import { z } from 'zod'

// Initialize dependencies
const cache = getRedisCache()
const studentRepo = new StudentRepository(cache)
const classRepo = new ClassRepository()
const userUseCases = new UserUseCases(studentRepo, null as any, classRepo, cache)

// Validation schema for profile update
const updateProfileSchema = z.object({
  nis: z.string().nullable().optional(),
  whatsapp: z.string().nullable().optional(),
  googleEmail: z.string().email('Invalid email format').nullable().optional(),
})

/**
 * GET /api/student/profile
 * Returns the current student's profile
 */
export async function GET(req: NextRequest) {
  try {
    // First try authenticating with JWT token
    const authToken = req.cookies.get('student_auth_token')?.value
    let studentId: number | null = null

    if (authToken) {
      try {
        studentId = await authenticateStudent(req)
      } catch (error) {}
    } else {
    }

    // If JWT authentication failed, return error
    if (!studentId) {
      console.error('No valid session found - user not authenticated')
      return NextResponse.json({ error: 'Authentication required' }, { status: 401 })
    }

    // Get the student from the repository

    const student = await studentRepo.findById(studentId)

    if (!student) {
      console.error(`Student with ID ${studentId} not found in database`)
      return NextResponse.json({ error: 'Student not found' }, { status: 404 })
    }

    // Return the student's profile
    return NextResponse.json({
      student: {
        id: student.id,
        uniqueCode: student.uniqueCode,
        googleEmail: student.googleEmail,
        name: student.name,
        nis: student.nis,
        whatsapp: student.whatsapp,
        className: student.className,
      },
    })
  } catch (error) {
    // Handle authentication errors
    if (
      error instanceof Error &&
      (error.message.includes('Authentication') ||
        error.message.includes('token') ||
        error.message.includes('Access denied'))
    ) {
      console.error('Authentication error in student profile:', error.message)
      return handleAuthError(error)
    }

    console.error('Error getting student profile:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

/**
 * PATCH /api/student/profile
 * Update the current student's profile
 */
export async function PATCH(req: NextRequest) {
  try {
    // Authenticate the student
    const studentId = await authenticateStudent(req)

    // Parse and validate the request body
    const body = await req.json()

    const validatedData = updateProfileSchema.parse(body)

    try {
      // Update the student's profile
      const student = await userUseCases.updateStudent(studentId, validatedData)

      // Return the updated student's profile
      return NextResponse.json({
        student: {
          id: student.id,
          uniqueCode: student.uniqueCode,
          googleEmail: student.googleEmail,
          name: student.name,
          nis: student.nis,
          whatsapp: student.whatsapp,
          className: student.className,
        },
      })
    } catch (updateError) {
      console.error('Error in updateStudent:', updateError)
      return NextResponse.json(
        {
          error: 'Failed to update profile',
          details: updateError instanceof Error ? updateError.message : 'Unknown error',
        },
        { status: 500 }
      )
    }
  } catch (error) {
    // Handle validation errors
    if (error instanceof z.ZodError) {
      return NextResponse.json({ error: 'Invalid input', details: error.errors }, { status: 400 })
    }

    // Handle authentication errors
    if (
      error instanceof Error &&
      (error.message.includes('Authentication') ||
        error.message.includes('token') ||
        error.message.includes('Access denied'))
    ) {
      return handleAuthError(error)
    }

    console.error('Error updating student profile:', error)
    return NextResponse.json(
      {
        error: 'Failed to update profile',
        details: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    )
  }
}
