import { NextRequest, NextResponse } from 'next/server'
import { authenticateStudent, handleAuthError } from '@/lib/middleware/auth'

/**
 * GET /api/student/check-auth
 * Checks if the student is authenticated
 */
export async function GET(req: NextRequest) {
  try {
    // This will throw if not authenticated
    const studentId = await authenticateStudent(req)

    // If we get here, the student is authenticated
    return NextResponse.json({
      authenticated: true,
      studentId,
    })
  } catch (error) {
    // Return an auth error response
    return handleAuthError(error)
  }
}
