import { NextRequest, NextResponse } from 'next/server'
import { AbsenceUseCases } from '@/lib/domain/usecases/absence'
import { AbsenceRepository } from '@/lib/data/repositories/absence'
import { StudentRepository } from '@/lib/data/repositories/student'
import { getRedisCache } from '@/lib/data/cache/redis'
import { AttendanceType } from '@/lib/domain/entities/absence'
import { authenticateAdmin, handleAuthError } from '@/lib/middleware/auth'
import { z } from 'zod'

// Initialize dependencies
const cache = getRedisCache()
const absenceRepo = new AbsenceRepository()
const studentRepo = new StudentRepository(cache)
const absenceUseCases = new AbsenceUseCases(absenceRepo, studentRepo, cache)

// Validation schema for deleting absence
const deleteAbsenceSchema = z.object({
  uniqueCode: z.string().uuid(),
  type: z.enum(['Zuhr', 'Asr', 'Pulang']),
  date: z.string().regex(/^\d{4}-\d{2}-\d{2}$/, 'Date must be in YYYY-MM-DD format'),
})

/**
 * DELETE /api/absence/delete
 * Delete an attendance record
 */
export async function DELETE(req: NextRequest) {
  try {
    // Authenticate the admin
    try {
      await authenticateAdmin(req)
    } catch (authError) {
      return handleAuthError(authError)
    }

    // Parse and validate the request body
    const body = await req.json()
    const { uniqueCode, type, date } = deleteAbsenceSchema.parse(body)

    // Convert date string to Date object
    const dateObj = new Date(date)

    // Delete the attendance record
    const success = await absenceUseCases.deleteAttendance(
      uniqueCode,
      type as AttendanceType,
      dateObj
    )

    if (!success) {
      return NextResponse.json(
        { error: 'Attendance record not found or could not be deleted' },
        { status: 404 }
      )
    }

    // After successful deletion, force a refresh of the materialized view
    // This will help ensure reports are up-to-date
    try {
      await absenceUseCases.refreshAttendanceSummary()
      
    } catch (refreshError) {
      console.error('Error refreshing materialized view:', refreshError)
      // Continue execution even if this fails
    }

    // Return success response
    return NextResponse.json({
      success: true,
      message: 'Attendance record deleted successfully',
    })
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json({ error: 'Invalid input', details: error.errors }, { status: 400 })
    }

    // Handle not found error
    if (error.name === 'NotFoundError') {
      return NextResponse.json({ error: error.message }, { status: 404 })
    }

    // Handle authentication errors
    if (
      error instanceof Error &&
      (error.message.includes('Authentication') ||
        error.message.includes('token') ||
        error.message.includes('Access denied'))
    ) {
      return handleAuthError(error)
    }

    console.error('Error deleting attendance:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
