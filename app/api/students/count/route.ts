import { NextRequest, NextResponse } from 'next/server'
import { StudentRepository } from '@/lib/data/repositories/student'
import { getRedisCache } from '@/lib/data/cache/redis'
import { authenticateAdmin, handleAuthError } from '@/lib/middleware/auth'
import { db, schema } from '@/lib/data/drizzle/db'
import { count as sqlCount, eq, and } from 'drizzle-orm'

// Initialize dependencies
const cache = getRedisCache()
const studentRepo = new StudentRepository(cache)

/**
 * GET /api/students/count
 * Get count of all students, optionally filtered by class
 */
export async function GET(req: NextRequest) {
  try {
    // Authenticate the admin
    try {
      await authenticateAdmin(req)
    } catch (authError) {
      return handleAuthError(authError)
    }

    // Get query parameters
    const url = new URL(req.url)
    const classFilter = url.searchParams.get('class')
    const force = url.searchParams.get('force') === 'true'

    console.info('Fetching student count', classFilter ? { class: classFilter } : {})

    let studentCount = 0

    // If force refresh is requested, bypass the cache and repository abstraction
    if (force) {
      // Query the database directly using the same logic as in StudentRepository.getCount
      let result
      if (classFilter) {
        result = await db
          .select({ count: sqlCount() })
          .from(schema.users)
          .innerJoin(schema.classes, eq(schema.users.classId, schema.classes.id))
          .where(and(eq(schema.users.role, 'student'), eq(schema.classes.name, classFilter)))
      } else {
        result = await db
          .select({ count: sqlCount() })
          .from(schema.users)
          .where(eq(schema.users.role, 'student'))
      }
      studentCount = result[0]?.count || 0
      
    } else {
      // Get count from repository (which uses cache)
      studentCount = await studentRepo.getCount(classFilter || undefined)
    }

    
    return NextResponse.json({ count: studentCount })
  } catch (error) {
    console.error('Failed to fetch student count:', error instanceof Error ? error.message : error)
    return NextResponse.json(
      {
        message: 'Gagal mengambil jumlah siswa',
        error: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    )
  }
}
