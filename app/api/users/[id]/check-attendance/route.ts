import { NextRequest, NextResponse } from 'next/server'
import { UserUseCases } from '@/lib/domain/usecases/user'
import { StudentRepository } from '@/lib/data/repositories/student'
import { AdminRepository } from '@/lib/data/repositories/admin'
import { ClassRepository } from '@/lib/data/repositories/class'
import { AbsenceRepository } from '@/lib/data/repositories/absence'
import { getRedisCache } from '@/lib/data/cache/redis'
import { authenticateAdmin, handleAuthError } from '@/lib/middleware/auth'
import { NotFoundError } from '@/lib/domain/errors'

// Initialize dependencies
const cache = getRedisCache()
const studentRepo = new StudentRepository(cache)
const adminRepo = new AdminRepository(cache)
const classRepo = new ClassRepository()
const absenceRepo = new AbsenceRepository()
const userUseCases = new UserUseCases(studentRepo, adminRepo, classRepo, cache, absenceRepo)

/**
 * GET /api/users/[id]/check-attendance
 * Check if a student has attendance records
 */
export async function GET(req: NextRequest, { params }: { params: Promise<{ id: string }> }) {
  try {
    // Authenticate the admin
    try {
      await authenticateAdmin(req)
    } catch (authError) {
      return handleAuthError(authError)
    }

    // Get the user ID from params
    const { id } = await params
    const userId = parseInt(id)

    if (isNaN(userId)) {
      return NextResponse.json({ message: 'ID User tidak valid' }, { status: 400 })
    }

    // Get the user to check if it's a student
    const user = await userUseCases.getUserById(userId)

    if (!user) {
      return NextResponse.json({ message: 'User tidak ditemukan' }, { status: 404 })
    }

    // Only check attendance for students
    if (user.role !== 'student') {
      return NextResponse.json({
        hasAttendanceRecords: false,
        message: 'User bukan student',
      })
    }

    try {
      // Check if student has attendance records
      const result = await userUseCases.checkStudentHasAttendanceRecords(userId)

      return NextResponse.json({
        hasAttendanceRecords: result.hasRecords,
        name: result.name,
        uniqueCode: result.uniqueCode,
        message: result.hasRecords
          ? 'Student memiliki data absensi'
          : 'Student tidak memiliki data absensi',
      })
    } catch (error: any) {
      console.error(`Error checking attendance records for user ${userId}:`, error)

      if (error instanceof NotFoundError) {
        return NextResponse.json({ message: error.message }, { status: 404 })
      }

      return NextResponse.json(
        {
          message: 'Gagal memeriksa data absensi',
          error: error.message,
        },
        { status: 500 }
      )
    }
  } catch (error: any) {
    console.error('Unexpected error in GET /api/users/[id]/check-attendance:', error)
    return NextResponse.json(
      { message: 'Terjadi kesalahan', error: error.message },
      { status: 500 }
    )
  }
}
