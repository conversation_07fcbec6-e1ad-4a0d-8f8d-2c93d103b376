import { NextRequest, NextResponse } from 'next/server'
import { UserUseCases } from '@/lib/domain/usecases/user'
import { StudentRepository } from '@/lib/data/repositories/student'
import { AdminRepository } from '@/lib/data/repositories/admin'
import { ClassRepository } from '@/lib/data/repositories/class'
import { getRedisCache } from '@/lib/data/cache/redis'
import { authenticateAdmin, handleAuthError } from '@/lib/middleware/auth'
import { z } from 'zod'
import { AbsenceRepository } from '@/lib/data/repositories/absence'

// Initialize dependencies
const cache = getRedisCache()
const studentRepo = new StudentRepository(cache)
const adminRepo = new AdminRepository(cache)
const classRepo = new ClassRepository()
const absenceRepo = new AbsenceRepository()
const userUseCases = new UserUseCases(studentRepo, adminRepo, classRepo, cache, absenceRepo)

// Validation schema for bulk delete request
const bulkDeleteSchema = z.object({
  userIds: z.array(z.number().int().positive('User ID harus angka positif')),
  deleteAttendanceRecords: z.boolean().default(true),
})

/**
 * POST /api/users/bulk-delete
 * Delete multiple users at once
 */
export async function POST(req: NextRequest) {
  try {
    // Authenticate the admin
    try {
      await authenticateAdmin(req)
    } catch (authError) {
      return handleAuthError(authError)
    }

    const body = await req.json()
    const validation = bulkDeleteSchema.safeParse(body)

    if (!validation.success) {
      return NextResponse.json(
        { message: 'Validasi gagal', errors: validation.error.format() },
        { status: 400 }
      )
    }

    const { userIds, deleteAttendanceRecords } = validation.data

    if (userIds.length === 0) {
      return NextResponse.json({ message: 'Tidak ada ID user yang diberikan' }, { status: 400 })
    }

    // For tracking results
    const results = {
      total: userIds.length,
      success: 0,
      failed: 0,
      errors: [] as { userId: number; message: string }[],
    }

    // Process deletion in batches
    const batchSize = 50 // Delete 50 users at a time
    for (let i = 0; i < userIds.length; i += batchSize) {
      const batch = userIds.slice(i, i + batchSize)

      // Process each user deletion in parallel within the batch
      await Promise.all(
        batch.map(async userId => {
          try {
            // Get user to determine role
            const user = await userUseCases.getUserById(userId)

            if (!user) {
              results.failed++
              results.errors.push({
                userId,
                message: 'User tidak ditemukan',
              })
              return
            }

            // Delete the user
            await userUseCases.deleteUser(userId, user.role, deleteAttendanceRecords)

            results.success++
          } catch (error) {
            results.failed++
            const errorMessage = error instanceof Error ? error.message : 'Unknown error'
            results.errors.push({
              userId,
              message: errorMessage,
            })
          }
        })
      )

      // Clear cache after each batch
      await cache.del('users:all')
      await cache.del('classes:all')
    }

    return NextResponse.json({
      message: `Bulk delete selesai. Berhasil: ${results.success}, Gagal: ${results.failed} dari ${results.total}`,
      results,
    })
  } catch (error) {
    console.error('Bulk delete error:', error instanceof Error ? error.stack : error)
    return NextResponse.json(
      {
        message: 'Gagal memproses bulk delete',
        error: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    )
  }
}
