import { NextRequest, NextResponse } from 'next/server'
import { UserUseCases } from '@/lib/domain/usecases/user'
import { StudentRepository } from '@/lib/data/repositories/student'
import { AdminRepository } from '@/lib/data/repositories/admin'
import { ClassRepository } from '@/lib/data/repositories/class'
import { getRedisCache } from '@/lib/data/cache/redis'
import { authenticateAdmin, handleAuthError } from '@/lib/middleware/auth'
import { z } from 'zod'
import { AbsenceRepository } from '@/lib/data/repositories/absence'

// Initialize dependencies
const cache = getRedisCache()
const studentRepo = new StudentRepository(cache)
const adminRepo = new AdminRepository(cache)
const classRepo = new ClassRepository()
const absenceRepo = new AbsenceRepository()
const userUseCases = new UserUseCases(studentRepo, adminRepo, classRepo, cache, absenceRepo)

// Validation schema for bulk class update request
const bulkUpdateClassSchema = z.object({
  targetClassId: z.number().int().positive('Target Class ID harus angka positif'),
  studentIds: z.array(z.number().int().positive('Student ID harus angka positif')),
})

/**
 * POST /api/users/bulk-update-class
 * Update class for multiple selected students
 */
export async function POST(req: NextRequest) {
  try {
    // Authenticate the admin
    try {
      await authenticateAdmin(req)
    } catch (authError) {
      return handleAuthError(authError)
    }

    const body = await req.json()
    const validation = bulkUpdateClassSchema.safeParse(body)

    if (!validation.success) {
      return NextResponse.json(
        { message: 'Validasi gagal', errors: validation.error.format() },
        { status: 400 }
      )
    }

    const { targetClassId, studentIds } = validation.data

    if (studentIds.length === 0) {
      return NextResponse.json(
        { message: 'Tidak ada siswa yang dipilih untuk diperbarui' },
        { status: 400 }
      )
    }

    // Check if target class exists
    const targetClass = await userUseCases
      .getAllClasses()
      .then(classes => classes.find(cls => cls.id === targetClassId))

    if (!targetClass) {
      return NextResponse.json({ message: 'Kelas target tidak ditemukan' }, { status: 404 })
    }

    // Get all users to find students by ID
    const users = await userUseCases.getAllUsers()

    // Get student information for the IDs
    const studentsToUpdate = studentIds
      .map(id => users.find(user => user.id === id && user.role === 'student'))
      .filter(student => student !== undefined) as any[]

    if (studentsToUpdate.length === 0) {
      return NextResponse.json(
        { message: 'Tidak ada siswa valid yang ditemukan dari ID yang diberikan' },
        { status: 404 }
      )
    }

    // For tracking results
    const results = {
      total: studentsToUpdate.length,
      success: 0,
      failed: 0,
      errors: [] as { userId: number; name: string; message: string }[],
    }

    // Process updates in batches
    const batchSize = 50 // Update 50 students at a time
    for (let i = 0; i < studentsToUpdate.length; i += batchSize) {
      const batch = studentsToUpdate.slice(i, i + batchSize)

      // Process each student update in parallel within the batch
      await Promise.all(
        batch.map(async student => {
          try {
            // Update the student's class
            await userUseCases.updateStudent(student.id, {
              classId: targetClassId,
            })

            results.success++
          } catch (error) {
            results.failed++
            const errorMessage = error instanceof Error ? error.message : 'Unknown error'
            results.errors.push({
              userId: student.id,
              name: student.name,
              message: errorMessage,
            })
          }
        })
      )

      // Clear cache after each batch
      await cache.del('users:all')
    }

    // Collect original classes for reporting
    const originalClasses = new Set<string>()
    studentsToUpdate.forEach(student => {
      if (student.className) {
        originalClasses.add(student.className)
      }
    })

    const originalClassesStr = Array.from(originalClasses).join(', ') || 'tidak ada kelas'

    return NextResponse.json({
      message: `Update kelas selesai. ${results.success} siswa dipindahkan ke kelas ${targetClass.name}. Gagal: ${results.failed} dari ${results.total}`,
      results,
      originalClasses: Array.from(originalClasses),
      targetClass: targetClass.name,
    })
  } catch (error) {
    console.error('Bulk update class error:', error instanceof Error ? error.stack : error)
    return NextResponse.json(
      {
        message: 'Gagal memproses update kelas',
        error: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    )
  }
}
