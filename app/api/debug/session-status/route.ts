import { NextRequest, NextResponse } from 'next/server'
import { authenticateWithSession } from '@/lib/middleware/enhanced-auth'
import { checkSessionInvalidationEvents, getAllSessionInvalidationEvents } from '@/lib/utils/session-broadcast'

/**
 * GET /api/debug/session-status
 * Debug endpoint to check current session status and invalidation events
 * This endpoint helps troubleshoot session issues
 */
export async function GET(request: NextRequest) {
  const debugInfo: any = {
    timestamp: new Date().toISOString(),
    cookies: {},
    authentication: null,
    sessionEvents: null,
    allInvalidationEvents: [],
  }

  try {
    // Check cookies
    const adminToken = request.cookies.get('admin_auth_token')?.value
    const studentToken = request.cookies.get('student_auth_token')?.value
    
    debugInfo.cookies = {
      admin_auth_token: adminToken ? 'present' : 'missing',
      student_auth_token: studentToken ? 'present' : 'missing',
    }

    // Try to authenticate
    try {
      const authResult = await authenticateWithSession(request)
      debugInfo.authentication = {
        success: true,
        userId: authResult.id,
        role: authResult.role,
        sessionId: authResult.sessionId,
        deviceId: authResult.deviceId,
      }

      // Check for session invalidation events
      const events = checkSessionInvalidationEvents(authResult.id)
      debugInfo.sessionEvents = {
        hasEvents: events.length > 0,
        events: events,
      }
    } catch (authError) {
      debugInfo.authentication = {
        success: false,
        error: authError instanceof Error ? authError.message : 'Unknown error',
      }
    }

    // Get all invalidation events for debugging
    debugInfo.allInvalidationEvents = getAllSessionInvalidationEvents()

    return NextResponse.json({
      success: true,
      debug: debugInfo,
    })
  } catch (error) {
    debugInfo.error = error instanceof Error ? error.message : 'Unknown error'
    
    return NextResponse.json({
      success: false,
      debug: debugInfo,
    }, { status: 500 })
  }
}
