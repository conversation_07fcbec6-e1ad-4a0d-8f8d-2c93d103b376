import { NextRequest, NextResponse } from 'next/server'
import { authenticateSuperAdmin } from '@/lib/middleware/enhanced-auth'
import { debugUserSessions, cleanupUserSessions, forceCleanUserSessions } from '@/lib/utils/session-debug'

/**
 * GET /api/debug/sessions?userId=123&action=debug|cleanup|force-clean
 * Debug session issues (super_admin only)
 */
export async function GET(request: NextRequest) {
  // Only allow in development or for super admin
  if (process.env.NODE_ENV === 'production') {
    try {
      await authenticateSuperAdmin(request)
    } catch (error) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }
  }

  const { searchParams } = new URL(request.url)
  const userId = searchParams.get('userId')
  const action = searchParams.get('action') || 'debug'

  if (!userId) {
    return NextResponse.json({ error: 'userId parameter required' }, { status: 400 })
  }

  const userIdNum = parseInt(userId, 10)
  if (isNaN(userIdNum)) {
    return NextResponse.json({ error: 'Invalid userId' }, { status: 400 })
  }

  try {
    let result: any = { userId: userIdNum, action }

    switch (action) {
      case 'debug':
        await debugUserSessions(userIdNum)
        result.message = 'Debug information logged to console'
        break

      case 'cleanup':
        const cleanedCount = await cleanupUserSessions(userIdNum)
        result.message = `Cleaned up ${cleanedCount} sessions`
        result.cleanedCount = cleanedCount
        break

      case 'force-clean':
        await forceCleanUserSessions(userIdNum)
        result.message = 'Force cleaned all sessions'
        break

      default:
        return NextResponse.json({ error: 'Invalid action. Use: debug, cleanup, or force-clean' }, { status: 400 })
    }

    return NextResponse.json(result)

  } catch (error) {
    console.error('Debug sessions error:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
