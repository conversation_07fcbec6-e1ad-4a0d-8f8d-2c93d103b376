import { NextRequest, NextResponse } from 'next/server'
import { EnhancedAuthUseCases } from '@/lib/domain/usecases/enhanced-auth'
import { SessionUseCases } from '@/lib/domain/usecases/session'
import { RedisSessionRepository } from '@/lib/data/repositories/redis-session-repository'
import { StudentRepository } from '@/lib/data/repositories/student'
import { AdminRepository } from '@/lib/data/repositories/admin'
import { getRedisCache } from '@/lib/data/cache/redis'
import { extractDeviceInfo } from '@/lib/middleware/enhanced-auth'
import { serverConfig } from '@/lib/config'
import { z } from 'zod'

// Initialize dependencies
const cache = getRedisCache()
const sessionRepo = new RedisSessionRepository(cache)
const studentRepo = new StudentRepository(cache)
const adminRepo = new AdminRepository(cache)
const sessionUseCases = new SessionUseCases(sessionRepo, studentRepo, adminRepo, cache)
const enhancedAuthUseCases = new EnhancedAuthUseCases(
  studentRepo,
  adminRepo,
  sessionUseCases,
  cache,
  serverConfig.auth.jwtSecret || ''
)

// Input validation schema
const loginSchema = z.object({
  username: z.string().min(1, 'Username is required'),
  password: z.string().min(1, 'Password is required'),
})

/**
 * POST /api/auth/student/login
 * Login a student with username and password
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()

    // Validate input
    const result = loginSchema.safeParse(body)
    if (!result.success) {
      return NextResponse.json(
        { error: 'Invalid input', details: result.error.format() },
        { status: 400 }
      )
    }

    const { username, password } = result.data

    try {
      console.log('Attempting student login for username:', username)

      // Extract device info from request
      const { userAgent, ipAddress } = extractDeviceInfo(request)

      const { token, refreshToken, student, session } =
        await enhancedAuthUseCases.loginStudentWithCredentials(
          username,
          password,
          userAgent,
          ipAddress
        )

      console.log('Student login successful for:', student.name)

      // Set the tokens as cookies
      const response = NextResponse.json({
        success: true,
        student: {
          id: student.id,
          name: student.name,
          uniqueCode: student.uniqueCode,
        },
      })

      // Set HTTP-only cookies for security with student-specific cookie names
      const { getSecureCookieOptions, COOKIE_NAMES } = await import('@/lib/utils/cookie-security')

      response.cookies.set(COOKIE_NAMES.STUDENT_AUTH, token, getSecureCookieOptions(60 * 60)) // 1 hour

      response.cookies.set(
        COOKIE_NAMES.STUDENT_REFRESH,
        refreshToken,
        getSecureCookieOptions(7 * 24 * 60 * 60)
      ) // 7 days

      return response
    } catch (error) {
      console.error('Student login error:', error)
      return NextResponse.json({ error: 'Invalid username or password' }, { status: 401 })
    }
  } catch (error) {
    console.error('Student login error:', error)
    return NextResponse.json({ error: 'Failed to process login request' }, { status: 500 })
  }
}
