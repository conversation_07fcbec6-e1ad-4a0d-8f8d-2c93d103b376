import { NextRequest, NextResponse } from 'next/server'
import { AuthUseCases } from '@/lib/domain/usecases/auth'
import { StudentRepository } from '@/lib/data/repositories/student'
import { AdminRepository } from '@/lib/data/repositories/admin'
import { getRedisCache } from '@/lib/data/cache/redis'
import { serverConfig } from '@/lib/config'
import { z } from 'zod'
import { ValidationError, NotFoundError } from '@/lib/domain/errors'

// Initialize dependencies
const cache = getRedisCache()
const studentRepo = new StudentRepository(cache)
const adminRepo = new AdminRepository(cache)
const authUseCases = new AuthUseCases(
  studentRepo,
  adminRepo,
  cache,
  serverConfig.auth.jwtSecret || ''
)

// Validation schema for the request
const requestSchema = z
  .object({
    // Allow either email or whatsapp (at least one must be provided)
    email: z.string().email().optional(),
    whatsapp: z
      .string()
      .regex(/^(\+?)[0-9]{10,15}$/, 'Invalid WhatsApp number format')
      .transform(val => (val.startsWith('+') ? val.substring(1) : val))
      .optional(),
  })
  .refine(data => data.email || data.whatsapp, {
    message: 'Either email or WhatsApp number must be provided',
    path: ['email'],
  })

/**
 * POST /api/auth/password-reset/request
 * Request a password reset via either WhatsApp OTP or email link
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()

    // Validate input
    const result = requestSchema.safeParse(body)
    if (!result.success) {
      return NextResponse.json(
        { error: 'Input tidak valid', details: result.error.format() },
        { status: 400 }
      )
    }

    const { email, whatsapp } = result.data

    try {
      // Call the appropriate use case method
      if (email) {
        await authUseCases.sendPasswordResetEmail(email)
      } else if (whatsapp) {
        await authUseCases.sendPasswordResetOTP(whatsapp)
      }

      return NextResponse.json({
        success: true,
        message: email
          ? 'Link reset password telah dikirim ke email Anda'
          : 'Kode OTP reset password telah dikirim ke WhatsApp Anda',
      })
    } catch (error) {
      console.error('Password reset request error:', error)

      // Specific error handling based on error type
      if (error instanceof NotFoundError) {
        return NextResponse.json({ error: 'Akun tidak ditemukan' }, { status: 404 })
      } else if (error instanceof ValidationError) {
        // Check if it's a rate limit error
        if (
          error.message.includes('rate limit') ||
          error.message.includes('too many') ||
          error.message.toLowerCase().includes('try again later')
        ) {
          return NextResponse.json(
            {
              error: 'Terlalu banyak permintaan reset password. Silakan coba lagi dalam 1 jam.',
              isRateLimit: true,
            },
            { status: 429 }
          )
        }

        return NextResponse.json({ error: error.message }, { status: 400 })
      }

      return NextResponse.json(
        { error: 'Gagal mengirim permintaan reset password' },
        { status: 500 }
      )
    }
  } catch (error) {
    console.error('Password reset request error:', error)
    return NextResponse.json(
      { error: 'Gagal memproses permintaan reset password' },
      { status: 500 }
    )
  }
}
