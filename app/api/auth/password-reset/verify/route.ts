import { NextRequest, NextResponse } from 'next/server'
import { AuthUseCases } from '@/lib/domain/usecases/auth'
import { StudentRepository } from '@/lib/data/repositories/student'
import { AdminRepository } from '@/lib/data/repositories/admin'
import { getRedisCache } from '@/lib/data/cache/redis'
import { serverConfig } from '@/lib/config'
import { z } from 'zod'
import { ValidationError, NotFoundError } from '@/lib/domain/errors'

// Initialize dependencies
const cache = getRedisCache()
const studentRepo = new StudentRepository(cache)
const adminRepo = new AdminRepository(cache)
const authUseCases = new AuthUseCases(
  studentRepo,
  adminRepo,
  cache,
  serverConfig.auth.jwtSecret || ''
)

// Validation schema
const verifySchema = z
  .object({
    // For WhatsApp verification
    whatsapp: z.string().optional(),
    otp: z.string().min(6).max(6).optional(),

    // For token verification (email link)
    token: z.string().optional(),

    // New password (required in all cases)
    newPassword: z.string().min(6, 'Password harus minimal 6 karakter'),
  })
  .refine(data => (data.whatsapp && data.otp) || data.token, {
    message: 'WhatsApp+OTP atau token reset harus disediakan',
    path: ['token'],
  })

/**
 * POST /api/auth/password-reset/verify
 * Verify password reset OTP/token and set new password
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()

    // Validate input
    const result = verifySchema.safeParse(body)
    if (!result.success) {
      return NextResponse.json(
        { error: 'Input tidak valid', details: result.error.format() },
        { status: 400 }
      )
    }

    const { whatsapp, otp, token, newPassword } = result.data

    try {
      // Call the appropriate use case method
      if (token) {
        await authUseCases.resetPasswordWithToken(token, newPassword)
      } else if (whatsapp && otp) {
        // First verify the OTP is valid
        const cacheKey = `password_reset:otp:${whatsapp}`
        const storedOtp = await cache.get(cacheKey)

        if (!storedOtp) {
          return NextResponse.json(
            {
              error: 'OTP sudah kedaluwarsa atau tidak valid. Silakan mulai proses dari awal.',
              isOtpInvalid: true,
            },
            { status: 400 }
          )
        }

        if (storedOtp !== otp) {
          return NextResponse.json(
            {
              error: 'OTP tidak valid. Silakan mulai proses dari awal.',
              isOtpInvalid: true,
            },
            { status: 400 }
          )
        }

        // If OTP is valid, proceed with reset
        await authUseCases.resetPasswordWithOTP(whatsapp, otp, newPassword)
      }

      return NextResponse.json({
        success: true,
        message: 'Password berhasil diperbarui',
      })
    } catch (error) {
      console.error('Password reset verification error:', error)

      if (error instanceof ValidationError) {
        // Check for different types of validation errors
        const errorMessage = error.message.toLowerCase()

        // Rate limit errors
        if (
          errorMessage.includes('too many failed attempts') ||
          errorMessage.includes('too many attempts')
        ) {
          return NextResponse.json(
            {
              error: 'Terlalu banyak percobaan gagal. Silakan mulai proses dari awal.',
              isRateLimit: true,
            },
            { status: 429 }
          )
        }

        // Invalid OTP errors
        if (
          errorMessage.includes('invalid otp') ||
          errorMessage.includes('otp expired') ||
          errorMessage.includes('request a new')
        ) {
          return NextResponse.json(
            {
              error: 'OTP tidak valid atau sudah kedaluwarsa. Silakan mulai proses dari awal.',
              isOtpInvalid: true,
            },
            { status: 400 }
          )
        }

        // Invalid token errors
        if (
          errorMessage.includes('invalid token') ||
          errorMessage.includes('expired token') ||
          errorMessage.includes('invalid or expired')
        ) {
          return NextResponse.json(
            {
              error:
                'Token reset password tidak valid atau sudah kedaluwarsa. Silakan mulai proses dari awal.',
              isTokenInvalid: true,
            },
            { status: 400 }
          )
        }

        // Other validation errors
        return NextResponse.json({ error: error.message }, { status: 400 })
      }

      if (error instanceof NotFoundError) {
        return NextResponse.json({ error: 'Akun tidak ditemukan' }, { status: 404 })
      }

      return NextResponse.json({ error: 'Gagal reset password' }, { status: 500 })
    }
  } catch (error) {
    console.error('Password reset verification error:', error)
    return NextResponse.json(
      { error: 'Gagal memproses verifikasi reset password' },
      { status: 500 }
    )
  }
}
