import { NextRequest, NextResponse } from 'next/server'
import { clearAuthCookies } from '@/lib/middleware/enhanced-auth'

/**
 * POST /api/auth/force-logout
 * Endpoint to clear cookies when user is force logged out
 * This endpoint is called by the client when they detect their session is invalid
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { role } = body

    if (!role || !['admin', 'student'].includes(role)) {
      return NextResponse.json(
        { error: 'Invalid role specified' },
        { status: 400 }
      )
    }

    // Create response and clear cookies
    const response = NextResponse.json({
      success: true,
      message: 'Cookies cleared successfully'
    })

    // Clear auth cookies for the specified role
    return clearAuthCookies(response, role as 'admin' | 'student')
  } catch (error) {
    console.error('Error in force logout:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
