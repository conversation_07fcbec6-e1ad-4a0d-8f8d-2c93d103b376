import { NextRequest, NextResponse } from 'next/server'
import { authenticateWithSession } from '@/lib/middleware/enhanced-auth'
import { checkSessionInvalidationEvents } from '@/lib/utils/session-broadcast'

/**
 * GET /api/auth/session-events
 * Check for pending session invalidation events for the current user
 * This endpoint is used for real-time session monitoring
 */
export async function GET(request: NextRequest) {
  try {
    // Authenticate the user
    const authResult = await authenticateWithSession(request)

    // Check for pending session invalidation events
    const events = checkSessionInvalidationEvents(authResult.id)

    if (events.length > 0) {
      // User has pending session invalidation events
      console.log(`Session invalidation events found for user ${authResult.id}:`, events)
      return NextResponse.json(
        {
          hasInvalidationEvents: true,
          events,
          message: 'Session has been invalidated',
        },
        { status: 401 }
      )
    }

    // No invalidation events, session is still valid
    return NextResponse.json({
      hasInvalidationEvents: false,
      message: 'Session is valid',
    })
  } catch (error) {
    console.error('Session events check failed:', error)

    // IMPORTANT: Don't return 401 for authentication failures in session events
    // This endpoint should only return 401 when there are actual invalidation events
    // Authentication failures should return 200 with hasInvalidationEvents: false
    return NextResponse.json(
      {
        hasInvalidationEvents: false,
        error: 'Authentication failed - but no invalidation events',
        message: 'Session events check failed',
      },
      { status: 200 }
    )
  }
}
