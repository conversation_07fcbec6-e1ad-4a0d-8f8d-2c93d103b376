import { NextRequest, NextResponse } from 'next/server'
import { verifyToken } from '@/lib/utils/auth'
import { serverConfig } from '@/lib/config'
import { AdminRepository } from '@/lib/data/repositories/admin'
import { getRedisCache } from '@/lib/data/cache/redis'

// Initialize dependencies
const cache = getRedisCache()
const adminRepo = new AdminRepository(cache)

/**
 * GET /api/auth/admin/session
 * Returns the current admin session if authenticated
 */
export async function GET(request: NextRequest) {
  try {
    // Get the auth token from the cookie
    const authToken = request.cookies.get('admin_auth_token')?.value

    if (!authToken) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Verify the JWT token
    try {
      const decoded = verifyToken(authToken, serverConfig.auth.jwtSecret || '')

      if (decoded.role !== 'admin' && decoded.role !== 'super_admin') {
        return NextResponse.json({ error: 'Forbidden: Insufficient role' }, { status: 403 })
      }

      // Get the admin from the repository
      const admin = await adminRepo.findById(decoded.id)
      if (!admin) {
        return NextResponse.json({ error: 'Admin not found' }, { status: 404 })
      }

      // Return the admin session data (excluding sensitive info)
      return NextResponse.json({
        admin: {
          id: admin.id,
          username: admin.username,
          name: admin.name,
          role: admin.role,
        },
      })
    } catch (error) {
      console.error('Token verification error:', error)
      return NextResponse.json({ error: 'Invalid token' }, { status: 401 })
    }
  } catch (error) {
    console.error('Session error:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
