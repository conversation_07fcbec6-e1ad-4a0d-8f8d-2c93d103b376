import { NextRequest, NextResponse } from 'next/server'
import { AdminRepository } from '@/lib/data/repositories/admin'
import { getRedisCache } from '@/lib/data/cache/redis'
import { verifyToken } from '@/lib/utils/auth'
import { serverConfig } from '@/lib/config'
import { z } from 'zod'

// Initialize dependencies
const cache = getRedisCache()
const adminRepo = new AdminRepository(cache)

// Input validation schema
const updateProfileSchema = z.object({
  name: z.string().min(1, 'Name is required'),
})

/**
 * PATCH /api/admin/profile
 * Updates the admin's profile
 */
export async function PATCH(request: NextRequest) {
  try {
    // Authenticate the request
    const authToken = request.cookies.get('admin_auth_token')?.value

    if (!authToken) {
      return NextResponse.json({ error: 'Authentication required' }, { status: 401 })
    }

    try {
      // Verify the token
      const decoded = verifyToken(authToken, serverConfig.auth.jwtSecret || '')

      if (!decoded || !decoded.id || (decoded.role !== 'admin' && decoded.role !== 'super_admin')) {
        return NextResponse.json({ error: 'Invalid or expired token' }, { status: 401 })
      }

      // Parse and validate the request body
      const body = await request.json()
      const result = updateProfileSchema.safeParse(body)

      if (!result.success) {
        return NextResponse.json(
          { error: 'Invalid input', details: result.error.format() },
          { status: 400 }
        )
      }

      const { name } = result.data

      // Update the admin's profile
      const admin = await adminRepo.update(decoded.id, { name })

      // Return the updated admin data (excluding sensitive information)
      return NextResponse.json({
        success: true,
        admin: {
          id: admin.id,
          username: admin.username,
          name: admin.name,
        },
      })
    } catch (error) {
      console.error('Token verification failed:', error)
      return NextResponse.json({ error: 'Invalid or expired token' }, { status: 401 })
    }
  } catch (error) {
    console.error('Error updating admin profile:', error)
    return NextResponse.json({ error: 'Failed to update admin profile' }, { status: 500 })
  }
}

/**
 * GET /api/admin/profile
 * Gets the admin's profile
 */
export async function GET(request: NextRequest) {
  try {
    // Authenticate the request
    const authToken = request.cookies.get('admin_auth_token')?.value

    if (!authToken) {
      return NextResponse.json({ error: 'Authentication required' }, { status: 401 })
    }

    try {
      // Verify the token
      const decoded = verifyToken(authToken, serverConfig.auth.jwtSecret || '')

      if (!decoded || !decoded.id || (decoded.role !== 'admin' && decoded.role !== 'super_admin')) {
        return NextResponse.json({ error: 'Invalid or expired token' }, { status: 401 })
      }

      // Get the admin's profile
      const admin = await adminRepo.findById(decoded.id)

      if (!admin) {
        return NextResponse.json({ error: 'Admin not found' }, { status: 404 })
      }

      // Return the admin data (excluding sensitive information)
      return NextResponse.json({
        admin: {
          id: admin.id,
          username: admin.username,
          name: admin.name,
        },
      })
    } catch (error) {
      console.error('Token verification failed:', error)
      return NextResponse.json({ error: 'Invalid or expired token' }, { status: 401 })
    }
  } catch (error) {
    console.error('Error getting admin profile:', error)
    return NextResponse.json({ error: 'Failed to get admin profile' }, { status: 500 })
  }
}
