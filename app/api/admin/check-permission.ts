import { NextRequest, NextResponse } from 'next/server'
import { verifyToken } from '@/lib/utils/auth'
import { serverConfig } from '@/lib/config'
import { AdminRepository } from '@/lib/data/repositories/admin'
import { getRedisCache } from '@/lib/data/cache/redis'

// Initialize dependencies
const cache = getRedisCache()
const adminRepo = new AdminRepository(cache)

/**
 * GET /api/admin/check-permission
 * Checks if the current admin has super_admin permissions
 */
export async function GET(request: NextRequest) {
  try {
    // Get the auth token from the cookie
    const authToken = request.cookies.get('admin_auth_token')?.value

    if (!authToken) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    try {
      // Verify the token
      const decoded = verifyToken(authToken, serverConfig.auth.jwtSecret || '')

      if (!decoded || !decoded.id) {
        return NextResponse.json({ error: 'Invalid or expired token' }, { status: 401 })
      }

      // Get the admin from the repository
      const admin = await adminRepo.findById(decoded.id)

      if (!admin) {
        return NextResponse.json({ error: 'Admin not found' }, { status: 404 })
      }

      // Check if the admin is a super_admin
      const isSuperAdmin = admin.role === 'super_admin'

      // Return the permission status
      return NextResponse.json({
        isSuperAdmin,
        role: admin.role,
      })
    } catch (error) {
      console.error('Token verification failed:', error)
      return NextResponse.json({ error: 'Invalid or expired token' }, { status: 401 })
    }
  } catch (error) {
    console.error('Error checking admin permission:', error)
    return NextResponse.json({ error: 'Failed to check admin permission' }, { status: 500 })
  }
}
