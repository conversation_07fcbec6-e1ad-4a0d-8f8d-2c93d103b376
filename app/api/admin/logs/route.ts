import { NextRequest, NextResponse } from 'next/server'
import { authenticateAdmin, handleAuthError } from '@/lib/middleware/auth'
import { withLogging } from '@/lib/middleware/logger'
import { logger } from '@/lib/utils/logger'
import path from 'path'
import fs from 'fs/promises'
import { z } from 'zod'

// Validation schema for log query parameters
const logQuerySchema = z.object({
  date: z.string().optional().nullable(),
  level: z.enum(['info', 'warn', 'error']).optional().nullable(),
  type: z.enum(['api', 'auth', 'system', 'warning']).optional().nullable(),
  limit: z.coerce.number().min(1).max(1000).default(100),
  page: z.coerce.number().min(1).default(1),
})

/**
 * GET /api/admin/logs
 * Get application logs (admin only)
 */
export async function GET(req: NextRequest) {
  return withLogging(req, async (req: NextRequest) => {
    try {
      // Authenticate the admin
      try {
        await authenticateAdmin(req)
      } catch (authError) {
        return handleAuthError(authError)
      }

      // Get query parameters
      const url = new URL(req.url)
      const date = url.searchParams.get('date')
      const level = url.searchParams.get('level') as 'info' | 'warn' | 'error' | null
      const type = url.searchParams.get('type') as 'api' | 'auth' | 'system' | 'warning' | null
      const limitParam = url.searchParams.get('limit')
      const pageParam = url.searchParams.get('page')

      try {
        // Validate and parse query parameters
        const { limit, page } = logQuerySchema.parse({
          date,
          level,
          type,
          limit: limitParam ? parseInt(limitParam) : 100,
          page: pageParam ? parseInt(pageParam) : 1,
        })

        // Calculate pagination offsets
        const offset = (page - 1) * limit

        // Get log file content
        const logs = await getLogEntries(
          date || undefined,
          level || undefined,
          type || undefined,
          limit,
          offset
        )

        // Return the logs with pagination info
        return NextResponse.json({
          logs,
          pagination: {
            page,
            limit,
            total: logs.totalCount,
            totalPages: Math.ceil(logs.totalCount / limit),
          },
        })
      } catch (error) {
        if (error instanceof z.ZodError) {
          return NextResponse.json(
            { error: 'Invalid query parameters', details: error.errors },
            { status: 400 }
          )
        }
        throw error
      }
    } catch (error) {
      logger.error('Error retrieving logs:', { error })
      return NextResponse.json({ error: 'Failed to retrieve logs' }, { status: 500 })
    }
  })
}

/**
 * Get filtered log entries from the log files
 */
async function getLogEntries(
  date?: string,
  level?: 'info' | 'warn' | 'error',
  type?: 'api' | 'auth' | 'system' | 'warning',
  limit: number = 100,
  offset: number = 0
): Promise<{ entries: any[]; totalCount: number }> {
  // Determine which log file to read
  const logDir = path.join(process.cwd(), 'logs')
  let logFileName = 'shalat-yuk'

  if (date) {
    logFileName += `-${date}`
  } else {
    // Default to today's log
    const today = new Date().toISOString().split('T')[0]
    logFileName += `-${today}`
  }

  logFileName += '.log'
  const logFilePath = path.join(logDir, logFileName)

  try {
    // Check if the log file exists
    await fs.access(logFilePath)

    // Read the log file
    const content = await fs.readFile(logFilePath, 'utf-8')

    // Parse log entries
    const entries = content
      .split('\n')
      .filter(line => line.trim() !== '')
      .map(line => {
        try {
          return JSON.parse(line)
        } catch (e) {
          return { raw: line, parseError: true }
        }
      })

    // Apply filters
    let filteredEntries = entries

    if (level) {
      filteredEntries = filteredEntries.filter(entry => entry.level === level)
    }

    if (type) {
      filteredEntries = filteredEntries.filter(entry => {
        if (type === 'api' && entry.requestType === 'api') return true
        if (type === 'auth' && entry.eventType === 'auth') return true
        if (type === 'system' && entry.eventType === 'system') return true
        if (type === 'warning' && entry.eventType === 'warning') return true
        return false
      })
    }

    // Apply pagination
    const totalCount = filteredEntries.length
    const paginatedEntries = filteredEntries.slice(offset, offset + limit)

    return {
      entries: paginatedEntries,
      totalCount,
    }
  } catch (error) {
    // Log file doesn't exist or can't be read
    if ((error as NodeJS.ErrnoException).code === 'ENOENT') {
      return { entries: [], totalCount: 0 }
    }

    // Rethrow other errors
    throw error
  }
}

/**
 * DELETE /api/admin/logs
 * Clear logs for a specific date or specific log file
 */
export async function DELETE(req: NextRequest) {
  return withLogging(req, async (req: NextRequest) => {
    try {
      // Authenticate the admin
      try {
        await authenticateAdmin(req)
      } catch (authError) {
        return handleAuthError(authError)
      }

      // Get date from query parameter
      const url = new URL(req.url)
      const date = url.searchParams.get('date')

      if (!date) {
        return NextResponse.json({ error: 'Date parameter is required' }, { status: 400 })
      }

      const logDir = path.join(process.cwd(), 'logs')
      const logFileName = `shalat-yuk-${date}.log`
      const logFilePath = path.join(logDir, logFileName)

      try {
        // Check if the log file exists
        await fs.access(logFilePath)

        // Delete the log file
        await fs.unlink(logFilePath)

        // Log the deletion
        logger.info(`Log file deleted: ${logFileName}`, { eventType: 'system' })

        return NextResponse.json({ message: `Log file for ${date} has been deleted` })
      } catch (error) {
        if ((error as NodeJS.ErrnoException).code === 'ENOENT') {
          return NextResponse.json({ error: `Log file for ${date} not found` }, { status: 404 })
        }
        throw error
      }
    } catch (error) {
      logger.error('Error deleting log file:', { error })
      return NextResponse.json({ error: 'Failed to delete log file' }, { status: 500 })
    }
  })
}
