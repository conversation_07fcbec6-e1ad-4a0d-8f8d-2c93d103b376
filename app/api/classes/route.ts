import { NextRequest, NextResponse } from 'next/server'
import { UserUseCases } from '@/lib/domain/usecases/user'
import { StudentRepository } from '@/lib/data/repositories/student'
import { AdminRepository } from '@/lib/data/repositories/admin'
import { ClassRepository } from '@/lib/data/repositories/class'
import { AbsenceRepository } from '@/lib/data/repositories/absence'
import { getRedisCache } from '@/lib/data/cache/redis'
import { authenticateAdmin, handleAuthError } from '@/lib/middleware/auth'
import { withLogging } from '@/lib/middleware/logger'
import { logger, logAPIError } from '@/lib/utils/logger'
import { z } from 'zod'
import { DuplicateError, NotFoundError } from '@/lib/domain/errors'

// Initialize dependencies
const cache = getRedisCache()
const studentRepo = new StudentRepository(cache)
const adminRepo = new AdminRepository(cache)
const classRepo = new ClassRepository()
const absenceRepo = new AbsenceRepository()
const userUseCases = new UserUseCases(studentRepo, adminRepo, classRepo, cache, absenceRepo)

// Validation schema for creating a class
const createClassSchema = z.object({
  name: z.string().min(1, 'Nama kelas tidak boleh kosong'),
})

/**
 * GET /api/classes
 * Get all classes
 */
export async function GET(req: NextRequest) {
  return withLogging(req, async (req: NextRequest) => {
    try {
      // Authenticate the admin
      try {
        await authenticateAdmin(req)
      } catch (authError) {
        return handleAuthError(authError)
      }

      // Get all classes
      const classes = await userUseCases.getAllClasses()

      // Return the classes
      return NextResponse.json(classes)
    } catch (error) {
      logAPIError('GET', '/api/classes', error)
      return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
    }
  })
}

/**
 * POST /api/classes
 * Create a new class
 */
export async function POST(req: NextRequest) {
  return withLogging(req, async (req: NextRequest) => {
    try {
      // Authenticate the admin
      try {
        await authenticateAdmin(req)
      } catch (authError) {
        return handleAuthError(authError)
      }

      // Parse and validate the request body
      try {
        const body = await req.json()
        const { name } = createClassSchema.parse(body)

        // Create the class
        const newClass = await userUseCases.createClass(name)

        // Return the created class
        return NextResponse.json(newClass)
      } catch (error: unknown) {
        if (error instanceof z.ZodError) {
          return NextResponse.json(
            { error: 'Invalid input', details: error.errors },
            { status: 400 }
          )
        }

        // Handle duplicate class error
        if (error instanceof DuplicateError) {
          return NextResponse.json({ error: error.message }, { status: 409 })
        }

        throw error // Re-throw to be caught by outer catch block
      }
    } catch (error) {
      logAPIError('POST', '/api/classes', error)
      return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
    }
  })
}

/**
 * PUT /api/classes?id={id}
 * Update a class
 */
export async function PUT(req: NextRequest) {
  return withLogging(req, async (req: NextRequest) => {
    try {
      // Authenticate the admin
      try {
        await authenticateAdmin(req)
      } catch (authError) {
        return handleAuthError(authError)
      }

      // Get class ID from the URL
      const url = new URL(req.url)
      const id = Number(url.searchParams.get('id'))

      if (!id || isNaN(id)) {
        return NextResponse.json({ error: 'Invalid class ID' }, { status: 400 })
      }

      try {
        // Parse and validate the request body
        const body = await req.json()
        const { name } = createClassSchema.parse(body)

        // Update the class
        const updatedClass = await userUseCases.updateClass(id, name)

        // Return the updated class
        return NextResponse.json(updatedClass)
      } catch (error: unknown) {
        if (error instanceof z.ZodError) {
          return NextResponse.json(
            { error: 'Invalid input', details: error.errors },
            { status: 400 }
          )
        }

        // Handle not found error
        if (error instanceof NotFoundError) {
          return NextResponse.json({ error: error.message }, { status: 404 })
        }

        // Handle duplicate class error
        if (error instanceof DuplicateError) {
          return NextResponse.json({ error: error.message }, { status: 409 })
        }

        throw error // Re-throw to be caught by outer catch block
      }
    } catch (error) {
      logAPIError('PUT', '/api/classes', error)
      return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
    }
  })
}

/**
 * DELETE /api/classes?id={id}
 * Delete a class
 */
export async function DELETE(req: NextRequest) {
  return withLogging(req, async (req: NextRequest) => {
    try {
      // Authenticate the admin
      try {
        await authenticateAdmin(req)
      } catch (authError) {
        return handleAuthError(authError)
      }

      // Get class ID from the URL
      const url = new URL(req.url)
      const id = Number(url.searchParams.get('id'))

      if (!id || isNaN(id)) {
        return NextResponse.json({ error: 'Invalid class ID' }, { status: 400 })
      }

      try {
        // Delete the class
        await userUseCases.deleteClass(id)

        // Return success message
        return NextResponse.json({ message: 'Class deleted successfully' }, { status: 200 })
      } catch (error: unknown) {
        // Handle not found error
        if (error instanceof NotFoundError) {
          return NextResponse.json({ error: error.message }, { status: 404 })
        }

        throw error // Re-throw to be caught by outer catch block
      }
    } catch (error) {
      logAPIError('DELETE', '/api/classes', error)
      return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
    }
  })
}
