import { Card, CardContent } from "@/components/ui/card"
import { ThemeToggle } from "@/components/theme-toggle"
import { FileText, QrCode, Camera } from "lucide-react"
import Image from "next/image"

export default function LandingPage() {
  return (
    <div className="min-h-screen bg-gradient-to-b from-indigo-50 to-white dark:from-slate-900 dark:to-slate-800">
      <header className="container mx-auto px-4 py-6 flex justify-between items-center">
        <h1 className="text-2xl font-bold text-slate-800 dark:text-slate-100">ShalatYuk</h1>
        <div className="flex items-center gap-4">
          <ThemeToggle />
        </div>
      </header>

      <main className="container mx-auto px-4 pb-16">
        {/* Hero Section */}
        <section className="py-12 md:py-20 grid md:grid-cols-2 gap-8 items-center">
          <div className="space-y-4">
            <h1 className="text-3xl md:text-5xl font-extrabold tracking-tight text-slate-800 dark:text-slate-100 leading-tight">
              ShalatYuk: Absensi Shalat Modern untuk Masa Depan Pendidikan
            </h1>
            <p className="text-lg text-slate-500 dark:text-slate-400 italic">
              Mendukung pelajar dalam ibadah shalat Zuhur dan Asr serta absensi pulang sekolah
            </p>
          </div>
          <div className="flex justify-center">
            <div className="relative w-full max-w-md aspect-[4/3] rounded-lg overflow-hidden border-2 border-indigo-600">
              <Image
                src="/placeholder.svg?height=400&width=600"
                alt="Masjid"
                width={600}
                height={400}
                className="object-cover"
              />
            </div>
          </div>
        </section>

        {/* About Section */}
        <section className="py-12">
          <h2 className="text-2xl font-semibold text-center mb-8 text-slate-800 dark:text-slate-100">
            Tentang Aplikasi
          </h2>
          <Card className="max-w-3xl mx-auto shadow-sm">
            <CardContent className="p-6">
              <p className="text-slate-800 dark:text-slate-100">
                ShalatYuk memudahkan pencatatan kehadiran shalat dan pulang sekolah dengan teknologi QR
                code. Sistem ini dirancang untuk meningkatkan kedisiplinan pelajar dalam melaksanakan ibadah shalat Zuhur
                dan Asr, serta memastikan proses absensi pulang sekolah yang terorganisir dengan baik.
              </p>
            </CardContent>
          </Card>
        </section>

        {/* Features Section */}
        <section className="py-12">
          <h2 className="text-2xl font-semibold text-center mb-8 text-slate-800 dark:text-slate-100">Fitur Utama</h2>
          <div className="grid md:grid-cols-3 gap-6">
            <Card className="border border-indigo-200 dark:border-slate-600 hover:translate-y-[-2px] transition-transform duration-200">
              <CardContent className="p-6 flex flex-col items-center text-center">
                <div className="bg-indigo-100 dark:bg-slate-700 p-4 rounded-full mb-4">
                  <QrCode className="h-8 w-8 text-indigo-600 dark:text-indigo-400" />
                </div>
                <h3 className="font-semibold mb-2 text-slate-800 dark:text-slate-100">QR Code Siswa</h3>
                <p className="text-slate-500 dark:text-slate-400">
                  Absensi cepat dengan QR code unik untuk setiap siswa
                </p>
              </CardContent>
            </Card>

            <Card className="border border-indigo-200 dark:border-slate-600 hover:translate-y-[-2px] transition-transform duration-200">
              <CardContent className="p-6 flex flex-col items-center text-center">
                <div className="bg-indigo-100 dark:bg-slate-700 p-4 rounded-full mb-4">
                  <Camera className="h-8 w-8 text-indigo-600 dark:text-indigo-400" />
                </div>
                <h3 className="font-semibold mb-2 text-slate-800 dark:text-slate-100">Scanner Admin</h3>
                <p className="text-slate-500 dark:text-slate-400">
                  Pindai QR code untuk shalat Zuhur, Asr, dan pulang sekolah
                </p>
              </CardContent>
            </Card>

            <Card className="border border-indigo-200 dark:border-slate-600 hover:translate-y-[-2px] transition-transform duration-200">
              <CardContent className="p-6 flex flex-col items-center text-center">
                <div className="bg-indigo-100 dark:bg-slate-700 p-4 rounded-full mb-4">
                  <FileText className="h-8 w-8 text-indigo-600 dark:text-indigo-400" />
                </div>
                <h3 className="font-semibold mb-2 text-slate-800 dark:text-slate-100">Laporan Harian</h3>
                <p className="text-slate-500 dark:text-slate-400">
                  Laporan kehadiran terperinci untuk pemantauan yang efektif
                </p>
              </CardContent>
            </Card>
          </div>
        </section>
      </main>

      <footer className="bg-indigo-50 dark:bg-slate-900 py-4 border-t border-slate-200 dark:border-slate-700">
        <div className="container mx-auto px-4 text-center text-sm text-slate-500 dark:text-slate-400">
          Banjarmasin High School - 2025 | Contact: +6281234567890
        </div>
      </footer>
    </div>
  )
}
