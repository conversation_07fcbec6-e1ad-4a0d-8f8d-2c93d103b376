import type React from "react"
import "@/app/globals.css"
import { Inter } from "next/font/google"
import ClientThemeProvider from "@/components/client-theme-provider"
import ClientToaster from "@/components/client-toaster"

const inter = Inter({ subsets: ["latin"] })

export const metadata = {
  title: "Aplikasi Absensi Shalat",
  description: "Aplikasi absensi shalat Zuhur dan Asr serta absensi pulang untuk siswa SMA di Banjarmasin",
  generator: 'v0.dev'
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="id" suppressHydrationWarning>
      <body className={inter.className}>
        <ClientThemeProvider>
          {children}
          <ClientToaster />
        </ClientThemeProvider>
      </body>
    </html>
  )
}
