'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { useToast } from '@/components/ui/use-toast'
import { Loader2 } from 'lucide-react'

export default function FixDatabase() {
  const { toast } = useToast()
  const [isLoading, setIsLoading] = useState(false)
  const [result, setResult] = useState<any>(null)

  const handleFixConstraint = async () => {
    setIsLoading(true)
    try {
      const response = await fetch('/api/fix-constraint', {
        method: 'POST',
      })

      const data = await response.json()
      setResult(data)

      toast({
        title: response.ok ? 'Success' : 'Error',
        description: response.ok
          ? 'Database constraint fixed successfully!'
          : `Failed to fix constraint: ${data.error || 'Unknown error'}`,
        variant: response.ok ? 'default' : 'destructive',
      })
    } catch (error) {
      console.error('Error:', error)
      toast({
        title: 'Error',
        description: `Failed to make request: ${error instanceof Error ? error.message : String(error)}`,
        variant: 'destructive',
      })
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="min-h-screen bg-slate-50 pb-16 dark:bg-slate-900">
      <header className="flex items-center justify-between p-4">
        <h1 className="text-xl font-bold text-slate-800 dark:text-slate-100">Fix Database</h1>
      </header>

      <main className="container mx-auto px-4">
        <div className="rounded-lg border bg-white p-6 shadow-sm dark:border-slate-800 dark:bg-slate-950">
          <h2 className="mb-4 text-lg font-semibold">Database Constraint Fix</h2>
          <p className="mb-4 text-slate-600 dark:text-slate-300">
            This will update the <code>chk_role_data</code> constraint to allow students with
            username/password authentication.
          </p>

          <Button
            onClick={handleFixConstraint}
            disabled={isLoading}
            className="bg-indigo-600 text-white hover:bg-indigo-700"
          >
            {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            {isLoading ? 'Fixing...' : 'Fix Constraint'}
          </Button>

          {result && (
            <div className="mt-6">
              <h3 className="text-md mb-2 font-medium">Result:</h3>
              <pre className="rounded-md bg-slate-100 p-4 text-sm dark:bg-slate-800">
                {JSON.stringify(result, null, 2)}
              </pre>
            </div>
          )}
        </div>
      </main>
    </div>
  )
}
