"use client"

import type React from "react"

import { useState, useCallback } from "react"
import { useRout<PERSON> } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { ThemeToggle } from "@/components/theme-toggle"
import { useToast } from "@/hooks/use-toast"
import { Lock, User, AlertCircle } from "lucide-react"
import { PasswordInput } from "@/components/ui/password-input"
import { Alert, AlertDescription } from "@/components/ui/alert"

export default function AdminLogin() {
  const router = useRouter()
  const { toast } = useToast()
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [formData, setFormData] = useState({
    username: "",
    password: "",
  })

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value,
    })
    // Clear error when user starts typing again
    if (error) setError(null)
  }

  // Fungsi untuk melakukan fetch dengan timeout dan retry
  const fetchWithRetry = useCallback(async (url: string, options: RequestInit, maxRetries = 2, timeout = 10000) => {
    let retries = 0;

    while (retries <= maxRetries) {
      try {
        // Buat AbortController untuk timeout
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), timeout);

        const fetchOptions = {
          ...options,
          signal: controller.signal
        };

        // Lakukan fetch request
        const response = await fetch(url, fetchOptions);

        // Clear timeout jika fetch berhasil
        clearTimeout(timeoutId);

        return response;
      } catch (error) {
        retries++;
        ;

        // Jika sudah mencapai batas retry, lempar error
        if (retries > maxRetries) {
          throw error;
        }

        // Tunggu sebelum mencoba lagi (exponential backoff)
        await new Promise(resolve => setTimeout(resolve, 1000 * retries));
      }
    }

    // Fallback jika semua retry gagal (seharusnya tidak pernah sampai sini)
    throw new Error("All fetch attempts failed");
  }, []);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)
    setError(null)

    try {
      const response = await fetchWithRetry("/api/auth/admin/login", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "Cache-Control": "no-cache, no-store, must-revalidate",
          "Pragma": "no-cache",
        },
        body: JSON.stringify(formData),
      }, 1, 10000); // 1 retry, 10 second timeout

      if (!response.ok) {
        let errorData;
        try {
          errorData = await response.json();
        } catch (parseError) {
          console.error("Error parsing error response:", parseError);
          throw new Error(`Login failed with status ${response.status}`);
        }

        if (response.status === 401) {
          throw new Error("Username atau password salah");
        } else {
          throw new Error(errorData.error || "Gagal melakukan login");
        }
      }

      const data = await response.json();

      // Login berhasil
      toast({
        title: "Login berhasil",
        description: `Selamat datang kembali, ${data.admin?.name || 'Admin'}!`,
      });

      // Redirect ke halaman home admin
      router.push("/admin/home");
    } catch (error) {
      console.error("Login error:", error);

      // Tampilkan pesan error yang sesuai
      let errorMessage = "Gagal melakukan login. Silakan coba lagi.";

      if (error instanceof TypeError && error.message.includes("fetch")) {
        errorMessage = "Koneksi terputus. Periksa koneksi internet Anda dan coba lagi.";
      } else if (error instanceof DOMException && error.name === "AbortError") {
        errorMessage = "Permintaan timeout. Server tidak merespons, coba lagi nanti.";
      } else if (error instanceof Error) {
        errorMessage = error.message;
      }

      setError(errorMessage);
    } finally {
      setLoading(false);
    }
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-b from-indigo-50 to-white dark:from-slate-900 dark:to-slate-800 p-4">
      <div className="absolute top-4 right-4">
        <ThemeToggle />
      </div>

      <Card className="w-full max-w-md shadow-md border-indigo-100 dark:border-slate-700">
        <CardHeader className="space-y-1 text-center">
          <CardTitle className="text-2xl font-bold">Masuk ke Akun Admin</CardTitle>
        </CardHeader>
        <CardContent>
          {error && (
            <Alert variant="destructive" className="mb-4">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="username">Username</Label>
              <div className="relative">
                <User className="absolute left-3 top-3 h-4 w-4 text-slate-500" />
                <Input
                  id="username"
                  name="username"
                  placeholder="Masukkan username"
                  className="pl-10"
                  value={formData.username}
                  onChange={handleChange}
                  disabled={loading}
                  required
                />
              </div>
            </div>
            <div className="space-y-2">
              <Label htmlFor="password">Password</Label>
              <div className="relative">
                <Lock className="absolute left-3 top-3 h-4 w-4 text-slate-500 z-10" />
                <PasswordInput
                  id="password"
                  name="password"
                  placeholder="Masukkan password"
                  className="pl-10"
                  value={formData.password}
                  onChange={handleChange}
                  disabled={loading}
                  required
                />
              </div>
            </div>
            <Button
              type="submit"
              className="w-full bg-indigo-600 hover:bg-indigo-700 text-white relative"
              disabled={loading}
            >
              {loading ? (
                <>
                  <span className="opacity-0">Masuk</span>
                  <span className="absolute inset-0 flex items-center justify-center">
                    <svg className="animate-spin h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    <span className="ml-2">Memproses...</span>
                  </span>
                </>
              ) : (
                "Masuk"
              )}
            </Button>
          </form>
        </CardContent>
      </Card>
    </div>
  )
}
