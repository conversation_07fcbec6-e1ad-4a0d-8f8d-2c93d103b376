'use client'

import { useRef, useState, useEffect } from 'react'
import jsQR from 'jsqr'
import { AttendanceType } from '@/lib/domain/entities/absence'
import { Button } from '@/components/ui/button'
import { Repeat, Camera, PauseCircle } from 'lucide-react'

interface QRScannerProps {
  onScan: (code: string, type: AttendanceType) => void
  scanType: AttendanceType
  isActive: boolean
  isPaused: boolean // When true, scanner will not process QR codes
  onError: (error: Error) => void
}

export default function QRScanner({
  onScan,
  scanType,
  isActive,
  isPaused = false,
  onError,
}: QRScannerProps) {
  const videoRef = useRef<HTMLVideoElement>(null)
  const canvasRef = useRef<HTMLCanvasElement>(null)
  const streamRef = useRef<MediaStream | null>(null)
  const scanLoopRef = useRef<number | null>(null)
  const [cameraMode, setCameraMode] = useState<'environment' | 'user'>('environment')
  const [cameraAvailable, setCameraAvailable] = useState<boolean>(true)
  const [isScanning, setIsScanning] = useState<boolean>(false)

  // Store the current scan type in a ref to ensure we always use the latest value
  const currentTypeRef = useRef<AttendanceType>(scanType)
  const isPausedRef = useRef<boolean>(isPaused)

  // Update refs when props change
  useEffect(() => {
    currentTypeRef.current = scanType
    isPausedRef.current = isPaused

    // Update scanning status based on paused state
    setIsScanning(!isPaused && isActive)
  }, [scanType, isPaused, isActive])

  // Watch for isPaused transitions to restart scanning when it goes from paused to unpaused
  useEffect(() => {
    // Only trigger when going from paused to unpaused, while the camera is active
    if (
      !isPaused &&
      isActive &&
      videoRef.current?.readyState === videoRef.current?.HAVE_ENOUGH_DATA
    ) {
      console.log('Scanner unpaused - restarting scan loop')
      // Small delay to ensure state is fully updated
      setTimeout(() => {
        startScanLoop()
      }, 100)
    }
  }, [isPaused, isActive])

  // Camera lifecycle management
  useEffect(() => {
    // If component is active, start camera
    if (isActive) {
      const setupTimeout = setTimeout(() => {
        startCamera()
      }, 300)

      return () => {
        clearTimeout(setupTimeout)
      }
    } else {
      // If component is not active, stop camera
      stopCamera()
    }

    // Cleanup on unmount
    return () => {
      stopCamera()
    }
  }, [isActive])

  // Handle camera mode changes
  useEffect(() => {
    if (isActive && streamRef.current) {
      // Restart camera when mode changes
      stopCamera()
      const restartTimeout = setTimeout(() => {
        startCamera()
      }, 300)

      return () => clearTimeout(restartTimeout)
    }
  }, [cameraMode, isActive])

  // Camera control functions
  const startCamera = async () => {
    try {
      if (streamRef.current) {
        stopCamera()
      }

      // Try exact mode first
      try {
        const stream = await navigator.mediaDevices.getUserMedia({
          video: { facingMode: { exact: cameraMode } },
          audio: false,
        })
        handleCameraStream(stream)
      } catch (exactError) {
        // Fall back to preferred mode
        try {
          const stream = await navigator.mediaDevices.getUserMedia({
            video: { facingMode: cameraMode },
            audio: false,
          })
          handleCameraStream(stream)
        } catch (preferredError) {
          // Last resort - any camera
          const stream = await navigator.mediaDevices.getUserMedia({
            video: true,
            audio: false,
          })
          handleCameraStream(stream)
        }
      }

      setCameraAvailable(true)
    } catch (error) {
      setCameraAvailable(false)
      onError(error instanceof Error ? error : new Error('Failed to start camera'))
    }
  }

  const handleCameraStream = (stream: MediaStream) => {
    streamRef.current = stream

    if (videoRef.current) {
      videoRef.current.srcObject = stream
      // Don't start scanning loop here - wait for video to be ready
    }
  }

  const stopCamera = () => {
    // Cancel any ongoing scan loop
    if (scanLoopRef.current !== null) {
      cancelAnimationFrame(scanLoopRef.current)
      scanLoopRef.current = null
    }

    // Stop all tracks in the stream
    if (streamRef.current) {
      streamRef.current.getTracks().forEach(track => track.stop())
      streamRef.current = null
    }

    // Clear video source
    if (videoRef.current) {
      videoRef.current.srcObject = null
    }

    // Update scanning status
    setIsScanning(false)
  }

  const switchCamera = () => {
    setCameraMode(prevMode => (prevMode === 'environment' ? 'user' : 'environment'))
  }

  // Core scanning function - completely rewritten for better control
  const startScanLoop = () => {
    // Cancel any existing loop
    if (scanLoopRef.current !== null) {
      cancelAnimationFrame(scanLoopRef.current)
      scanLoopRef.current = null
    }

    console.log('Starting QR scan loop')
    setIsScanning(true)

    // Function to process a single frame
    const processScanFrame = () => {
      // Stop scanning if component is not active
      if (!isActive || !videoRef.current || !canvasRef.current) {
        scanLoopRef.current = null
        setIsScanning(false)
        return
      }

      const video = videoRef.current
      const canvas = canvasRef.current
      const context = canvas.getContext('2d')

      // Make sure video is ready
      if (video.readyState !== video.HAVE_ENOUGH_DATA || !context) {
        // Not ready yet, request next frame
        scanLoopRef.current = requestAnimationFrame(processScanFrame)
        return
      }

      // Set canvas dimensions to match video
      canvas.width = video.videoWidth
      canvas.height = video.videoHeight

      // Draw current frame to canvas
      context.drawImage(video, 0, 0, canvas.width, canvas.height)

      // If scanning is paused, just continue the loop without processing QR codes
      if (isPausedRef.current) {
        setIsScanning(false)
        scanLoopRef.current = requestAnimationFrame(processScanFrame)
        return
      }

      // We're actively scanning now
      setIsScanning(true)

      // Get image data for QR code processing
      const imageData = context.getImageData(0, 0, canvas.width, canvas.height)

      // Process with jsQR
      const code = jsQR(imageData.data, imageData.width, imageData.height, {
        inversionAttempts: 'dontInvert',
      })

      // Process QR code if valid
      if (code && code.data && code.data.trim().length > 0) {
        // Check and format UUID
        const formattedCode = formatQRCodeData(code.data)

        if (formattedCode) {
          // Pause scanning immediately to prevent duplicate scans
          isPausedRef.current = true
          setIsScanning(false)

          // Call onScan callback with the code and current scan type
          onScan(formattedCode, currentTypeRef.current)

          // Don't request next frame - scanning will resume when isPaused becomes false
          return
        }
      }

      // Continue scanning loop
      scanLoopRef.current = requestAnimationFrame(processScanFrame)
    }

    // Start the scan loop
    scanLoopRef.current = requestAnimationFrame(processScanFrame)
  }

  // Helper to format QR code data
  const formatQRCodeData = (data: string): string | null => {
    // Standard UUID format
    const strictUuidPattern = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i
    if (strictUuidPattern.test(data)) {
      return data
    }

    // UUID without hyphens
    const uuidWithoutHyphens = /^[0-9a-f]{32}$/i
    if (uuidWithoutHyphens.test(data)) {
      // Format as UUID with hyphens
      return `${data.substring(0, 8)}-${data.substring(8, 12)}-${data.substring(12, 16)}-${data.substring(16, 20)}-${data.substring(20)}`
    }

    // UUID with extra characters
    const uuidWithExtraChars =
      /.*?([0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}).*?/i
    const match = data.match(uuidWithExtraChars)
    if (match && match[1]) {
      return match[1]
    }

    return null
  }

  // Start scanning when video is ready
  useEffect(() => {
    // Use the video element's events to control scan loop
    const video = videoRef.current

    // Don't set up listeners if component is not active
    if (!video || !isActive) return

    const handleCanPlay = () => {
      if (isActive) {
        startScanLoop()
      }
    }

    // Add event listener
    video.addEventListener('canplay', handleCanPlay)

    // Clean up
    return () => {
      video.removeEventListener('canplay', handleCanPlay)
      if (scanLoopRef.current !== null) {
        cancelAnimationFrame(scanLoopRef.current)
      }
    }
  }, [isActive, videoRef.current])

  return (
    <div className="relative h-full w-full">
      {/* Camera Status Message */}
      {!cameraAvailable && (
        <div className="absolute inset-0 z-20 flex items-center justify-center bg-black bg-opacity-80">
          <div className="p-4 text-center text-white">
            <p className="mb-2">Tidak dapat mengakses kamera.</p>
            <p className="text-sm">Pastikan browser Anda memiliki izin untuk mengakses kamera.</p>
          </div>
        </div>
      )}

      {/* Pause Status Message */}
      {isPaused && (
        <div className="absolute inset-0 z-20 flex items-center justify-center bg-black bg-opacity-60">
          <div className="rounded-lg bg-black bg-opacity-70 p-4 text-center text-white">
            <PauseCircle className="mx-auto mb-2 h-10 w-10 text-indigo-400" />
            <p className="text-lg font-medium">Scanner Dijeda</p>
            <p className="text-sm">Tunggu hingga dialog tertutup</p>
          </div>
        </div>
      )}

      {/* Scanning Indicator */}
      {isScanning && (
        <div className="absolute left-4 top-4 z-20 animate-pulse rounded-full bg-green-500 p-2 shadow-md">
          <Camera className="h-5 w-5 text-white" />
        </div>
      )}

      <video
        ref={videoRef}
        autoPlay={true}
        playsInline
        muted
        disablePictureInPicture
        disableRemotePlayback
        className="absolute inset-0 h-full w-full object-cover"
        onError={e => {
          onError(
            new Error('Video element error: ' + (e.currentTarget.error?.message || 'Unknown error'))
          )
        }}
      />
      <canvas
        ref={canvasRef}
        className="hidden" // Hidden canvas for processing
      />
      <div className="pointer-events-none absolute inset-0 z-10 border-2 border-dashed border-indigo-500 opacity-70">
        <div className="absolute left-0 top-0 h-20 w-20 border-l-4 border-t-4 border-indigo-500"></div>
        <div className="absolute right-0 top-0 h-20 w-20 border-r-4 border-t-4 border-indigo-500"></div>
        <div className="absolute bottom-0 left-0 h-20 w-20 border-b-4 border-l-4 border-indigo-500"></div>
        <div className="absolute bottom-0 right-0 h-20 w-20 border-b-4 border-r-4 border-indigo-500"></div>
      </div>

      {/* Camera Switch Button */}
      {isActive && cameraAvailable && (
        <Button
          variant="outline"
          size="icon"
          onClick={switchCamera}
          className="absolute bottom-4 right-4 z-20 rounded-full bg-white/80 hover:bg-white dark:bg-black/80 dark:hover:bg-black"
        >
          <Repeat className="h-4 w-4" />
          <span className="sr-only">Ganti Kamera</span>
        </Button>
      )}
    </div>
  )
}
