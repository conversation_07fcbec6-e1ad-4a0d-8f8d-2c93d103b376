'use client'

import { useState, useEffect, useRef } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { useToast } from '@/components/ui/use-toast'
import { Camera, CheckCircle, Clock, Calendar, Loader2, AlertCircle } from 'lucide-react'
import { ThemeToggle } from '@/components/theme-toggle'
import { AdminBottomNav } from '@/components/admin-bottom-nav'
import { AttendanceType } from '@/lib/domain/entities/absence'
import { getCurrentDateTime } from '@/lib/utils/date'
import QRScanner from './QRScanner'
import { useAdminSession } from '@/hooks/use-admin-session'

// Student interface for reference
interface ScanResult {
  uniqueCode: string
  name: string
  className?: string
  nis?: string | null
}

// All possible states for the scanner
type ScannerState =
  | 'INACTIVE' // Camera is off
  | 'SCANNING' // Actively scanning for QR codes
  | 'PROCESSING' // Processing a scanned code
  | 'PAUSED' // Temporarily paused (dialog open)

export default function AdminHome() {
  const { toast } = useToast()
  const [scanType, setScanType] = useState<AttendanceType>(AttendanceType.ZUHR)
  const [scannerState, setScannerState] = useState<ScannerState>('SCANNING')
  const [scanResult, setScanResult] = useState<ScanResult | null>(null)
  const [showDialog, setShowDialog] = useState(false)
  const [currentTime, setCurrentTime] = useState('')
  const [currentDate, setCurrentDate] = useState('')
  const [isDuplicate, setIsDuplicate] = useState(false)
  const [cameraActive, setCameraActive] = useState(true)
  const [showSuccessDialog, setShowSuccessDialog] = useState(false)
  const [successMessage, setSuccessMessage] = useState({
    title: '',
    description: '',
    status: 'success' as 'success' | 'error',
  })
  const [processingType, setProcessingType] = useState<AttendanceType>(scanType)
  const [showIjinConfirmDialog, setShowIjinConfirmDialog] = useState(false)
  const [pendingIjinScan, setPendingIjinScan] = useState<{
    uniqueCode: string
    studentData: any
  } | null>(null)
  const { admin } = useAdminSession()

  // Function to play alert sound for notifications
  const playAlertSound = () => {
    // Only run in browser environment
    if (typeof window === 'undefined') return

    try {
      const audio = new Audio('/alert.mp3')

      // Play the sound
      audio
        .play()
        .then(() => {
          // Success - audio is playing
        })
        .catch(err => {
          // Try an alternative approach for browsers that block autoplay
          document.addEventListener(
            'click',
            function playOnClick() {
              audio.play()
              document.removeEventListener('click', playOnClick)
            },
            { once: true }
          )
        })
    } catch (error) {
      // Handle any errors silently
    }
  }

  useEffect(() => {
    const updateDateTime = () => {
      const { date, time } = getCurrentDateTime()
      setCurrentDate(date)
      setCurrentTime(time)
    }

    updateDateTime()
    const interval = setInterval(updateDateTime, 60000)
    return () => clearInterval(interval)
  }, [])

  const isScannerPaused = scannerState !== 'SCANNING'

  const setScannerStateSafe = (newState: ScannerState) => {
    if (newState === scannerState) return

    setScannerState(newState)
  }

  const handleQRCodeScanned = async (code: string, type: AttendanceType) => {
    if (scannerState !== 'SCANNING') {
      return
    }

    // Important: Set the state to PROCESSING to prevent duplicate scans
    setScannerStateSafe('PROCESSING')

    setProcessingType(type)

    try {
      setIsLoading(true)
      setLoadingMessage('Memproses kode QR...')

      await processScannedCode(code, type)
    } catch (error) {
      setSuccessMessage({
        title: 'Error Pemindaian',
        description:
          error instanceof Error ? error.message : 'Gagal memproses kode QR. Silakan coba lagi.',
        status: 'error',
      })

      setIsLoading(false)

      setShowSuccessDialog(true)

      // Set to PAUSED instead of processing
      setScannerStateSafe('PAUSED')
    }
  }

  const processScannedCode = async (uniqueCode: string, type: AttendanceType) => {
    // If this is an IJIN scan, show confirmation dialog first
    if (type === AttendanceType.IJIN) {
      const studentResponse = await fetch(`/api/student?uniqueCode=${uniqueCode}`)

      if (!studentResponse.ok) {
        setIsLoading(false)

        setSuccessMessage({
          title: 'Siswa tidak ditemukan',
          description:
            'Kode QR tidak terdaftar atau tidak valid. Pastikan siswa sudah terdaftar di sistem.',
          status: 'error',
        })

        setShowSuccessDialog(true)
        setScannerStateSafe('PAUSED')
        return
      }

      const studentData = await studentResponse.json()

      // Store the pending scan information
      setPendingIjinScan({
        uniqueCode,
        studentData,
      })

      // Show confirmation dialog and pause scanner
      setShowIjinConfirmDialog(true)
      setScannerStateSafe('PAUSED')
      return
    }

    // For other scan types, proceed as normal
    const checkResponse = await fetch('/api/absence/check', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ uniqueCode, type }),
    })

    const checkData = await checkResponse.json()

    setLoadingMessage('Mengambil informasi siswa...')

    const studentResponse = await fetch(`/api/student?uniqueCode=${uniqueCode}`)

    if (!studentResponse.ok) {
      setIsLoading(false)

      setSuccessMessage({
        title: 'Siswa tidak ditemukan',
        description:
          'Kode QR tidak terdaftar atau tidak valid. Pastikan siswa sudah terdaftar di sistem.',
        status: 'error',
      })

      setShowSuccessDialog(true)

      setScannerStateSafe('PAUSED')
      return
    }

    const studentData = await studentResponse.json()

    if (checkData.isDuplicate) {
      setIsLoading(false)

      setScanResult({
        uniqueCode: studentData.uniqueCode,
        name: studentData.name,
        className: studentData.className,
        nis: studentData.nis,
      })

      setIsDuplicate(true)

      // Play sound only for duplicate "Pulang" (Dismissal)
      if (type === AttendanceType.DISMISSAL) {
        playAlertSound()
      }

      setShowDialog(true)

      setScannerStateSafe('PAUSED')
    } else {
      setLoadingMessage('Merekam kehadiran...')

      const attendanceResult = await recordAttendance(uniqueCode, false, type)

      setScanResult({
        uniqueCode: studentData.uniqueCode,
        name: studentData.name,
        className: studentData.className,
        nis: studentData.nis,
      })

      setIsLoading(false)

      if (attendanceResult.success) {
        setSuccessMessage({
          title: 'Absensi berhasil',
          description: `${studentData.name} telah diabsen untuk ${getScanTypeName(type)}`,
          status: 'success',
        })
      } else {
        if (
          type === AttendanceType.DISMISSAL &&
          attendanceResult.message &&
          (attendanceResult.message.toLowerCase().includes('zuhur') ||
            attendanceResult.message.toLowerCase().includes('asr') ||
            attendanceResult.message.toLowerCase().includes('ashar'))
        ) {
          playAlertSound()
        }

        setSuccessMessage({
          title: 'Tidak dapat melakukan absensi',
          description: attendanceResult.message,
          status: 'error',
        })
      }

      setShowSuccessDialog(true)

      setScannerStateSafe('PAUSED')
    }
  }

  const recordAttendance = async (
    uniqueCode: string,
    force: boolean = false,
    attendanceType?: AttendanceType
  ) => {
    const typeToUse = attendanceType || processingType

    try {
      const response = await fetch('/api/absence/record', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          uniqueCode,
          type: typeToUse,
          force,
        }),
      })

      const data = await response.json()

      if (!response.ok) {
        return {
          success: false,
          message: data.message || data.error || 'Gagal merekam kehadiran',
          code: data.code,
        }
      }

      return { success: true, data }
    } catch (error) {
      return {
        success: false,
        message: 'Gagal terhubung ke server. Periksa koneksi internet Anda.',
        code: 'NETWORK_ERROR',
      }
    }
  }

  const handleConfirmDuplicate = async () => {
    if (!scanResult) return

    try {
      setIsLoading(true)
      setLoadingMessage('Memperbarui kehadiran...')

      setShowDialog(false)

      const currentType = processingType

      const attendanceResult = await recordAttendance(scanResult.uniqueCode, true, currentType)

      setIsLoading(false)

      setIsDuplicate(false)

      if (attendanceResult.success) {
        setSuccessMessage({
          title: 'Absensi diperbarui',
          description: `${scanResult.name} telah diabsen ulang untuk ${getScanTypeName(currentType)}`,
          status: 'success',
        })
      } else {
        // Check for the special case: User trying to absen pulang without prior prayers
        if (
          currentType === AttendanceType.DISMISSAL &&
          attendanceResult.message &&
          (attendanceResult.message.toLowerCase().includes('zuhur') ||
            attendanceResult.message.toLowerCase().includes('asr') ||
            attendanceResult.message.toLowerCase().includes('ashar'))
        ) {
          playAlertSound()
        }

        setSuccessMessage({
          title: 'Tidak dapat memperbarui absensi',
          description: attendanceResult.message,
          status: 'error',
        })
      }

      setShowSuccessDialog(true)

      setScannerStateSafe('PAUSED')
    } catch (error) {
      setIsLoading(false)

      toast({
        title: 'Error',
        description: 'Terjadi kesalahan tak terduga. Silakan coba lagi.',
        variant: 'destructive',
      })

      setScannerStateSafe('SCANNING')
    }
  }

  const handleStartScan = () => {
    setCameraActive(true)
    setScannerStateSafe('SCANNING')
  }

  const getScanTypeName = (type: AttendanceType) => {
    switch (type) {
      case AttendanceType.ZUHR:
        return 'Shalat Zuhur'
      case AttendanceType.ASR:
        return 'Shalat Asr'
      case AttendanceType.DISMISSAL:
        return 'Pulang'
      case AttendanceType.IJIN:
        return 'Ijin (Bebas Shalat)'
      default:
        return type
    }
  }

  const forceRestartScanner = () => {
    // First, tell the scanner it should be in scanning mode
    setScannerStateSafe('SCANNING')

    // Make sure camera is active
    setCameraActive(true)

    // When closing dialog, add a small delay to ensure state is updated then force a re-render
    setTimeout(() => {
      // This will trigger a re-render and force the scanner to restart
      setCameraActive(false)
      setTimeout(() => {
        setCameraActive(true)
      }, 50)
    }, 100)
  }

  const handleDialogClose = (dialogType: 'duplicate' | 'success' | 'loading') => {
    // Close the appropriate dialog first
    switch (dialogType) {
      case 'duplicate':
        setShowDialog(false)
        // If user closes dialog without confirming, clear scan result
        if (isDuplicate) {
          setScanResult(null)
          setIsDuplicate(false)
        }
        break

      case 'success':
        setShowSuccessDialog(false)
        // Clear scan result if it was an error
        if (successMessage.status === 'error') {
          setScanResult(null)
        }
        break

      case 'loading':
        setIsLoading(false)
        break
    }

    // Now restart the scanner
    forceRestartScanner()
  }

  const [isLoading, setIsLoading] = useState(false)
  const [loadingMessage, setLoadingMessage] = useState('')

  const handleConfirmIjin = async (confirmed: boolean) => {
    console.log(`Ijin confirmation: ${confirmed ? 'Yes' : 'No'}`)

    // Close the dialog
    setShowIjinConfirmDialog(false)

    // If not confirmed, just resume scanning
    if (!confirmed || !pendingIjinScan) {
      console.log('Resuming scanner after Ijin rejection')
      // Reset scanner state to scanning and clear loading state
      setIsLoading(false)
      setLoadingMessage('')
      setPendingIjinScan(null)
      setScannerStateSafe('SCANNING')

      // Beri waktu untuk dialog tertutup sebelum restart scanner
      setTimeout(() => {
        console.log('Forcing scanner restart in handleConfirmIjin')
        forceRestartScanner()
      }, 300)
      return
    }

    // If confirmed, proceed with the Ijin scan processing
    try {
      setIsLoading(true)
      setLoadingMessage('Merekam kehadiran...')

      const { uniqueCode, studentData } = pendingIjinScan

      // Record the Ijin attendance
      const attendanceResult = await recordAttendance(uniqueCode, false, AttendanceType.IJIN)

      setScanResult({
        uniqueCode: studentData.uniqueCode,
        name: studentData.name,
        className: studentData.className,
        nis: studentData.nis,
      })

      setIsLoading(false)

      if (attendanceResult.success) {
        setSuccessMessage({
          title: 'Ijin berhasil',
          description: `${studentData.name} telah diberikan ijin untuk tidak mengikuti shalat hari ini`,
          status: 'success',
        })
      } else {
        setSuccessMessage({
          title: 'Tidak dapat memberikan ijin',
          description: attendanceResult.message,
          status: 'error',
        })
      }

      setShowSuccessDialog(true)
      setScannerStateSafe('PAUSED')
    } catch (error) {
      setIsLoading(false)

      setSuccessMessage({
        title: 'Error Pemindaian',
        description:
          error instanceof Error ? error.message : 'Gagal memproses kode QR. Silakan coba lagi.',
        status: 'error',
      })

      setShowSuccessDialog(true)
      setScannerStateSafe('PAUSED')
    } finally {
      // Clear the pending scan
      setPendingIjinScan(null)
    }
  }

  return (
    <div className="min-h-screen bg-slate-50 pb-16 dark:bg-slate-900">
      <header className="flex items-center justify-between p-4">
        <h1 className="text-xl font-bold text-slate-800 dark:text-slate-100">Scanner Admin</h1>
        <ThemeToggle />
      </header>

      <main className="container mx-auto px-4">
        <div className="mb-4 flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Calendar className="h-4 w-4 text-slate-500" />
            <span className="text-sm text-slate-500 dark:text-slate-400">{currentDate}</span>
          </div>
          <div className="flex items-center gap-2">
            <Clock className="h-4 w-4 text-slate-500" />
            <span className="text-sm text-slate-500 dark:text-slate-400">{currentTime} WITA</span>
          </div>
        </div>

        <div className="mb-6 space-y-6">
          <div className="space-y-2">
            <label className="text-sm font-medium text-slate-700 dark:text-slate-300">
              Tipe Absensi
            </label>
            <Select value={scanType} onValueChange={value => setScanType(value as AttendanceType)}>
              <SelectTrigger className="w-full">
                <SelectValue placeholder="Pilih tipe absensi" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value={AttendanceType.ZUHR}>Shalat Zuhur</SelectItem>
                <SelectItem value={AttendanceType.ASR}>Shalat Asr</SelectItem>
                <SelectItem value={AttendanceType.DISMISSAL}>Pulang</SelectItem>
                <SelectItem value={AttendanceType.IJIN}>Ijin (Bebas Shalat)</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <Card className="mt-4 border border-dashed border-indigo-600 dark:border-indigo-600">
            <CardContent className="relative flex min-h-[300px] flex-col items-center justify-center bg-slate-100 p-6 dark:bg-slate-800">
              {cameraActive ? (
                <div className="absolute inset-0">
                  <QRScanner
                    onScan={handleQRCodeScanned}
                    scanType={scanType}
                    isActive={cameraActive}
                    isPaused={isScannerPaused}
                    onError={error => {
                      toast({
                        title: 'Error',
                        description: 'Tidak dapat mengakses kamera.',
                        variant: 'destructive',
                      })
                      setCameraActive(false)
                      setScannerStateSafe('INACTIVE')
                    }}
                  />
                </div>
              ) : (
                <>
                  <Camera className="mb-4 h-16 w-16 text-slate-400 dark:text-slate-500" />
                  <Button
                    onClick={handleStartScan}
                    className="bg-indigo-600 text-white hover:bg-indigo-700"
                  >
                    Mulai Pemindaian
                  </Button>
                </>
              )}
            </CardContent>
          </Card>
        </div>
      </main>

      <AdminBottomNav activeTab="home" adminRole={admin?.role} />

      <Dialog
        open={showDialog}
        onOpenChange={open => {
          if (!open) {
            // When dialog is being closed
            setTimeout(() => {
              handleDialogClose('duplicate')
            }, 100)
          }
        }}
      >
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Absensi Ganda Terdeteksi</DialogTitle>
            <DialogDescription>
              {scanResult?.name} (Kode: {scanResult?.uniqueCode.substring(0, 8)}...) sudah melakukan
              absensi untuk {getScanTypeName(processingType)} hari ini. Apakah Anda ingin
              memperbarui data absensi?
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={() => handleDialogClose('duplicate')}>
              Batal
            </Button>
            <Button
              className="bg-indigo-600 text-white hover:bg-indigo-700"
              onClick={handleConfirmDuplicate}
            >
              Lanjutkan
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      <Dialog
        open={showSuccessDialog}
        onOpenChange={open => {
          if (!open) {
            // When dialog is being closed
            setTimeout(() => {
              handleDialogClose('success')
            }, 100)
          }
        }}
      >
        <DialogContent
          className={`sm:max-w-md ${successMessage.status === 'error' ? 'border-red-500' : 'border-green-500'}`}
        >
          <DialogHeader>
            <DialogTitle
              className={successMessage.status === 'error' ? 'text-red-600' : 'text-green-600'}
            >
              {successMessage.status === 'error' && (
                <AlertCircle className="mr-2 inline-block h-5 w-5" />
              )}
              {successMessage.status === 'success' && (
                <CheckCircle className="mr-2 inline-block h-5 w-5" />
              )}
              {successMessage.title}
            </DialogTitle>
            <DialogDescription>{successMessage.description}</DialogDescription>
          </DialogHeader>

          {scanResult && successMessage.status === 'success' && (
            <div className="mt-2 border-t border-slate-200 pt-4 dark:border-slate-700">
              <div className="flex flex-col space-y-2">
                <div className="flex justify-between">
                  <span className="text-sm font-medium text-slate-500 dark:text-slate-400">
                    Nama:
                  </span>
                  <span className="text-sm font-medium text-slate-800 dark:text-slate-200">
                    {scanResult.name}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm font-medium text-slate-500 dark:text-slate-400">
                    Kode:
                  </span>
                  <span className="text-sm font-medium text-slate-800 dark:text-slate-200">
                    {scanResult.uniqueCode.substring(0, 8)}...
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm font-medium text-slate-500 dark:text-slate-400">
                    Kelas:
                  </span>
                  <span className="text-sm font-medium text-slate-800 dark:text-slate-200">
                    {scanResult.className}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm font-medium text-slate-500 dark:text-slate-400">
                    Waktu:
                  </span>
                  <span className="text-sm font-medium text-slate-800 dark:text-slate-200">
                    {currentDate}, {currentTime} WITA
                  </span>
                </div>
              </div>
            </div>
          )}

          <DialogFooter className="mt-4">
            <Button
              onClick={() => handleDialogClose('success')}
              className={
                successMessage.status === 'error'
                  ? 'bg-red-600 text-white hover:bg-red-700'
                  : 'bg-green-600 text-white hover:bg-green-700'
              }
            >
              OK
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      <Dialog open={isLoading} onOpenChange={() => {}}>
        <DialogContent className="sm:max-w-md">
          <div className="flex flex-col items-center justify-center py-6">
            <Loader2 className="mb-4 h-10 w-10 animate-spin text-indigo-600" />
            <DialogTitle className="mb-2 text-center">Mohon Tunggu</DialogTitle>
            <DialogDescription className="text-center">{loadingMessage}</DialogDescription>
          </div>
        </DialogContent>
      </Dialog>

      <Dialog
        open={showIjinConfirmDialog}
        onOpenChange={open => {
          // Saat dialog ditutup tanpa pilihan (klik diluar atau escape key)
          if (!open) {
            // Handle seperti tombol "Tidak" ditekan
            console.log('Dialog closed via escape/outside click')
            setShowIjinConfirmDialog(false)
            setIsLoading(false) // Pastikan dialog loading tertutup
            setPendingIjinScan(null)
            setScannerStateSafe('SCANNING')

            // Beri waktu semua state untuk update
            setTimeout(() => {
              forceRestartScanner()
            }, 200)
          }
        }}
      >
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle className="text-amber-600 dark:text-amber-500">
              Konfirmasi Ijin
            </DialogTitle>
            <DialogDescription>
              Apakah Anda yakin akan memberikan ijin untuk tidak mengikuti shalat hari ini?
            </DialogDescription>
          </DialogHeader>

          {pendingIjinScan && (
            <div className="mt-2 border-t border-slate-200 pt-4 dark:border-slate-700">
              <div className="flex flex-col space-y-2">
                <div className="flex justify-between">
                  <span className="text-sm font-medium text-slate-500 dark:text-slate-400">
                    Nama:
                  </span>
                  <span className="text-sm font-medium text-slate-800 dark:text-slate-200">
                    {pendingIjinScan.studentData.name}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm font-medium text-slate-500 dark:text-slate-400">
                    Kelas:
                  </span>
                  <span className="text-sm font-medium text-slate-800 dark:text-slate-200">
                    {pendingIjinScan.studentData.className}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm font-medium text-slate-500 dark:text-slate-400">
                    Jenis Ijin:
                  </span>
                  <span className="text-sm font-medium text-amber-600 dark:text-amber-500">
                    Pembebasan Shalat
                  </span>
                </div>
              </div>

              <div className="mt-4 rounded-md bg-amber-50 p-3 dark:bg-amber-900/20">
                <p className="text-xs text-amber-800 dark:text-amber-300">
                  <strong>Perhatian:</strong> Dengan memberikan ijin ini, siswa akan dibebaskan dari
                  kewajiban shalat Zuhur dan Asr hari ini dan tetap dapat melakukan absen pulang.
                </p>
              </div>
            </div>
          )}

          <DialogFooter className="mt-4 flex space-x-2">
            <Button
              variant="outline"
              onClick={() => {
                console.log('Tidak button clicked')
                setShowIjinConfirmDialog(false)
                // Pastikan dialog loading tertutup
                setIsLoading(false)
                setLoadingMessage('')
                setPendingIjinScan(null)

                // Reset scanner state
                setScannerStateSafe('SCANNING')

                // Beri waktu untuk dialog tertutup sebelum restart scanner
                setTimeout(() => {
                  console.log('Forcing scanner restart after Tidak')
                  forceRestartScanner()
                }, 300)
              }}
              className="flex-1"
            >
              Tidak
            </Button>
            <Button
              onClick={() => {
                console.log('Ya button clicked')
                handleConfirmIjin(true)
              }}
              className="flex-1 bg-amber-600 text-white hover:bg-amber-700"
            >
              Ya, Berikan Ijin
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}
