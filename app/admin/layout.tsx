"use client"

import type React from "react"
import { AdminLayout } from "@/components/layouts/admin-layout"
import { usePathname } from "next/navigation"

// Wrapper component to conditionally apply layout
export default function AdminRootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  // This is a client component that will check the path
  return <AdminLayoutWrapper>{children}</AdminLayoutWrapper>
}

function AdminLayoutWrapper({ children }: { children: React.ReactNode }) {
  const pathname = usePathname()

  // Don't apply layout to login page
  if (pathname === "/admin") {
    return <>{children}</>
  }

  // Apply layout to all other admin pages
  return <AdminLayout>{children}</AdminLayout>
}
