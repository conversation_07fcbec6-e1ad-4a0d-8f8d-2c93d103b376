"use client"

import { useEffect } from "react"
import { useRouter } from "next/navigation"

export default function ScannerRedirect() {
  const router = useRouter()

  useEffect(() => {
    // Redirect to the new scanner location
    router.replace("/admin/home")
  }, [router])

  return (
    <div className="min-h-screen flex items-center justify-center bg-slate-50 dark:bg-slate-900">
      <div className="text-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600 mx-auto mb-4"></div>
        <p className="text-slate-500 dark:text-slate-400">Redirecting to scanner...</p>
      </div>
    </div>
  )
}
