"use client"

import { useState } from "react"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { ThemeToggle } from "@/components/theme-toggle"
import { AdminBottomNav } from "@/components/admin-bottom-nav"
import { Calendar, Clock, Users, CheckCircle, AlertTriangle } from "lucide-react"

export default function AdminDashboard() {
  const [period, setPeriod] = useState("today")

  // Mock data for charts
  const attendanceData = {
    zuhur: {
      present: 42,
      absent: 8,
      percentage: 84,
    },
    asr: {
      present: 38,
      absent: 12,
      percentage: 76,
    },
    dismissal: {
      present: 45,
      absent: 5,
      percentage: 90,
    },
  }

  // Mock data for classes
  const classData = [
    { name: "XII IPA 1", zuhur: 95, asr: 90, dismissal: 100 },
    { name: "XII IPA 2", zuhur: 85, asr: 80, dismissal: 90 },
    { name: "XII IPS 1", zuhur: 75, asr: 65, dismissal: 85 },
  ]

  // Mock data for recent activity
  const recentActivity = [
    { time: "14:30", name: "Ahmad Fauzi", action: "Absen Asr", class: "XII IPA 1" },
    { time: "14:28", name: "Siti Aminah", action: "Absen Asr", class: "XII IPA 1" },
    { time: "14:25", name: "Muhammad Rizki", action: "Absen Asr", class: "XII IPA 2" },
    { time: "12:20", name: "Anisa Putri", action: "Absen Zuhur", class: "XII IPS 1" },
    { time: "12:18", name: "Dimas Pratama", action: "Absen Zuhur", class: "XII IPS 1" },
  ]

  return (
    <div className="min-h-screen bg-slate-50 dark:bg-slate-900 pb-16">
      <header className="p-4 flex justify-between items-center">
        <h1 className="text-xl font-bold text-slate-800 dark:text-slate-100">Dashboard</h1>
        <ThemeToggle />
      </header>

      <main className="container mx-auto px-4">
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center gap-2">
            <Calendar className="h-4 w-4 text-slate-500" />
            <span className="text-sm text-slate-500 dark:text-slate-400">29 Apr 2025</span>
          </div>
          <Select value={period} onValueChange={setPeriod}>
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="Pilih periode" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="today">Hari Ini</SelectItem>
              <SelectItem value="week">Minggu Ini</SelectItem>
              <SelectItem value="month">Bulan Ini</SelectItem>
              <SelectItem value="semester">Semester Ini</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-lg flex items-center gap-2">
                <Users className="h-5 w-5 text-indigo-600" />
                Total Siswa
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold">50</div>
              <p className="text-sm text-slate-500 dark:text-slate-400">Siswa terdaftar</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-lg flex items-center gap-2">
                <CheckCircle className="h-5 w-5 text-green-600" />
                Kehadiran Tertinggi
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold">90%</div>
              <p className="text-sm text-slate-500 dark:text-slate-400">Pulang</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-lg flex items-center gap-2">
                <AlertTriangle className="h-5 w-5 text-yellow-600" />
                Kehadiran Terendah
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold">76%</div>
              <p className="text-sm text-slate-500 dark:text-slate-400">Shalat Asr</p>
            </CardContent>
          </Card>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-lg">Persentase Kehadiran</CardTitle>
            </CardHeader>
            <CardContent>
              <Tabs defaultValue="zuhur">
                <TabsList className="grid w-full grid-cols-3">
                  <TabsTrigger value="zuhur">Zuhur</TabsTrigger>
                  <TabsTrigger value="asr">Asr</TabsTrigger>
                  <TabsTrigger value="dismissal">Pulang</TabsTrigger>
                </TabsList>

                <TabsContent value="zuhur" className="pt-4">
                  <div className="flex items-center justify-between mb-2">
                    <div>
                      <div className="text-2xl font-bold">{attendanceData.zuhur.percentage}%</div>
                      <p className="text-sm text-slate-500 dark:text-slate-400">Kehadiran</p>
                    </div>
                    <div className="text-right">
                      <div className="text-sm font-medium">{attendanceData.zuhur.present} hadir</div>
                      <div className="text-sm text-slate-500 dark:text-slate-400">
                        {attendanceData.zuhur.absent} tidak hadir
                      </div>
                    </div>
                  </div>
                  <div className="w-full bg-slate-200 dark:bg-slate-700 rounded-full h-2.5">
                    <div
                      className="bg-indigo-600 h-2.5 rounded-full"
                      style={{ width: `${attendanceData.zuhur.percentage}%` }}
                    ></div>
                  </div>
                </TabsContent>

                <TabsContent value="asr" className="pt-4">
                  <div className="flex items-center justify-between mb-2">
                    <div>
                      <div className="text-2xl font-bold">{attendanceData.asr.percentage}%</div>
                      <p className="text-sm text-slate-500 dark:text-slate-400">Kehadiran</p>
                    </div>
                    <div className="text-right">
                      <div className="text-sm font-medium">{attendanceData.asr.present} hadir</div>
                      <div className="text-sm text-slate-500 dark:text-slate-400">
                        {attendanceData.asr.absent} tidak hadir
                      </div>
                    </div>
                  </div>
                  <div className="w-full bg-slate-200 dark:bg-slate-700 rounded-full h-2.5">
                    <div
                      className="bg-indigo-600 h-2.5 rounded-full"
                      style={{ width: `${attendanceData.asr.percentage}%` }}
                    ></div>
                  </div>
                </TabsContent>

                <TabsContent value="dismissal" className="pt-4">
                  <div className="flex items-center justify-between mb-2">
                    <div>
                      <div className="text-2xl font-bold">{attendanceData.dismissal.percentage}%</div>
                      <p className="text-sm text-slate-500 dark:text-slate-400">Kehadiran</p>
                    </div>
                    <div className="text-right">
                      <div className="text-sm font-medium">{attendanceData.dismissal.present} hadir</div>
                      <div className="text-sm text-slate-500 dark:text-slate-400">
                        {attendanceData.dismissal.absent} tidak hadir
                      </div>
                    </div>
                  </div>
                  <div className="w-full bg-slate-200 dark:bg-slate-700 rounded-full h-2.5">
                    <div
                      className="bg-indigo-600 h-2.5 rounded-full"
                      style={{ width: `${attendanceData.dismissal.percentage}%` }}
                    ></div>
                  </div>
                </TabsContent>
              </Tabs>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-lg">Kehadiran per Kelas</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {classData.map((cls, index) => (
                  <div key={index}>
                    <div className="flex items-center justify-between mb-1">
                      <div className="font-medium">{cls.name}</div>
                      <div className="text-sm text-slate-500 dark:text-slate-400">
                        Z: {cls.zuhur}% | A: {cls.asr}% | P: {cls.dismissal}%
                      </div>
                    </div>
                    <div className="w-full flex h-2 gap-0.5">
                      <div className="bg-indigo-600 rounded-l-full" style={{ width: `${cls.zuhur / 3}%` }}></div>
                      <div className="bg-teal-500" style={{ width: `${cls.asr / 3}%` }}></div>
                      <div className="bg-green-500 rounded-r-full" style={{ width: `${cls.dismissal / 3}%` }}></div>
                    </div>
                  </div>
                ))}
              </div>
              <div className="flex items-center justify-between text-xs text-slate-500 dark:text-slate-400 mt-4">
                <div className="flex items-center gap-1">
                  <div className="w-2 h-2 bg-indigo-600 rounded-full"></div>
                  <span>Zuhur</span>
                </div>
                <div className="flex items-center gap-1">
                  <div className="w-2 h-2 bg-teal-500 rounded-full"></div>
                  <span>Asr</span>
                </div>
                <div className="flex items-center gap-1">
                  <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                  <span>Pulang</span>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-lg">Aktivitas Terbaru</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {recentActivity.map((activity, index) => (
                <div
                  key={index}
                  className="flex items-center gap-3 pb-3 border-b border-slate-200 dark:border-slate-700 last:border-0 last:pb-0"
                >
                  <div className="bg-slate-100 dark:bg-slate-800 p-2 rounded-full">
                    <Clock className="h-4 w-4 text-slate-500" />
                  </div>
                  <div className="flex-1">
                    <div className="flex items-center justify-between">
                      <p className="font-medium">{activity.name}</p>
                      <p className="text-xs text-slate-500 dark:text-slate-400">{activity.time} WITA</p>
                    </div>
                    <p className="text-sm text-slate-500 dark:text-slate-400">
                      {activity.action} • {activity.class}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </main>

      {/* <AdminBottomNav activeTab="dashboard" /> */}
    </div>
  )
}
