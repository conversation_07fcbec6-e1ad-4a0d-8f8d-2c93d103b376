/**
 * Test script untuk memverifikasi fix auto-logout issue
 * Script ini akan test session monitoring dan memastikan tidak ada auto-logout yang tidak diinginkan
 */

const BASE_URL = 'http://localhost:3000'

async function testSessionFlow() {
  console.log('🧪 Testing Auto-Logout Fix...\n')

  try {
    // Test 1: Check session events API behavior
    console.log('1️⃣ Testing session events API without authentication...')
    const eventsResponse = await fetch(`${BASE_URL}/api/auth/session-events`, {
      method: 'GET',
      headers: {
        'Cache-Control': 'no-cache',
      },
    })

    console.log(`   Status: ${eventsResponse.status}`)
    const eventsData = await eventsResponse.json()
    console.log(`   Response:`, eventsData)
    
    if (eventsResponse.status === 200 && eventsData.hasInvalidationEvents === false) {
      console.log('   ✅ PASS: Session events API returns 200 for auth failures (no invalidation events)')
    } else {
      console.log('   ❌ FAIL: Session events API should return 200 with hasInvalidationEvents: false')
    }

    // Test 2: Check debug endpoint
    console.log('\n2️⃣ Testing debug session status endpoint...')
    const debugResponse = await fetch(`${BASE_URL}/api/debug/session-status`, {
      method: 'GET',
    })

    console.log(`   Status: ${debugResponse.status}`)
    const debugData = await debugResponse.json()
    console.log(`   Debug info:`, JSON.stringify(debugData, null, 2))

    if (debugResponse.ok) {
      console.log('   ✅ PASS: Debug endpoint accessible')
    } else {
      console.log('   ❌ FAIL: Debug endpoint not accessible')
    }

    // Test 3: Test session check API
    console.log('\n3️⃣ Testing session check API without authentication...')
    const sessionCheckResponse = await fetch(`${BASE_URL}/api/auth/check-session`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ role: 'student' }),
    })

    console.log(`   Status: ${sessionCheckResponse.status}`)
    const sessionCheckData = await sessionCheckResponse.json()
    console.log(`   Response:`, sessionCheckData)

    if (sessionCheckResponse.status === 401) {
      console.log('   ✅ PASS: Session check returns 401 for unauthenticated requests')
    } else {
      console.log('   ❌ FAIL: Session check should return 401 for unauthenticated requests')
    }

    console.log('\n🎯 Test Summary:')
    console.log('   - Session events API now properly handles auth failures')
    console.log('   - Debug endpoint available for troubleshooting')
    console.log('   - Session monitoring should no longer cause auto-logout')
    console.log('\n✅ Auto-logout fix verification completed!')

  } catch (error) {
    console.error('❌ Test failed with error:', error)
  }
}

// Run the test
testSessionFlow()
