# Session Management Integration Guide

## Overview

This guide explains how to integrate the session management system into your existing authentication flows and how to use the new features.

## Quick Start

### 1. Environment Setup

Ensure your `.env` file includes:
```env
REDIS_URL=redis://localhost:6379
JWT_SECRET=your-secure-jwt-secret
```

### 2. Using Enhanced Authentication

Replace existing auth imports with enhanced versions:

```typescript
// Old way
import { authenticate } from '@/lib/middleware/auth'

// New way
import { authenticateWithSession } from '@/lib/middleware/enhanced-auth'
```

### 3. Login Flow Integration

Update your login endpoints to use enhanced auth:

```typescript
import { EnhancedAuthUseCases } from '@/lib/domain/usecases/enhanced-auth'
import { extractDeviceInfo } from '@/lib/middleware/enhanced-auth'

// In your login handler
const { userAgent, ipAddress } = extractDeviceInfo(request)
const { token, refreshToken, user, session } = await authUseCases.loginAdmin(
  username, 
  password,
  userAgent,
  ipAddress
)
```

## API Integration

### Admin Session Management

#### List Sessions
```typescript
// GET /api/admin/sessions
const response = await fetch('/api/admin/sessions?role=student&isActive=true&limit=50')
const { sessions, pagination } = await response.json()
```

#### Force Logout User
```typescript
// DELETE /api/admin/sessions
await fetch('/api/admin/sessions', {
  method: 'DELETE',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({ userId: 123 })
})
```

#### Invalidate Specific Session
```typescript
// DELETE /api/admin/sessions
await fetch('/api/admin/sessions', {
  method: 'DELETE',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({ sessionId: 'session-uuid' })
})
```

### User Session Management

#### Get User's Sessions
```typescript
// GET /api/user/sessions
const response = await fetch('/api/user/sessions')
const { sessions, currentSessionId } = await response.json()
```

#### Logout from Other Sessions
```typescript
// DELETE /api/user/sessions
await fetch('/api/user/sessions', {
  method: 'DELETE',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({ currentSessionId: 'current-session-uuid' })
})
```

## Frontend Integration

### React Hook for Session Management

```typescript
import { useState, useEffect } from 'react'

export function useUserSessions() {
  const [sessions, setSessions] = useState([])
  const [loading, setLoading] = useState(true)

  const fetchSessions = async () => {
    try {
      const response = await fetch('/api/user/sessions')
      const data = await response.json()
      setSessions(data.data.sessions)
    } catch (error) {
      console.error('Failed to fetch sessions:', error)
    } finally {
      setLoading(false)
    }
  }

  const logoutFromOtherSessions = async (currentSessionId: string) => {
    try {
      await fetch('/api/user/sessions', {
        method: 'DELETE',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ currentSessionId })
      })
      await fetchSessions() // Refresh list
    } catch (error) {
      console.error('Failed to logout from other sessions:', error)
    }
  }

  useEffect(() => {
    fetchSessions()
  }, [])

  return { sessions, loading, fetchSessions, logoutFromOtherSessions }
}
```

### Session Status Component

```typescript
import { useUserSessions } from './hooks/useUserSessions'

export function SessionStatus() {
  const { sessions, logoutFromOtherSessions } = useUserSessions()
  const activeSessions = sessions.filter(s => s.isActive)

  return (
    <div className="session-status">
      <h3>Active Sessions: {activeSessions.length}</h3>
      {activeSessions.map(session => (
        <div key={session.sessionId} className="session-item">
          <span>{session.deviceType} - {session.browser}</span>
          <span>{new Date(session.lastAccessedAt).toLocaleString()}</span>
        </div>
      ))}
      {activeSessions.length > 1 && (
        <button onClick={() => logoutFromOtherSessions(currentSessionId)}>
          Logout from other devices
        </button>
      )}
    </div>
  )
}
```

## Middleware Integration

### Enhanced Route Protection

```typescript
import { authenticateWithSession } from '@/lib/middleware/enhanced-auth'

export async function GET(request: NextRequest) {
  try {
    // Authenticate with session validation
    const authResult = await authenticateWithSession(request, 'admin')
    
    // Handle token refresh if needed
    const response = NextResponse.json({ data: 'protected data' })
    return createResponseWithRefreshedToken(response, authResult, 'admin')
  } catch (error) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
  }
}
```

### Super Admin Protection

```typescript
import { authenticateSuperAdmin } from '@/lib/middleware/enhanced-auth'

export async function DELETE(request: NextRequest) {
  try {
    const authResult = await authenticateSuperAdmin(request)
    // Super admin only operations
    return NextResponse.json({ success: true })
  } catch (error) {
    return NextResponse.json({ error: 'Forbidden' }, { status: 403 })
  }
}
```

## Database Integration

### Session Data Structure

Sessions are stored in Redis with the following structure:

```typescript
interface SessionData {
  sessionId: string
  userId: number
  role: 'student' | 'admin' | 'super_admin'
  deviceId: string
  ipAddress: string
  userAgent: string
  createdAt: Date
  lastAccessedAt: Date
  expiresAt: Date
  isActive: boolean
  metadata?: {
    deviceType?: string
    browser?: string
    os?: string
    location?: string
  }
}
```

### Redis Keys

- `session:{sessionId}` - Session data
- `user_sessions:{userId}` - User's session IDs
- `device_session:{userId}:{deviceId}` - Device-specific session mapping
- `session_index:all` - Global session index

## Migration Guide

### From Legacy Auth to Enhanced Auth

1. **Update imports**:
```typescript
// Before
import { authenticate } from '@/lib/middleware/auth'

// After
import { authenticateWithSession } from '@/lib/middleware/enhanced-auth'
```

2. **Update login handlers**:
```typescript
// Before
const { token, refreshToken, user } = await authUseCases.loginAdmin(username, password)

// After
const { userAgent, ipAddress } = extractDeviceInfo(request)
const { token, refreshToken, user, session } = await enhancedAuthUseCases.loginAdmin(
  username, password, userAgent, ipAddress
)
```

3. **Update middleware usage**:
```typescript
// Before
const decoded = await authenticate(req, 'admin')

// After
const authResult = await authenticateWithSession(req, 'admin')
// Handle token refresh
return createResponseWithRefreshedToken(response, authResult, 'admin')
```

### Backward Compatibility

The system maintains backward compatibility:
- Legacy JWT tokens without session info still work
- Existing auth endpoints continue to function
- Gradual migration is supported

## Testing

### Unit Tests

```typescript
import { testSessionManagement } from '@/lib/test-session-management'

// Run comprehensive test suite
await testSessionManagement()
```

### Integration Tests

```typescript
// Test session creation
const response = await fetch('/api/auth/admin/login', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({ username: 'admin', password: 'password' })
})

const { session } = await response.json()
expect(session.sessionId).toBeDefined()

// Test session validation
const sessionResponse = await fetch('/api/admin/sessions', {
  headers: { 'Cookie': `admin_auth_token=${token}` }
})
expect(sessionResponse.ok).toBe(true)
```

## Troubleshooting

### Common Issues

1. **Redis Connection Failed**
   - Check REDIS_URL environment variable
   - Ensure Redis server is running
   - Verify network connectivity

2. **Session Validation Failed**
   - Check JWT secret consistency
   - Verify session exists in Redis
   - Check session expiry

3. **Device ID Issues**
   - Ensure user agent is being passed correctly
   - Check device fingerprinting logic
   - Verify IP address extraction

### Debug Mode

Enable debug logging:
```env
NODE_ENV=development
```

### Health Check

```typescript
// Check session system health
const stats = await fetch('/api/admin/sessions/stats')
const { totalSessions, activeSessions } = await stats.json()
console.log(`Active sessions: ${activeSessions}/${totalSessions}`)
```

## Security Considerations

### Best Practices

1. **Session Duration**: Keep sessions short (1 hour default)
2. **Device Tracking**: Monitor unusual device patterns
3. **IP Validation**: Consider IP-based validation for sensitive operations
4. **Audit Logging**: Log all session management operations
5. **Rate Limiting**: Implement rate limiting on session endpoints

### Security Headers

```typescript
// Add security headers to session endpoints
response.headers.set('X-Content-Type-Options', 'nosniff')
response.headers.set('X-Frame-Options', 'DENY')
response.headers.set('X-XSS-Protection', '1; mode=block')
```

## Performance Optimization

### Redis Optimization

1. **Connection Pooling**: Use Redis connection pooling
2. **Key Expiration**: Leverage Redis TTL for automatic cleanup
3. **Indexing**: Optimize session indexing for queries
4. **Memory Usage**: Monitor Redis memory consumption

### Caching Strategy

```typescript
// Cache session validation results briefly
const VALIDATION_CACHE_TTL = 60 // 1 minute
await cache.set(`validation:${sessionId}`, 'valid', VALIDATION_CACHE_TTL)
```

## Monitoring and Alerts

### Metrics to Track

1. **Active Sessions**: Number of active sessions by role
2. **Session Duration**: Average session duration
3. **Device Distribution**: Sessions by device type
4. **Failed Validations**: Rate of session validation failures
5. **Force Logouts**: Frequency of admin force logouts

### Alert Conditions

1. **High Session Count**: Alert if sessions exceed threshold
2. **Validation Failures**: Alert on high validation failure rate
3. **Redis Issues**: Alert on Redis connection problems
4. **Suspicious Activity**: Alert on unusual session patterns

This integration guide provides everything needed to successfully implement and maintain the session management system in your application.
