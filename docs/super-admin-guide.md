# Super Admin Guide for ShalatYuk

## Overview

ShalatYuk now supports two types of admin roles:

1. **Admin**: Can only access the Scanner and Reports features
2. **Super Admin**: Has full access to all features, including User Management and Class Management

This guide explains how to manage these roles and their permissions.

## Role Permissions

| Feature          | Admin | Super Admin |
| ---------------- | ----- | ----------- |
| Scanner (Home)   | ✅    | ✅          |
| Reports          | ✅    | ✅          |
| User Management  | ❌    | ✅          |
| Class Management | ❌    | ✅          |
| Profile          | ✅    | ✅          |

## Managing Admin Roles

### Promoting an Admin to Super Admin

To promote an existing admin to super admin, use the provided script:

```bash
npx tsx scripts/promote-admin.ts <username>
```

Replace `<username>` with the username of the admin you want to promote.

Example:

```bash
npx tsx scripts/promote-admin.ts admin123
```

### Demoting a Super Admin to Regular Admin

To demote a super admin back to a regular admin, use the provided script:

```bash
npx tsx scripts/demote-admin.ts <username>
```

Replace `<username>` with the username of the super admin you want to demote.

Example:

```bash
npx tsx scripts/demote-admin.ts admin123
```

### Creating a New Super Admin

When creating a new admin through the User Management interface, you can select the "Super Admin" role from the dropdown menu. Only existing super admins can create new super admins.

## Security Considerations

- Access to the User Management and Class Management pages is restricted at both the UI and API levels.
- Regular admins will be automatically redirected to the Home page if they try to access these restricted pages.
- API endpoints for user and class management also verify the admin role before allowing access.

## Technical Implementation

The super admin role is implemented using:

1. A new value in the `user_role` enum in the database schema
2. Role-based permission checks in the middleware and API routes
3. Conditional rendering of UI elements based on the admin role
4. Automatic redirection when unauthorized access is attempted

## Troubleshooting

If an admin cannot access certain features:

1. Check their role in the database using the admin username
2. Ensure the database migration to add the `super_admin` role has been applied
3. Try logging out and logging back in to refresh the session

For any issues, contact the system administrator.
