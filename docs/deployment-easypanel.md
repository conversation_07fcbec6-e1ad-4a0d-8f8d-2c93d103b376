# Panduan Deployment ke EasyPanel

## Persiapan

1. Pastikan Anda memiliki akun GitHub dan repositori untuk proyek ShalatYuk
2. Pastikan Anda memiliki akses ke EasyPanel

## Langkah-langkah Deployment

### 1. Persiapkan Repositori GitHub

- Pastikan file `Dockerfile` dan konfigurasi `next.config.mjs` dengan `output: 'standalone'` sudah ada di repositori
- Commit dan push perubahan ke repositori GitHub Anda

### 2. Konfigurasi di EasyPanel

1. Login ke dashboard EasyPanel
2. <PERSON><PERSON> "New Project" atau tombol serupa untuk membuat proyek baru
3. <PERSON><PERSON>h "GitHub" sebagai sumber deployment
4. Hubungkan dengan repositori GitHub Anda yang berisi proyek ShalatYuk
5. Pilih branch yang ingin di-deploy (biasanya `main` atau `master`)

### 3. Konfigurasi Docker di EasyPanel

1. <PERSON><PERSON><PERSON> "Docker" sebagai jenis deployment
2. EasyPanel akan otomatis mendeteksi `Dockerfile` di repositori Anda
3. Konfigurasi port: Pastikan port 3000 diekspos (sesuai dengan `EXPOSE 3000` di Dockerfile)

### 4. Konfigurasi Environment Variables

Tambahkan variabel lingkungan berikut di EasyPanel:

```
NODE_ENV=production
NEXT_TELEMETRY_DISABLED=1
DATABASE_URL=***********************************************/database_name
REDIS_URL=redis://default:password@redis-host:6379
JWT_SECRET=your-secret-key-at-least-32-characters-long
NEXTAUTH_URL=https://your-domain.com
NEXTAUTH_SECRET=your-nextauth-secret
```

Ganti nilai-nilai di atas dengan kredensial dan URL yang sesuai dengan konfigurasi EasyPanel Anda.

### 5. Konfigurasi Sumber Daya

Sesuaikan sumber daya yang dialokasikan untuk aplikasi berdasarkan kebutuhan:

- CPU: Minimal 1 core
- RAM: Minimal 1 GB
- Storage: Minimal 5 GB

### 6. Deploy Aplikasi

1. Klik tombol "Deploy" atau "Create" untuk memulai proses deployment
2. EasyPanel akan mengambil kode dari GitHub, membangun Docker image, dan menjalankan container
3. Tunggu hingga proses deployment selesai

### 7. Konfigurasi Domain dan SSL

1. Setelah deployment berhasil, konfigurasikan domain di EasyPanel
2. Aktifkan SSL/HTTPS menggunakan Let's Encrypt yang disediakan oleh EasyPanel

## Pemeliharaan dan Update

### Update Aplikasi

Untuk memperbarui aplikasi:

1. Push perubahan ke repositori GitHub
2. Di EasyPanel, pilih proyek ShalatYuk
3. Klik "Rebuild" atau "Redeploy"

### Monitoring

Gunakan fitur monitoring EasyPanel untuk memantau:

- Penggunaan CPU dan RAM
- Log aplikasi
- Status container

## Troubleshooting

### Container Gagal Start

1. Periksa log container di EasyPanel
2. Pastikan semua environment variables sudah dikonfigurasi dengan benar
3. Periksa apakah database dan Redis dapat diakses dari container

### Masalah Database

1. Pastikan string koneksi DATABASE_URL sudah benar
2. Periksa apakah database sudah dimigrasi dengan benar

### Masalah Redis

1. Pastikan string koneksi REDIS_URL sudah benar
2. Periksa apakah Redis dapat diakses dari container

## Catatan Penting

- Pastikan untuk tidak menyimpan kredensial sensitif di repositori GitHub
- Gunakan environment variables di EasyPanel untuk semua konfigurasi sensitif
- Lakukan backup database secara berkala menggunakan fitur backup EasyPanel