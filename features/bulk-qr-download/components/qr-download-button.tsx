'use client'

import { But<PERSON> } from '@/components/ui/button'
import { QrCode } from 'lucide-react'

interface QrDownloadButtonProps {
  onClick: () => void
  disabled?: boolean
}

export function QrDownloadButton({ onClick, disabled = false }: QrDownloadButtonProps) {
  return (
    <Button
      onClick={onClick}
      variant="outline"
      className="flex gap-1 sm:flex-nowrap"
      disabled={disabled}
    >
      <QrCode className="h-4 w-4" />
      <span className="sm:hidden">QR</span>
      <span className="hidden sm:inline">Download QR</span>
    </Button>
  )
}
