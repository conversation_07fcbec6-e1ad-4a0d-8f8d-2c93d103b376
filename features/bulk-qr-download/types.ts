// Types for Bulk QR Download feature
export type QrDownloadType = 'all' | 'selected' | 'class'

export interface QrDownloadProgress {
  current: number
  total: number
  status: string
}

export interface QrDownloadRequest {
  downloadType: QrDownloadType
  studentIds?: number[]
  classId?: string
}

export interface QrDownloadState {
  showDialog: boolean
  downloadType: QrDownloadType
  classId: string
  isDownloading: boolean
  progress: QrDownloadProgress | null
}

export interface QrDownloadActions {
  openDialog: () => void
  closeDialog: () => void
  setDownloadType: (type: QrDownloadType) => void
  setClassId: (classId: string) => void
  downloadQrCodes: (selectedUserIds: Set<number>) => Promise<void>
}

export interface Class {
  id: number
  name: string
}

export interface User {
  id: number
  name: string
  role: 'student' | 'admin' | 'super_admin'
  uniqueCode?: string
  username?: string
  classId?: number
  className?: string
}
