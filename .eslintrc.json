{"extends": ["next/core-web-vitals", "eslint:recommended", "plugin:@typescript-eslint/recommended", "plugin:react/recommended", "plugin:react-hooks/recommended", "plugin:tailwindcss/recommended", "prettier"], "parser": "@typescript-eslint/parser", "plugins": ["@typescript-eslint", "react", "react-hooks", "tailwindcss"], "rules": {"react/react-in-jsx-scope": "off", "react/prop-types": "off", "@typescript-eslint/explicit-module-boundary-types": "off", "@typescript-eslint/no-unused-vars": ["warn", {"argsIgnorePattern": "^_"}], "@typescript-eslint/no-explicit-any": "warn", "tailwindcss/no-custom-classname": "warn", "tailwindcss/classnames-order": "warn", "react-hooks/rules-of-hooks": "error", "react-hooks/exhaustive-deps": "warn"}, "settings": {"react": {"version": "detect"}, "tailwindcss": {"callees": ["cn", "cva"], "config": "tailwind.config.ts"}}, "ignorePatterns": ["node_modules/", ".next/", "out/"]}