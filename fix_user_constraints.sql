ALTER TABLE users ALTER COLUMN google_email DROP NOT NULL; ALTER TABLE users ALTER COLUMN unique_code DROP NOT NULL; ALTER TABLE users ALTER COLUMN username DROP NOT NULL; ALTER TABLE users ALTER COLUMN password_hash DROP NOT NULL; ALTER TABLE users DROP CONSTRAINT IF EXISTS chk_role_data; ALTER TABLE users ADD CONSTRAINT chk_role_data CHECK ((role = 'student' AND unique_code IS NOT NULL AND username IS NOT NULL AND password_hash IS NOT NULL) OR (role = 'admin' AND google_email IS NULL AND unique_code IS NULL AND username IS NOT NULL AND password_hash IS NOT NULL));
