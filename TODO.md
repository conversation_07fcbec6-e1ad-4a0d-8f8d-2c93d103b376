# Todo List Aplikasi Absensi Shalat

## Setup Awal

- [x] Siapkan struktur folder Clean Architecture (Presentation, Domain, Data)
- [x] Konfigurasi ESLint dan Prettier
- [x] Setup TypeScript dengan konfigurasi yang ketat
- [x] Konfigurasi Tailwind CSS dan shadcn/ui
- [x] Setup next-themes untuk dukungan tema gelap/terang
- [x] Setup environment variables (.env dan .env.local)
- [x] Setup SSH tunnel untuk development

## Clean Architecture Implementation

- [ ] Domain Layer:

  - [x] Definisikan entity User (admin, student)
  - [x] Definisikan entity Attendance (Zuhr, Asr, Pulang)
  - [x] Definisikan entity Class dan AttendanceSummary
  - [x] Implementasi use cases untuk autentikasi (login, forgot password)
  - [ ] Implementasi use cases untuk absensi (record, check, reports)
  - [ ] Implementasi use cases untuk manajemen user
  - [x] Definisikan interface repositories untuk abstraksi data layer

- [ ] Data Layer:

  - [x] Implementasi Drizzle schema untuk PostgreSQL
  - [x] Implementasi repositories untuk User
  - [x] Implementasi repositories untuk Attendance
  - [x] Implementasi repositories untuk Class dan AttendanceSummary
  - [x] Implementasi cache service dengan Redis
  - [ ] Implementasi WhatsApp service dengan n8n webhook
  - [x] Implementasi JWT service untuk autentikasi

- [ ] Presentation Layer:

  - [ ] Implementasi hooks untuk autentikasi (useAuth)
  - [ ] Implementasi hooks untuk absensi (useAttendance)
  - [ ] Implementasi hooks untuk manajemen user (useUsers)
  - [ ] Implementasi context providers (AuthProvider)
  - [x] Implementasi form validation dengan Zod schemas
  - [ ] Implementasi error handling dan toast notifications

- [ ] API Routes:
  - [x] Implementasi API routes dengan dependency injection
  - [x] Implementasi middleware untuk autentikasi dan rate limiting
  - [x] Implementasi error handling untuk API responses

## Database dan Backend

- [x] Buat skema database PostgreSQL sesuai PRD
- [x] Setup Drizzle ORM dan koneksi database
- [x] Konfigurasi Redis untuk caching
- [ ] Setup n8n Webhook untuk WhatsApp OTP
- [x] Implementasi JWT untuk autentikasi
- [x] Implementasi rate limiting untuk API
- [x] Implementasi fallback untuk Redis dan PostgreSQL

## Landing Page

- [x] Buat Hero Section dengan headline dan gambar
- [x] Buat About Section dengan deskripsi aplikasi
- [x] Buat Features Section dengan 3 card (QR Code, Scanner, Reports)
- [x] Buat Footer dengan informasi kontak
- [x] Implementasi Theme Toggle

## Student App

- [x] Implementasi halaman Login dengan validasi Zod
- [x] Implementasi halaman Forgot Password dengan OTP WhatsApp
- [x] Implementasi halaman Home dengan QR Code
- [x] Implementasi halaman Profile
- [x] Implementasi fitur refresh QR Code setiap 5 menit
- [x] Implementasi status absensi real-time

## Admin App

- [ ] Implementasi halaman Login Admin
- [ ] Implementasi halaman Scanner dengan jsQR
- [ ] Implementasi halaman Reports dengan filter dan export CSV
- [ ] Implementasi halaman User Management (CRUD)
  - [x] **NEW: Super Admin Change Student Password Feature** ✅
  - [x] **NEW: Display Student Usernames in Student Table** ✅
- [ ] Implementasi halaman Profile Admin
- [ ] Implementasi ringkasan absensi real-time

## API Routes

- [x] Implementasi `/api/auth/student/google` (Initiates Google OAuth flow)
- [x] Implementasi `/api/auth/student/google/callback` (Handles Google callback)
- [x] Implementasi `/api/auth/logout` (Handles user logout)
- [x] Implementasi `/api/auth/admin/login` (Handles admin login)
- [x] Implementasi `/api/student/whatsapp/send-otp` (Sends OTP via WhatsApp)
- [x] Implementasi `/api/student/whatsapp/verify-otp` (Verifies WhatsApp OTP)
- [x] Implementasi `/api/student/profile` (GET, PATCH)
- [x] Implementasi `/api/absence/record` (POST)
- [x] Implementasi `/api/absence/check` (GET)
- [x] Implementasi `/api/absence/reports` (GET)
- [ ] Implementasi `/api/refresh-summary` (POST)
- [ ] Implementasi `/api/users` (GET, POST)
- [ ] Implementasi `/api/users/{id}` (PATCH, DELETE)
- [ ] Implementasi `/api/admin/profile` (PATCH)

## Testing

- [x] Setup simple test scripts untuk database connections
- [x] Setup simple test scripts untuk domain layer
- [ ] Setup Jest untuk unit testing
- [ ] Setup React Testing Library untuk component testing
- [ ] Tulis test untuk utility functions
- [ ] Tulis test untuk komponen UI
- [ ] Tulis test untuk API routes
- [ ] Pastikan coverage testing minimal 80%

## Deployment

- [x] Create deployment script (scripts/deploy.sh)
- [ ] Konfigurasi Docker untuk aplikasi Next.js
- [ ] Setup EasyPanel di VPS
- [ ] Konfigurasi HTTPS dengan Let's Encrypt
- [ ] Setup backup harian untuk PostgreSQL
- [ ] Implementasi logging dengan Winston
- [ ] Setup monitoring (opsional: Sentry)

## Optimasi

- [ ] Optimasi performa frontend (lazy loading, code splitting)
- [ ] Optimasi query database
- [x] Implementasi strategi caching yang efektif dengan Redis
- [x] Implementasi in-memory fallback untuk Redis
- [ ] Pastikan responsif untuk semua ukuran layar (320px-1280px)
- [ ] Pastikan aksesibilitas sesuai WCAG 2.1 Level AA

## Refactoring

- [x] Refactor authentication routes to follow RESTful principles
- [x] Remove [...nextauth] catch-all route in favor of explicit routes
- [x] Implement centralized configuration in lib/config.ts
- [x] Implement proper error handling for database connections
- [ ] Refactor remaining API routes to use new authentication middleware
