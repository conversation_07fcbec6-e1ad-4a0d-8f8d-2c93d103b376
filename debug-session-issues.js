/**
 * Debug Session Issues Script
 * Helps diagnose and fix session management problems
 */

const BASE_URL = 'http://localhost:3001'

async function debugSessionIssues() {
  console.log('🔍 Debugging Session Issues...\n')

  try {
    // 1. Login as super admin
    console.log('1. Logging in as super admin...')
    const adminLoginResponse = await fetch(`${BASE_URL}/api/auth/admin/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        username: 'super_admin',
        password: 'admin123'
      })
    })

    if (!adminLoginResponse.ok) {
      throw new Error('Admin login failed')
    }

    const adminCookies = adminLoginResponse.headers.get('set-cookie')
    console.log('✅ Super admin logged in successfully')

    // 2. Get current session list
    console.log('\n2. Getting current session list...')
    const sessionListResponse = await fetch(`${BASE_URL}/api/admin/sessions?isActive=true&limit=50`, {
      method: 'GET',
      headers: {
        'Cookie': adminCookies || ''
      }
    })

    if (sessionListResponse.ok) {
      const sessionListData = await sessionListResponse.json()
      console.log(`Found ${sessionListData.data.sessions.length} active sessions`)
      
      // Group sessions by user
      const sessionsByUser = {}
      sessionListData.data.sessions.forEach(session => {
        if (!sessionsByUser[session.userId]) {
          sessionsByUser[session.userId] = []
        }
        sessionsByUser[session.userId].push(session)
      })

      // Check for users with multiple sessions
      console.log('\n📊 Session Analysis:')
      for (const [userId, sessions] of Object.entries(sessionsByUser)) {
        if (sessions.length > 1) {
          console.log(`⚠️ User ${userId} has ${sessions.length} sessions:`)
          sessions.forEach(session => {
            console.log(`  - Session ${session.sessionId}: device=${session.deviceId}, created=${session.createdAt}`)
          })
          
          // Debug this user's sessions
          console.log(`\n🔍 Debugging user ${userId} sessions...`)
          const debugResponse = await fetch(`${BASE_URL}/api/debug/sessions?userId=${userId}&action=debug`, {
            method: 'GET',
            headers: {
              'Cookie': adminCookies || ''
            }
          })
          
          if (debugResponse.ok) {
            const debugData = await debugResponse.json()
            console.log('Debug result:', debugData.message)
          }
        } else {
          console.log(`✅ User ${userId} has ${sessions.length} session (correct)`)
        }
      }
    }

    // 3. Test device ID consistency
    console.log('\n3. Testing device ID consistency...')
    const testUserAgent = 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_7_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.2 Mobile/15E148 Safari/604.1'
    const testIpAddress = '*************'
    
    console.log('Test User Agent:', testUserAgent)
    console.log('Test IP Address:', testIpAddress)
    
    // We can't directly test device ID generation from here, but we can test login consistency
    console.log('Testing login consistency with same device info...')

    // 4. Test login with student
    console.log('\n4. Testing student login...')
    const studentLoginResponse1 = await fetch(`${BASE_URL}/api/auth/student/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': testUserAgent,
        'X-Forwarded-For': testIpAddress
      },
      body: JSON.stringify({
        nis: '12345',
        password: 'student123'
      })
    })

    if (studentLoginResponse1.ok) {
      console.log('✅ First student login successful')
      
      // Check session count
      const sessionCheck1 = await fetch(`${BASE_URL}/api/admin/sessions?role=student&isActive=true&limit=10`, {
        method: 'GET',
        headers: {
          'Cookie': adminCookies || ''
        }
      })
      
      if (sessionCheck1.ok) {
        const sessionData1 = await sessionCheck1.json()
        const studentSessions1 = sessionData1.data.sessions.filter(s => s.role === 'student')
        console.log(`Student sessions after first login: ${studentSessions1.length}`)
      }

      // Wait a moment
      await new Promise(resolve => setTimeout(resolve, 1000))

      // Login again with same device info
      console.log('\n5. Testing second login with same device info...')
      const studentLoginResponse2 = await fetch(`${BASE_URL}/api/auth/student/login`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'User-Agent': testUserAgent,
          'X-Forwarded-For': testIpAddress
        },
        body: JSON.stringify({
          nis: '12345',
          password: 'student123'
        })
      })

      if (studentLoginResponse2.ok) {
        console.log('✅ Second student login successful')
        
        // Check session count again
        const sessionCheck2 = await fetch(`${BASE_URL}/api/admin/sessions?role=student&isActive=true&limit=10`, {
          method: 'GET',
          headers: {
            'Cookie': adminCookies || ''
          }
        })
        
        if (sessionCheck2.ok) {
          const sessionData2 = await sessionCheck2.json()
          const studentSessions2 = sessionData2.data.sessions.filter(s => s.role === 'student')
          console.log(`Student sessions after second login: ${studentSessions2.length}`)
          
          if (studentSessions2.length === 1) {
            console.log('🎉 Perfect! Single session enforcement working')
          } else {
            console.log('⚠️ Session duplication detected!')
            
            // Try to cleanup
            console.log('\n🧹 Attempting to cleanup duplicate sessions...')
            const studentUserId = studentSessions2[0].userId
            const cleanupResponse = await fetch(`${BASE_URL}/api/debug/sessions?userId=${studentUserId}&action=cleanup`, {
              method: 'GET',
              headers: {
                'Cookie': adminCookies || ''
              }
            })
            
            if (cleanupResponse.ok) {
              const cleanupData = await cleanupResponse.json()
              console.log('Cleanup result:', cleanupData.message)
            }
          }
        }
      }
    }

    console.log('\n🎉 Session Debugging Completed!')
    console.log('\n📋 Recommendations:')
    console.log('- Check server logs for detailed session operations')
    console.log('- Use /api/debug/sessions endpoint for specific user debugging')
    console.log('- Monitor Redis keys for session consistency')
    console.log('- Verify device ID generation is deterministic')

  } catch (error) {
    console.error('❌ Debug failed:', error)
  }
}

// Run the debug
debugSessionIssues()
