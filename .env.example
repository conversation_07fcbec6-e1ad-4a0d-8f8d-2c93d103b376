# ShalatYuk Environment Configuration Example
# Copy this file to .env.local for development or .env for production
# and update the values according to your setup

# =============================================================================
# ENVIRONMENT
# =============================================================================
NODE_ENV=development

# =============================================================================
# DOMAIN CONFIGURATION - NO HARDCODED VALUES
# =============================================================================
# Base domain for your application (REQUIRED)
# This is the main domain where your application will be hosted
DOMAIN=yourdomain.com

# Subdomain configuration (OPTIONAL)
# If not set, will automatically use student.DOMAIN and admin.DOMAIN
# Set these if you want custom subdomain names
STUDENT_DOMAIN=shalat.yourdomain.com
ADMIN_DOMAIN=admin.yourdomain.com

# Examples for different setups:
#
# For development with localhost:
# DOMAIN=localhost:3000
# STUDENT_DOMAIN=student.localhost:3000
# ADMIN_DOMAIN=admin.localhost:3000
#
# For production with custom domain:
# DOMAIN=myschool.com
# STUDENT_DOMAIN=shalat.myschool.com
# ADMIN_DOMAIN=admin.myschool.com
#
# For production with simple subdomains (auto-generated):
# DOMAIN=myschool.com
# (Will automatically create student.myschool.com and admin.myschool.com)

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================
# PostgreSQL database connection string
DATABASE_URL=postgres://postgres:password@localhost:5432/website

# =============================================================================
# REDIS CONFIGURATION
# =============================================================================
# Redis connection string for caching and sessions
REDIS_URL=redis://localhost:6379
# Optional Redis password if required
REDIS_PASSWORD=your_redis_password

# =============================================================================
# AUTHENTICATION
# =============================================================================
# JWT secret key for token signing (REQUIRED - generate a strong random key)
JWT_SECRET=your-secret-key-at-least-32-characters-long

# NextAuth configuration (legacy - can be removed if not using NextAuth)
NEXTAUTH_URL=https://yourdomain.com
NEXTAUTH_SECRET=your_nextauth_secret_key

# =============================================================================
# SCHOOL CONFIGURATION - FULLY CUSTOMIZABLE
# =============================================================================
# School information (customizable for different schools)
SCHOOL_NAME=Your School Name
SCHOOL_ADDRESS=Your School Address
SCHOOL_WEBSITE=https://yourschool.edu

# =============================================================================
# EXTERNAL SERVICES
# =============================================================================
# N8N WhatsApp webhook URL for OTP sending
N8N_WHATSAPP_WEBHOOK_URL=https://your-n8n-instance.com/webhook/your-webhook-id

# Google OAuth (legacy - can be removed if not using Google auth)
GOOGLE_CLIENT_ID=your_google_client_id
GOOGLE_CLIENT_SECRET=your_google_client_secret