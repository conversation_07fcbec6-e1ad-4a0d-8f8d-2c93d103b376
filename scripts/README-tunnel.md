# SSH Tunnel untuk Development

Script ini memungkinkan Anda mengakses database PostgreSQL dan Redis di server remote melalui SSH tunnel untuk keperluan development.

## Setup Awal

1. Copy template ke file aktual:
   ```bash
   cp scripts/dev-tunnel.template.sh scripts/dev-tunnel.sh
   chmod +x scripts/dev-tunnel.sh
   ```

2. Edit file `scripts/dev-tunnel.sh` dan sesuaikan konfigurasi:
   ```bash
   # Configuration (edit these values)
   SSH_KEY="~/.ssh/your_key_file"
   SSH_USER="your_username"
   SSH_HOST="your_server_ip"
   DB_USER="postgres"
   DB_PASSWORD="your_password_here"
   DB_NAME="your_database_name"
   REDIS_PASSWORD="your_redis_password"
   ```

3. Sesuaikan port jika diperlukan (default menggunakan port standar):
   ```bash
   # Ports - Gunakan port yang sama dengan EasyPanel untuk konsistensi
   PG_LOCAL_PORT=5432
   PG_REMOTE_PORT=5432
   REDIS_LOCAL_PORT=6379
   REDIS_REMOTE_PORT=6379
   ```

## Cara Penggunaan

### 1. Jalankan Tunnel

```bash
npm run tunnel
# atau
pnpm tunnel
```

Script akan:
- Membuat SSH tunnel ke PostgreSQL dan Redis
- Memperbarui atau membuat file `.env.local` dengan konfigurasi yang benar
- Menampilkan informasi status tunnel

### 2. Jalankan Aplikasi

```bash
npm run dev
# atau
pnpm dev
```

Atau, jalankan keduanya sekaligus:

```bash
npm run dev:tunnel
# atau
pnpm dev:tunnel
```

### 3. Memeriksa Status Tunnel

```bash
npm run tunnel:status
# atau
pnpm tunnel:status
```

### 4. Menghentikan Tunnel

```bash
npm run tunnel:stop
# atau
pnpm tunnel:stop
```

### 5. Memulai Ulang Tunnel

```bash
npm run tunnel:restart
# atau
pnpm tunnel:restart
```

### 6. Membersihkan Semua Tunnel dan Port

```bash
npm run tunnel:clean
# atau
pnpm tunnel:clean
```

## Pemecahan Masalah

### Port Sudah Digunakan

Jika port PostgreSQL (5432) atau Redis (6379) sudah digunakan oleh layanan lokal:

1. Script akan mencoba menghentikan proses yang menggunakan port tersebut secara otomatis.
2. Jika gagal, Anda dapat:
   - Menjalankan `npm run tunnel:clean` untuk membersihkan semua tunnel dan port
   - Mengubah port lokal di file `scripts/dev-tunnel.sh` (misalnya menjadi 5433 dan 6380)
   - Menghentikan layanan lokal yang menggunakan port tersebut

### Tunnel Tidak Terdeteksi

Jika `tunnel:status` melaporkan tunnel tidak berjalan tetapi port masih digunakan:

1. Periksa proses SSH yang berjalan:
   ```bash
   ps aux | grep ssh
   ```

2. Periksa apakah port dapat diakses:
   ```bash
   nc -zv localhost 5432
   nc -zv localhost 6379
   ```

3. Jika port dapat diakses tetapi tunnel tidak terdeteksi, coba restart tunnel:
   ```bash
   npm run tunnel:restart
   ```

## Catatan Penting

- Script ini hanya untuk lingkungan development
- File `scripts/dev-tunnel.sh` dan `.env.local` tidak boleh di-commit ke Git (sudah ditambahkan ke .gitignore)
- Pastikan SSH key memiliki permission yang benar (chmod 600)
- Untuk lingkungan production, gunakan konfigurasi internal host di EasyPanel
- Script akan membuat backup `.env.local` sebagai `.env.local.bak` sebelum memperbarui file
