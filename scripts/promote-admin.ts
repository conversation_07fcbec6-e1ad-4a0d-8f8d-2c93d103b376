import 'dotenv/config'
import { drizzle } from 'drizzle-orm/postgres-js'
import postgres from 'postgres'
import { eq } from 'drizzle-orm'
import * as fs from 'fs'
import * as path from 'path'
import * as schema from '@/lib/data/drizzle/schema'

async function promoteAdmin() {
  try {
    // Check if username is provided
    const username = process.argv[2]
    if (!username) {
      console.error('Error: Username is required')
      console.log('Usage: npx tsx scripts/promote-admin.ts <username>')
      process.exit(1)
    }

    // Read DATABASE_URL from .env.local
    const envLocalPath = path.join(process.cwd(), '.env.local')
    const envLocal = fs.readFileSync(envLocalPath, 'utf8')

    // Parse DATABASE_URL from .env.local
    const databaseUrlMatch = envLocal.match(/DATABASE_URL=(.+)/)
    if (!databaseUrlMatch) {
      throw new Error('DATABASE_URL not found in .env.local')
    }

    const databaseUrl = databaseUrlMatch[1].trim()
    console.log('Using DATABASE_URL from .env.local')

    // Create a new database connection
    const client = postgres(databaseUrl)
    const db = drizzle(client)

    console.log(`Promoting admin "${username}" to super_admin...`)

    // Find the admin by username
    const [admin] = await db
      .select()
      .from(schema.users)
      .where(eq(schema.users.username, username))
      .limit(1)

    if (!admin) {
      console.error(`Error: Admin with username "${username}" not found`)
      process.exit(1)
    }

    if (admin.role !== 'admin') {
      console.error(`Error: User "${username}" is not an admin (current role: ${admin.role})`)
      process.exit(1)
    }

    // Update the admin role to super_admin
    const [updatedAdmin] = await db
      .update(schema.users)
      .set({
        role: 'super_admin',
        updatedAt: new Date(),
      })
      .where(eq(schema.users.id, admin.id))
      .returning()

    console.log(`Admin "${username}" has been promoted to super_admin successfully!`)
    console.log('Updated admin details:')
    console.log(`- ID: ${updatedAdmin.id}`)
    console.log(`- Username: ${updatedAdmin.username}`)
    console.log(`- Name: ${updatedAdmin.name}`)
    console.log(`- Role: ${updatedAdmin.role}`)

    // Close the connection
    await client.end()
  } catch (error) {
    console.error('Error promoting admin:', error)
  } finally {
    process.exit(0)
  }
}

promoteAdmin()
