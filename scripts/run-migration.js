// run-migration.js
// <PERSON>ript to run the custom SQL migration to remove the class name constraint
require('dotenv').config();
const { Pool } = require('pg');
const fs = require('fs');
const path = require('path');

async function runMigration() {
    // Create a connection to the database
    const pool = new Pool({
        connectionString: process.env.DB_URL,
    });

    try {
        // Read the SQL file
        const migrationPath = path.join(__dirname, '../drizzle/migrations/0001_flexible_class_names.sql');
        const sql = fs.readFileSync(migrationPath, 'utf8');

        console.log('Running migration to remove class name constraint...');

        // Execute the SQL
        await pool.query(sql);

        console.log('Migration completed successfully!');
    } catch (error) {
        console.error('Error running migration:', error);
    } finally {
        // Close the pool
        await pool.end();
    }
}

runMigration(); 