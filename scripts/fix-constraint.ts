import { db } from '../lib/data/drizzle/db'
import { fixConstraintSQL } from '../lib/data/drizzle/schema'

// Execute the SQL to fix constraint
async function fixConstraint() {
  try {
    // Execute SQL directly using db client
    await db.execute(fixConstraintSQL)
  } catch (error) {
    console.error('Error fixing constraint:', error)
    process.exit(1)
  } finally {
    process.exit(0)
  }
}

fixConstraint()
