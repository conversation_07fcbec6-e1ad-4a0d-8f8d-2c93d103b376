#!/usr/bin/env node

/**
 * Redis Session Cleanup Script
 * 
 * This script clears all session-related data from Redis
 * Use this to reset sessions when experiencing auto-logout issues
 * 
 * Usage: node scripts/clear-redis-sessions.js
 */

const Redis = require('ioredis')
require('dotenv').config({ path: '.env.local' })
require('dotenv').config({ path: '.env' })

async function clearRedisSessions() {
  console.log('🧹 Starting Redis session cleanup...')
  
  let redis
  try {
    // Connect to Redis
    const redisUrl = process.env.REDIS_URL || 'redis://localhost:6379'
    console.log(`Connecting to Redis: ${redisUrl}`)
    
    redis = new Redis(redisUrl, {
      retryDelayOnFailover: 100,
      maxRetriesPerRequest: 3,
    })

    // Test connection
    await redis.ping()
    console.log('✅ Connected to Redis successfully')

    // Get all session-related keys
    const sessionKeys = await redis.keys('session:*')
    const userSessionKeys = await redis.keys('user_sessions:*')
    const deviceSessionKeys = await redis.keys('device_session:*')
    const sessionIndexKeys = await redis.keys('session_index:*')
    const sessionCounterKeys = await redis.keys('session_counters:*')

    const allKeys = [
      ...sessionKeys,
      ...userSessionKeys,
      ...deviceSessionKeys,
      ...sessionIndexKeys,
      ...sessionCounterKeys
    ]

    console.log(`\n📊 Found session-related keys:`)
    console.log(`   - session:* keys: ${sessionKeys.length}`)
    console.log(`   - user_sessions:* keys: ${userSessionKeys.length}`)
    console.log(`   - device_session:* keys: ${deviceSessionKeys.length}`)
    console.log(`   - session_index:* keys: ${sessionIndexKeys.length}`)
    console.log(`   - session_counters:* keys: ${sessionCounterKeys.length}`)
    console.log(`   - Total keys to delete: ${allKeys.length}`)

    if (allKeys.length === 0) {
      console.log('\n✅ No session keys found - Redis is already clean!')
      return
    }

    // Delete all session keys
    console.log('\n🗑️  Deleting session keys...')
    
    if (allKeys.length > 0) {
      const deleted = await redis.del(...allKeys)
      console.log(`✅ Deleted ${deleted} keys successfully`)
    }

    // Clear any remaining session-related patterns
    console.log('\n🧹 Clearing additional session patterns...')
    
    // Clear auth refresh tokens
    const authKeys = await redis.keys('auth:refresh:*')
    if (authKeys.length > 0) {
      await redis.del(...authKeys)
      console.log(`✅ Deleted ${authKeys.length} auth refresh tokens`)
    }

    // Clear any cached user data that might interfere
    const userCacheKeys = await redis.keys('user:*')
    if (userCacheKeys.length > 0) {
      await redis.del(...userCacheKeys)
      console.log(`✅ Deleted ${userCacheKeys.length} user cache keys`)
    }

    console.log('\n🎉 Redis session cleanup completed successfully!')
    console.log('\n📝 Next steps:')
    console.log('   1. Restart your application: npm run dev')
    console.log('   2. Clear browser cookies and localStorage')
    console.log('   3. Try logging in again')
    console.log('   4. Monitor console for any session-related errors')

  } catch (error) {
    console.error('❌ Redis cleanup failed:', error)
    console.log('\n🔧 Troubleshooting:')
    console.log('   1. Check if Redis is running: redis-cli ping')
    console.log('   2. Verify REDIS_URL in your .env file')
    console.log('   3. Check Redis connection settings')
    
    process.exit(1)
  } finally {
    if (redis) {
      await redis.disconnect()
      console.log('🔌 Disconnected from Redis')
    }
  }
}

// Add confirmation prompt for production safety
async function confirmCleanup() {
  const readline = require('readline')
  const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout
  })

  return new Promise((resolve) => {
    rl.question('\n⚠️  This will delete ALL session data from Redis. Continue? (y/N): ', (answer) => {
      rl.close()
      resolve(answer.toLowerCase() === 'y' || answer.toLowerCase() === 'yes')
    })
  })
}

// Main execution
async function main() {
  console.log('🚨 Redis Session Cleanup Tool')
  console.log('==============================')
  console.log('This tool will clear all session-related data from Redis.')
  console.log('Use this when experiencing auto-logout issues.')
  
  const confirmed = await confirmCleanup()
  
  if (!confirmed) {
    console.log('❌ Cleanup cancelled by user')
    process.exit(0)
  }

  await clearRedisSessions()
}

// Run the script
if (require.main === module) {
  main().catch(console.error)
}

module.exports = { clearRedisSessions }
