#!/usr/bin/env node

/**
 * Domain Configuration Test Script
 * 
 * This script validates the domain configuration and tests the fixes
 * Run with: node scripts/test-domain-config.js
 */

const path = require('path')
const fs = require('fs')

// Load environment variables
require('dotenv').config({ path: path.join(__dirname, '../.env.local') })
require('dotenv').config({ path: path.join(__dirname, '../.env') })

console.log('🧪 Testing Domain Configuration...\n')

// Test 1: Environment Variables
console.log('📋 1. Environment Variables Check')
console.log('================================')

const requiredEnvVars = [
  'DOMAIN',
  'STUDENT_DOMAIN',
  'ADMIN_DOMAIN'
]

const envStatus = {}
requiredEnvVars.forEach(varName => {
  const value = process.env[varName]
  envStatus[varName] = {
    defined: !!value,
    value: value || 'NOT SET'
  }

  const status = value ? '✅' : '❌'
  console.log(`${status} ${varName}: ${value || 'NOT SET'}`)
})

console.log('')

// Test 2: Domain Format Validation
console.log('🔍 2. Domain Format Validation')
console.log('==============================')

const domainRegex = /^[a-zA-Z0-9][a-zA-Z0-9-]{0,61}[a-zA-Z0-9](?:\.[a-zA-Z0-9][a-zA-Z0-9-]{0,61}[a-zA-Z0-9])*$/

Object.entries(envStatus).forEach(([varName, info]) => {
  if (info.defined) {
    const isValid = domainRegex.test(info.value) || info.value.includes('localhost')
    const status = isValid ? '✅' : '❌'
    console.log(`${status} ${varName} format: ${isValid ? 'Valid' : 'Invalid'}`)
  } else {
    console.log(`⚠️  ${varName} format: Cannot validate (not set)`)
  }
})

console.log('')

// Test 3: Domain Prefix Extraction
console.log('🔧 3. Domain Prefix Extraction')
console.log('===============================')

if (envStatus.STUDENT_DOMAIN.defined) {
  const studentPrefix = envStatus.STUDENT_DOMAIN.value.split('.')[0]
  console.log(`✅ Student domain prefix: "${studentPrefix}"`)
} else {
  console.log('❌ Cannot extract student domain prefix (STUDENT_DOMAIN not set)')
}

if (envStatus.ADMIN_DOMAIN.defined) {
  const adminPrefix = envStatus.ADMIN_DOMAIN.value.split('.')[0]
  console.log(`✅ Admin domain prefix: "${adminPrefix}"`)
} else {
  console.log('❌ Cannot extract admin domain prefix (ADMIN_DOMAIN not set)')
}

console.log('')

// Test 4: Configuration File Check
console.log('📁 4. Configuration Files Check')
console.log('================================')

const configFiles = [
  { path: '.env', name: 'Production Environment' },
  { path: '.env.local', name: 'Local Environment' },
  { path: 'lib/config.ts', name: 'Configuration Module' },
  { path: 'middleware.ts', name: 'Middleware' },
  { path: 'docker-compose.yml', name: 'Docker Compose' }
]

configFiles.forEach(file => {
  const fullPath = path.join(__dirname, '..', file.path)
  const exists = fs.existsSync(fullPath)
  const status = exists ? '✅' : '❌'
  console.log(`${status} ${file.name}: ${exists ? 'Found' : 'Missing'}`)

  if (exists && file.path.endsWith('.ts')) {
    // Check if TypeScript files contain the new exports
    const content = fs.readFileSync(fullPath, 'utf8')
    const hasPrefix = content.includes('DOMAIN_PREFIX')
    const prefixStatus = hasPrefix ? '✅' : '⚠️'
    console.log(`   ${prefixStatus} Contains domain prefix exports: ${hasPrefix ? 'Yes' : 'No'}`)
  }
})

console.log('')

// Test 5: Middleware Configuration
console.log('⚙️  5. Middleware Configuration')
console.log('===============================')

const middlewarePath = path.join(__dirname, '..', 'middleware.ts')
if (fs.existsSync(middlewarePath)) {
  const middlewareContent = fs.readFileSync(middlewarePath, 'utf8')

  // Check for hardcoded domains
  const hasHardcodedStudent = middlewareContent.includes("'shalatyuk.'")
  const hasHardcodedAdmin = middlewareContent.includes("'adminshalat.'")
  const usesDynamicConfig = middlewareContent.includes('STUDENT_DOMAIN_PREFIX') &&
    middlewareContent.includes('ADMIN_DOMAIN_PREFIX')

  console.log(`${hasHardcodedStudent ? '❌' : '✅'} Hardcoded student domain: ${hasHardcodedStudent ? 'Found (needs fix)' : 'Not found (good)'}`)
  console.log(`${hasHardcodedAdmin ? '❌' : '✅'} Hardcoded admin domain: ${hasHardcodedAdmin ? 'Found (needs fix)' : 'Not found (good)'}`)
  console.log(`${usesDynamicConfig ? '✅' : '❌'} Dynamic domain config: ${usesDynamicConfig ? 'Implemented' : 'Missing'}`)
} else {
  console.log('❌ Middleware file not found')
}

console.log('')

// Test 6: Docker Configuration
console.log('🐳 6. Docker Configuration')
console.log('===========================')

const dockerComposePath = path.join(__dirname, '..', 'docker-compose.yml')
if (fs.existsSync(dockerComposePath)) {
  const dockerContent = fs.readFileSync(dockerComposePath, 'utf8')

  const hasEnvVars = dockerContent.includes('${STUDENT_DOMAIN') &&
    dockerContent.includes('${ADMIN_DOMAIN')
  const hasHardcoded = dockerContent.includes('shalatyuk.libstudio.my.id') ||
    dockerContent.includes('adminshalat.libstudio.my.id')

  console.log(`${hasEnvVars ? '✅' : '❌'} Uses environment variables: ${hasEnvVars ? 'Yes' : 'No'}`)
  console.log(`${hasHardcoded ? '⚠️' : '✅'} Has hardcoded domains: ${hasHardcoded ? 'Yes (as fallbacks)' : 'No'}`)
} else {
  console.log('❌ docker-compose.yml not found')
}

console.log('')

// Test 7: Summary and Recommendations
console.log('📊 7. Summary and Recommendations')
console.log('==================================')

const allEnvVarsDefined = requiredEnvVars.every(varName => envStatus[varName].defined)
const isProduction = process.env.NODE_ENV === 'production'

console.log(`Environment: ${process.env.NODE_ENV || 'development'}`)
console.log(`All required env vars defined: ${allEnvVarsDefined ? '✅ Yes' : '❌ No'}`)

if (!allEnvVarsDefined) {
  console.log('\n⚠️  RECOMMENDATIONS:')
  requiredEnvVars.forEach(varName => {
    if (!envStatus[varName].defined) {
      console.log(`   - Set ${varName} in your .env.local file`)
    }
  })
}

if (isProduction && !allEnvVarsDefined) {
  console.log('\n🚨 CRITICAL: Missing environment variables in production!')
}

console.log('\n✅ NEXT STEPS:')
console.log('   1. Ensure all environment variables are set')
console.log('   2. Test the application with different domain configurations')
console.log('   3. Verify API calls work from both student and admin domains')
console.log('   4. Test authentication flow across domains')
console.log('   5. Run the application and check for any console errors')

console.log('\n🔧 TESTING COMMANDS:')
console.log('   - Start development server: npm run dev')
if (envStatus.STUDENT_DOMAIN.defined) {
  console.log(`   - Test student domain: http://${envStatus.STUDENT_DOMAIN.value}`)
}
if (envStatus.ADMIN_DOMAIN.defined) {
  console.log(`   - Test admin domain: http://${envStatus.ADMIN_DOMAIN.value}`)
}
console.log('   - Check middleware logs in browser network tab')

console.log('\n📝 For more details, see: TODO-DOMAIN-FIXES.md')
console.log('\n🎉 Domain configuration test completed!')
