import 'dotenv/config'
import { drizzle } from 'drizzle-orm/postgres-js'
import postgres from 'postgres'
import { sql } from 'drizzle-orm'
import * as fs from 'fs'
import * as path from 'path'

async function updateConstraint() {
  try {
    // Read DATABASE_URL from .env.local
    const envLocalPath = path.join(process.cwd(), '.env.local')
    const envLocal = fs.readFileSync(envLocalPath, 'utf8')

    // Parse DATABASE_URL from .env.local
    const databaseUrlMatch = envLocal.match(/DATABASE_URL=(.+)/)
    if (!databaseUrlMatch) {
      throw new Error('DATABASE_URL not found in .env.local')
    }

    const databaseUrl = databaseUrlMatch[1].trim()
    console.log('Using DATABASE_URL from .env.local')

    // Create a new database connection
    const client = postgres(databaseUrl)
    const db = drizzle(client)

    console.log('Updating chk_role_data constraint to allow super_admin role...')

    // Drop the existing constraint
    await db.execute(sql`ALTER TABLE users DROP CONSTRAINT IF EXISTS chk_role_data`)

    // Add the updated constraint
    await db.execute(sql`
      ALTER TABLE users ADD CONSTRAINT chk_role_data CHECK (
        (role = 'student' AND unique_code IS NOT NULL AND username IS NOT NULL AND password_hash IS NOT NULL) OR 
        (role = 'admin' AND google_email IS NULL AND unique_code IS NULL AND username IS NOT NULL AND password_hash IS NOT NULL) OR
        (role = 'super_admin' AND google_email IS NULL AND unique_code IS NULL AND username IS NOT NULL AND password_hash IS NOT NULL)
      )
    `)

    console.log('Constraint updated successfully!')

    // Close the connection
    await client.end()
  } catch (error) {
    console.error('Error updating constraint:', error)
  } finally {
    process.exit(0)
  }
}

updateConstraint()
