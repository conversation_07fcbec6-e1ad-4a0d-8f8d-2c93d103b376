const { drizzle } = require('drizzle-orm/postgres-js');
const postgres = require('postgres');
const dotenv = require('dotenv');

// Load environment variables from .env and .env.local
try {
  dotenv.config({ path: '.env' });
  dotenv.config({ path: '.env.local', override: true });
} catch (error) {
  console.error('Error loading environment variables:', error);
}

// Get database connection string from environment variables
const connectionString = process.env.DATABASE_URL;

if (!connectionString) {
  console.error('Error: DATABASE_URL is not set in environment variables.');
  console.error('Please run the tunnel script: npm run tunnel');
  console.error('Or set DATABASE_URL manually in .env.local');
  console.error('\nFormat DATABASE_URL: postgres://username:password@host:port/database');
  console.error('Example: postgres://postgres:postgres@localhost:5432/website');
  process.exit(1);
}

// Create a postgres client
const client = postgres(connectionString);

// Create a drizzle instance
const db = drizzle(client);

// Function to display table information
async function displayTableInfo(tableName) {
  console.log(`\n📋 Table: ${tableName}`);
  console.log('-------------------------------------------');

  try {
    // Get table structure
    const tableStructure = await client`
      SELECT column_name, data_type, is_nullable
      FROM information_schema.columns
      WHERE table_name = ${tableName}
      ORDER BY ordinal_position
    `;

    console.log('Table Structure:');
    tableStructure.forEach(column => {
      console.log(`  - ${column.column_name} (${column.data_type}, ${column.is_nullable === 'YES' ? 'NULL' : 'NOT NULL'})`);
    });

    // Get row count
    const rowCount = await client`
      SELECT COUNT(*) as count FROM ${client.unsafe(tableName)}
    `;

    console.log(`\nRow Count: ${rowCount[0].count}`);

    // Get sample data (up to 5 rows)
    if (parseInt(rowCount[0].count) > 0) {
      const sampleData = await client`
        SELECT * FROM ${client.unsafe(tableName)} LIMIT 5
      `;

      console.log('\nSample Data:');
      sampleData.forEach((row, index) => {
        console.log(`  Row ${index + 1}:`, row);
      });
    }
  } catch (error) {
    console.error(`Error getting info for table ${tableName}:`, error);
  }
}

// Function to display materialized view information
async function displayMaterializedViewInfo(viewName) {
  console.log(`\n📊 Materialized View: ${viewName}`);
  console.log('-------------------------------------------');

  try {
    // Get view structure
    const viewStructure = await client`
      SELECT column_name, data_type
      FROM information_schema.columns
      WHERE table_name = ${viewName}
      ORDER BY ordinal_position
    `;

    console.log('View Structure:');
    viewStructure.forEach(column => {
      console.log(`  - ${column.column_name} (${column.data_type})`);
    });

    // Get row count
    const rowCount = await client`
      SELECT COUNT(*) as count FROM ${client.unsafe(viewName)}
    `;

    console.log(`\nRow Count: ${rowCount[0].count}`);

    // Get sample data (up to 5 rows)
    if (parseInt(rowCount[0].count) > 0) {
      const sampleData = await client`
        SELECT * FROM ${client.unsafe(viewName)} LIMIT 5
      `;

      console.log('\nSample Data:');
      sampleData.forEach((row, index) => {
        console.log(`  Row ${index + 1}:`, row);
      });
    }
  } catch (error) {
    console.error(`Error getting info for materialized view ${viewName}:`, error);
  }
}

// Main function
async function main() {
  console.log('🔍 Database Viewer');
  console.log('===========================================');
  console.log(`Connected to: ${connectionString.replace(/:[^:]*@/, ':****@')}`);

  try {
    // Get list of tables
    const tables = await client`
      SELECT table_name
      FROM information_schema.tables
      WHERE table_schema = 'public'
      AND table_type = 'BASE TABLE'
      ORDER BY table_name
    `;

    console.log('\n📚 Tables:');
    tables.forEach(table => console.log(`  - ${table.table_name}`));

    // Get list of materialized views
    const materializedViews = await client`
      SELECT matviewname as name
      FROM pg_matviews
      ORDER BY matviewname
    `;

    console.log('\n📈 Materialized Views:');
    materializedViews.forEach(view => console.log(`  - ${view.name}`));

    // Display information for each table
    for (const table of tables) {
      await displayTableInfo(table.table_name);
    }

    // Display information for each materialized view
    for (const view of materializedViews) {
      await displayMaterializedViewInfo(view.name);
    }

    console.log('\n✅ Database inspection completed!');
  } catch (error) {
    console.error('Error inspecting database:', error);
  } finally {
    await client.end();
  }
}

main();
