// Set NODE_ENV to 'development' before importing any modules
process.env.NODE_ENV = 'development'

import 'dotenv/config'
import { drizzle } from 'drizzle-orm/postgres-js'
import postgres from 'postgres'
import { sql } from 'drizzle-orm'
import * as fs from 'fs'
import * as path from 'path'

async function applyMigration() {
  try {
    // Read DATABASE_URL from .env.local
    const envLocalPath = path.join(process.cwd(), '.env.local')
    const envLocal = fs.readFileSync(envLocalPath, 'utf8')

    // Parse DATABASE_URL from .env.local
    const databaseUrlMatch = envLocal.match(/DATABASE_URL=(.+)/)
    if (!databaseUrlMatch) {
      throw new Error('DATABASE_URL not found in .env.local')
    }

    const databaseUrl = databaseUrlMatch[1].trim()
    console.log('Using DATABASE_URL from .env.local')

    // Create a new database connection
    const client = postgres(databaseUrl)
    const db = drizzle(client)

    console.log('Applying migration to add super_admin role...')

    // Execute the ALTER TYPE statement
    await db.execute(sql`ALTER TYPE "user_role" ADD VALUE IF NOT EXISTS 'super_admin'`)

    console.log('Migration applied successfully!')

    // Close the connection
    await client.end()
  } catch (error) {
    console.error('Error applying migration:', error)
  } finally {
    process.exit(0)
  }
}

applyMigration()
