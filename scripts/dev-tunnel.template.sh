#!/bin/bash

# Simple SSH Tunnel for Development
# This script sets up SSH tunnels to access remote PostgreSQL and Redis
# TEMPLATE FILE - Copy to dev-tunnel.sh and edit with your credentials

# Configuration (edit these values)
SSH_KEY="~/.ssh/your_key_file"
SSH_USER="your_username"
SSH_HOST="your_server_ip"
DB_USER="postgres"
DB_PASSWORD="your_password_here"
DB_NAME="your_database_name"  # Nama database
REDIS_PASSWORD="your_redis_password" # Password untuk Redis

# Ports - Gunakan port yang sama dengan EasyPanel untuk konsistensi
PG_LOCAL_PORT=5432
PG_REMOTE_PORT=5432
REDIS_LOCAL_PORT=6379
REDIS_REMOTE_PORT=6379

# Expand SSH key path
SSH_KEY="${SSH_KEY/#\~/$HOME}"

# Check if SSH key exists
if [ ! -f "$SSH_KEY" ]; then
  echo "❌ SSH key not found at $SSH_KEY"
  exit 1
fi

# Function to check if a port is in use
is_port_in_use() {
  nc -z localhost $1 >/dev/null 2>&1
  return $?
}

# Function to find and kill process using a specific port
kill_process_on_port() {
  local port=$1
  local pid=$(lsof -ti:$port)
  
  if [ -n "$pid" ]; then
    echo "🔄 Port $port is in use by process $pid. Attempting to kill..."
    kill -15 $pid 2>/dev/null || kill -9 $pid 2>/dev/null
    sleep 1
    
    # Check if port is still in use
    if is_port_in_use $port; then
      echo "❌ Failed to free port $port. Please close the application using this port manually."
      return 1
    else
      echo "✅ Successfully freed port $port"
      return 0
    fi
  fi
  
  return 0
}

# Function to kill existing SSH tunnel
kill_tunnel() {
  echo "🔄 Stopping SSH tunnels..."
  tunnel_pid=$(pgrep -f "ssh -i $SSH_KEY -N -L $PG_LOCAL_PORT:localhost:$PG_REMOTE_PORT -L $REDIS_LOCAL_PORT:localhost:$REDIS_REMOTE_PORT $SSH_USER@$SSH_HOST")
  
  if [ -n "$tunnel_pid" ]; then
    kill "$tunnel_pid"
    echo "✅ SSH tunnels stopped (PID: $tunnel_pid)"
    # Wait for ports to be released
    sleep 1
  else
    echo "ℹ️ No active SSH tunnels found with exact match"
    
    # Look for any SSH tunnels on these ports
    pg_tunnel=$(pgrep -f "ssh.*$PG_LOCAL_PORT")
    redis_tunnel=$(pgrep -f "ssh.*$REDIS_LOCAL_PORT")
    
    if [ -n "$pg_tunnel" ]; then
      echo "🔄 Found SSH tunnel using PostgreSQL port (PID: $pg_tunnel). Stopping..."
      kill "$pg_tunnel"
    fi
    
    if [ -n "$redis_tunnel" ]; then
      echo "🔄 Found SSH tunnel using Redis port (PID: $redis_tunnel). Stopping..."
      kill "$redis_tunnel"
    fi
    
    # Wait for ports to be released
    sleep 1
  fi
}

# Handle command line arguments first to avoid unnecessary checks
if [ "$1" = "stop" ]; then
  kill_tunnel
  exit 0
fi

if [ "$1" = "status" ]; then
  # Cari tunnel SSH dengan pola yang lebih umum
  tunnel_pid=$(pgrep -f "ssh.*-L $PG_LOCAL_PORT.*-L $REDIS_LOCAL_PORT.*$SSH_HOST")
  
  if [ -z "$tunnel_pid" ]; then
    # Coba pola pencarian alternatif jika tidak ditemukan
    tunnel_pid=$(ps aux | grep "ssh.*-L $PG_LOCAL_PORT.*-L $REDIS_LOCAL_PORT.*$SSH_HOST" | grep -v grep | awk '{print $2}')
  fi
  
  if [ -n "$tunnel_pid" ]; then
    echo "✅ SSH tunnels are running (PID: $tunnel_pid)"
    echo "   - PostgreSQL: localhost:$PG_LOCAL_PORT -> $SSH_HOST -> localhost:$PG_REMOTE_PORT"
    echo "   - Redis: localhost:$REDIS_LOCAL_PORT -> $SSH_HOST -> localhost:$REDIS_REMOTE_PORT"
    
    # Check if ports are actually accessible
    if nc -z localhost $PG_LOCAL_PORT >/dev/null 2>&1 && nc -z localhost $REDIS_LOCAL_PORT >/dev/null 2>&1; then
      echo "✅ All ports are accessible"
    else
      echo "⚠️ Tunnel is running but ports may not be accessible"
      if ! nc -z localhost $PG_LOCAL_PORT >/dev/null 2>&1; then
        echo "   - PostgreSQL port $PG_LOCAL_PORT is not accessible"
      fi
      if ! nc -z localhost $REDIS_LOCAL_PORT >/dev/null 2>&1; then
        echo "   - Redis port $REDIS_LOCAL_PORT is not accessible"
      fi
      echo "   Try restarting the tunnel with: bash scripts/dev-tunnel.sh restart"
    fi
  else
    echo "❌ SSH tunnels are not running"
    
    # Check if ports are in use by other processes
    if is_port_in_use $PG_LOCAL_PORT; then
      pg_pid=$(lsof -ti:$PG_LOCAL_PORT)
      echo "⚠️ PostgreSQL port $PG_LOCAL_PORT is in use by process $pg_pid"
    fi
    
    if is_port_in_use $REDIS_LOCAL_PORT; then
      redis_pid=$(lsof -ti:$REDIS_LOCAL_PORT)
      echo "⚠️ Redis port $REDIS_LOCAL_PORT is in use by process $redis_pid"
    fi
  fi
  exit 0
fi

if [ "$1" = "clean" ]; then
  echo "🔄 Cleaning up all SSH tunnels and port bindings..."
  
  # Kill SSH tunnel
  kill_tunnel
  
  # Force kill any process using our ports
  kill_process_on_port $PG_LOCAL_PORT
  kill_process_on_port $REDIS_LOCAL_PORT
  
  echo "✅ Cleanup completed"
  exit 0
fi

if [ "$1" = "restart" ]; then
  kill_tunnel
  # Continue with the script to start a new tunnel
fi

# Check if tunnels are already running
if pgrep -f "ssh -i $SSH_KEY -N -L $PG_LOCAL_PORT:localhost:$PG_REMOTE_PORT -L $REDIS_LOCAL_PORT:localhost:$REDIS_REMOTE_PORT $SSH_USER@$SSH_HOST" > /dev/null; then
  echo "✅ SSH tunnels are already running"
else
  # Check if ports are already in use and try to free them
  if is_port_in_use $PG_LOCAL_PORT; then
    echo "⚠️ Port $PG_LOCAL_PORT is already in use."
    kill_process_on_port $PG_LOCAL_PORT || {
      echo "❌ Please choose a different PostgreSQL port or free port $PG_LOCAL_PORT manually."
      exit 1
    }
  fi
  
  if is_port_in_use $REDIS_LOCAL_PORT; then
    echo "⚠️ Port $REDIS_LOCAL_PORT is already in use."
    kill_process_on_port $REDIS_LOCAL_PORT || {
      echo "❌ Please choose a different Redis port or free port $REDIS_LOCAL_PORT manually."
      exit 1
    }
  fi

  # Start SSH tunnel
  echo "🔄 Starting SSH tunnels..."
  ssh -i "$SSH_KEY" -N -f -L "$PG_LOCAL_PORT":localhost:"$PG_REMOTE_PORT" -L "$REDIS_LOCAL_PORT":localhost:"$REDIS_REMOTE_PORT" "$SSH_USER"@"$SSH_HOST"
  
  if [ $? -eq 0 ]; then
    echo "✅ SSH tunnels established successfully:"
    echo "   - PostgreSQL: localhost:$PG_LOCAL_PORT -> $SSH_HOST -> localhost:$PG_REMOTE_PORT"
    echo "   - Redis: localhost:$REDIS_LOCAL_PORT -> $SSH_HOST -> localhost:$REDIS_REMOTE_PORT"
  else
    echo "❌ Failed to establish SSH tunnels. Please check your SSH key and connection."
    exit 1
  fi
fi

# Wait for ports to be available
echo "🔄 Checking port availability..."
for i in {1..3}; do
  if nc -z localhost "$PG_LOCAL_PORT" > /dev/null 2>&1 && nc -z localhost "$REDIS_LOCAL_PORT" > /dev/null 2>&1; then
    echo "✅ All ports are accessible"
    break
  fi
  if [ $i -eq 3 ]; then
    echo "❌ Timeout waiting for ports to be accessible"
    exit 1
  fi
  echo "⏳ Waiting... ($i/3)"
  sleep 1
done

# Construct Redis URL with password
REDIS_URL_VALUE="redis://default:$REDIS_PASSWORD@localhost:$REDIS_LOCAL_PORT"

# Update .env.local with the correct values
if [ -f ".env.local" ]; then
  # Create backup
  cp .env.local .env.local.bak
  
  # Update or add DATABASE_URL
  if grep -q "^DATABASE_URL=" .env.local; then
    sed -i.tmp "s|^DATABASE_URL=.*$|DATABASE_URL=postgres://$DB_USER:$DB_PASSWORD@localhost:$PG_LOCAL_PORT/$DB_NAME|" .env.local
  else
    echo "DATABASE_URL=postgres://$DB_USER:$DB_PASSWORD@localhost:$PG_LOCAL_PORT/$DB_NAME" >> .env.local
  fi
  
  # Update or add REDIS_URL
  if grep -q "^REDIS_URL=" .env.local; then
    sed -i.tmp "s|^REDIS_URL=.*$|REDIS_URL=$REDIS_URL_VALUE|" .env.local
  else
    echo "REDIS_URL=$REDIS_URL_VALUE" >> .env.local
  fi
  
  # Remove temporary files
  rm -f .env.local.tmp
  
  echo "✅ Updated .env.local with tunnel configuration"
else
  # Create new .env.local
  cat > .env.local << EOF
# Generated by dev-tunnel.sh - DO NOT COMMIT THIS FILE

# Database (SSH Tunnel)
DATABASE_URL=postgres://$DB_USER:$DB_PASSWORD@localhost:$PG_LOCAL_PORT/$DB_NAME

# Redis (SSH Tunnel)
REDIS_URL=$REDIS_URL_VALUE

# Environment
NODE_ENV=development
EOF
  
  echo "✅ Created .env.local with tunnel configuration"
fi

# Show usage information
echo ""
echo "🚀 SSH tunnels are ready!"
echo ""
echo "To stop the tunnels, run:"
echo "$ bash scripts/dev-tunnel.sh stop"
echo ""
echo "To check tunnel status, run:"
echo "$ bash scripts/dev-tunnel.sh status"
echo ""
echo "To restart the tunnels, run:"
echo "$ bash scripts/dev-tunnel.sh restart"
echo ""
echo "To clean up all tunnels and ports, run:"
echo "$ bash scripts/dev-tunnel.sh clean"
echo ""
