import 'dotenv/config'
import { drizzle } from 'drizzle-orm/postgres-js'
import postgres from 'postgres'
import { eq, and, isNotNull, or } from 'drizzle-orm'
import * as fs from 'fs'
import * as path from 'path'
import * as schema from '@/lib/data/drizzle/schema'

async function fixAdminClassIds() {
  try {
    // Read DATABASE_URL from .env.local
    const envLocalPath = path.join(process.cwd(), '.env.local')
    const envLocal = fs.readFileSync(envLocalPath, 'utf8')

    // Parse DATABASE_URL from .env.local
    const databaseUrlMatch = envLocal.match(/DATABASE_URL=(.+)/)
    if (!databaseUrlMatch) {
      throw new Error('DATABASE_URL not found in .env.local')
    }

    const databaseUrl = databaseUrlMatch[1].trim()
    console.log('Using DATABASE_URL from .env.local')

    // Create a new database connection
    const client = postgres(databaseUrl)
    const db = drizzle(client)

    console.log('Finding admin users with non-null classId...')

    // Find all admin and super_admin users with non-null classId
    const adminsWithClassId = await db
      .select()
      .from(schema.users)
      .where(
        and(
          isNotNull(schema.users.classId),
          or(eq(schema.users.role, 'admin'), eq(schema.users.role, 'super_admin'))
        )
      )

    if (adminsWithClassId.length === 0) {
      console.log('No admin users with non-null classId found.')
      await client.end()
      return
    }

    console.log(`Found ${adminsWithClassId.length} admin users with non-null classId:`)
    adminsWithClassId.forEach(admin => {
      console.log(
        `- ID: ${admin.id}, Username: ${admin.username}, Role: ${admin.role}, ClassId: ${admin.classId}`
      )
    })

    // Update all admin and super_admin users to have null classId
    const result = await db
      .update(schema.users)
      .set({
        classId: null,
        updatedAt: new Date(),
      })
      .where(
        and(
          isNotNull(schema.users.classId),
          or(eq(schema.users.role, 'admin'), eq(schema.users.role, 'super_admin'))
        )
      )
      .returning()

    console.log(`Updated ${result.length} admin users to have null classId.`)

    // Close the connection
    await client.end()
    console.log('Database connection closed.')
  } catch (error) {
    console.error('Error fixing admin class IDs:', error)
  } finally {
    process.exit(0)
  }
}

fixAdminClassIds()
