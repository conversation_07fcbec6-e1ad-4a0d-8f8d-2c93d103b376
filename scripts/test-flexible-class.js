// test-flexible-class.js
// Test script to verify that flexible class naming now works
require('dotenv').config();
const { Pool } = require('pg');

async function testFlexibleClassNames() {
    console.log('Testing flexible class names...');

    // Create a database connection
    const pool = new Pool({
        connectionString: process.env.DATABASE_URL,
    });

    try {
        // Test case 1: Simple class name (just a level)
        console.log('Testing insertion of class name "X"...');
        const res1 = await pool.query(
            'INSERT INTO classes (name) VALUES ($1) RETURNING *',
            ['X']
        );
        console.log('✓ Successfully created class with name "X":', res1.rows[0]);

        // Test case 2: Alphanumeric format
        console.log('Testing insertion of class name "10A"...');
        const res2 = await pool.query(
            'INSERT INTO classes (name) VALUES ($1) RETURNING *',
            ['10A']
        );
        console.log('✓ Successfully created class with name "10A":', res2.rows[0]);

        // Test case 3: Custom format
        console.log('Testing insertion of class name "Kelas 12 IPA"...');
        const res3 = await pool.query(
            'INSERT INTO classes (name) VALUES ($1) RETURNING *',
            ['Kelas 12 IPA']
        );
        console.log('✓ Successfully created class with name "Kelas 12 IPA":', res3.rows[0]);

        // Clean up the test classes
        for (const row of [res1.rows[0], res2.rows[0], res3.rows[0]]) {
            await pool.query('DELETE FROM classes WHERE id = $1', [row.id]);
            console.log(`✓ Cleaned up test class "${row.name}" with ID ${row.id}`);
        }

        console.log('All tests completed successfully!');
        console.log('Flexible class naming is now working as expected.');

    } catch (error) {
        console.error('Test failed:', error);
    } finally {
        await pool.end();
        process.exit(0);
    }
}

testFlexibleClassNames(); 