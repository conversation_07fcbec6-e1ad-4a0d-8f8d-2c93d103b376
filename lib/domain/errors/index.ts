/**
 * Base error class for domain-specific errors
 */
export class DomainError extends Error {
  constructor(message: string) {
    super(message)
    this.name = this.constructor.name
    Error.captureStackTrace(this, this.constructor)
  }
}

/**
 * Error thrown when a requested entity is not found
 */
export class NotFoundError extends DomainError {
  constructor(message: string) {
    super(message)
  }
}

/**
 * Error thrown when input validation fails
 */
export class ValidationError extends DomainError {
  constructor(message: string) {
    super(message)
  }
}

/**
 * Error thrown when authentication fails
 */
export class AuthenticationError extends DomainError {
  constructor(message: string) {
    super(message)
  }
}

/**
 * Error thrown when a user is not authorized to perform an action
 */
export class AuthorizationError extends DomainError {
  constructor(message: string) {
    super(message)
  }
}

/**
 * Error thrown when a duplicate entity is detected
 */
export class DuplicateError extends DomainError {
  constructor(message: string) {
    super(message)
  }
}

/**
 * Error thrown when there is a conflict with an existing resource
 * Used for cases like unique constraint violations, concurrent updates, etc.
 */
export class ConflictError extends DomainError {
  constructor(message: string) {
    super(message)
  }
}
