/**
 * Admin entity representing an administrator user in the system
 */
export interface Admin {
  id: number
  username: string
  name: string
  passwordHash: string
  role: 'admin' | 'super_admin'
  createdAt: Date
  updatedAt?: Date
}

/**
 * Data required to create a new admin
 */
export interface CreateAdminDTO {
  username: string
  name: string
  password: string // Plain text password, will be hashed before storage
  role?: 'admin' | 'super_admin' // Optional role field, defaults to 'admin' if not specified
}

/**
 * Data for updating an admin's profile
 */
export interface UpdateAdminDTO {
  name?: string
  password?: string // Plain text password, will be hashed before storage
}
