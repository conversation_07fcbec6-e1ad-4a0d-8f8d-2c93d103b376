/**
 * Attendance type enum
 */
export enum AttendanceType {
  ZUHR = 'Zuhr',
  ASR = 'Asr',
  DISMISSAL = 'Pulang',
  IJIN = 'Ijin',
}

/**
 * Absence entity representing a student's attendance record
 */
export interface Absence {
  id: number
  uniqueCode: string // References Student.uniqueCode
  type: AttendanceType
  recordedAt: Date // Timestamp when the attendance was recorded
  createdAt: Date
}

/**
 * Data required to create a new absence record
 */
export interface CreateAbsenceDTO {
  uniqueCode: string
  type: AttendanceType
  recordedAt: Date
}

/**
 * Attendance summary for reporting
 */
export interface AttendanceSummary {
  summaryDate: Date
  uniqueCode: string
  name: string
  className: string
  zuhr: boolean
  asr: boolean
  dismissal: boolean
  ijin: boolean
  updatedAt: Date
}
