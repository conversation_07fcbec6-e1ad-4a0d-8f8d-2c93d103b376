import { Student, CreateStudentDTO, UpdateStudentDTO } from '../entities/student'
import { Admin, CreateAdminDTO, UpdateAdminDTO } from '../entities/admin'
import { Class } from '../entities/class'
import { NotFoundError, DuplicateError } from '../errors'
import { v4 as uuidv4 } from 'uuid'
import { hashPassword } from '@/lib/utils/auth' // Assuming this utility exists or will be created
import { AbsenceRepository } from './absence' // Add this import
import * as bcrypt from 'bcrypt'

/**
 * Interface for student repository
 */
export interface StudentRepository {
  findAll(): Promise<Student[]>
  findById(id: number): Promise<Student | null>
  findByUniqueCode(uniqueCode: string): Promise<Student | null>
  create(
    studentData: Omit<Student, 'id' | 'createdAt' | 'updatedAt' | 'className'>
  ): Promise<Student> // Adjusted for full student creation
  update(id: number, data: UpdateStudentDTO): Promise<Student>
  delete(id: number): Promise<void>
  hasAttendanceRecords(uniqueCode: string): Promise<boolean>
}

/**
 * Interface for admin repository
 */
export interface AdminRepository {
  findAll(): Promise<Admin[]>
  findById(id: number): Promise<Admin | null>
  findByUsername(username: string): Promise<Admin | null>
  create(adminData: Omit<Admin, 'id' | 'createdAt' | 'updatedAt'>): Promise<Admin> // Expects passwordHash
  update(id: number, data: UpdateAdminDTO): Promise<Admin>
  delete(id: number): Promise<void>
}

/**
 * Interface for class repository
 */
export interface ClassRepository {
  findAll(): Promise<Class[]>
  findById(id: number): Promise<Class | null>
  findByName(name: string): Promise<Class | null>
  create(className: string): Promise<Class>
  update(id: number, className: string): Promise<Class>
  delete(id: number): Promise<void>
}

/**
 * Interface for cache service
 */
export interface CacheService {
  get(key: string): Promise<string | null>
  set(key: string, value: string, ttlSeconds: number): Promise<void>
  del(key: string): Promise<void>
}

/**
 * User management use cases
 */
export class UserUseCases {
  constructor(
    private studentRepo: StudentRepository,
    private adminRepo: AdminRepository,
    private classRepo: ClassRepository,
    private cache: CacheService,
    private absenceRepo: AbsenceRepository
  ) {}

  /**
   * Get all users (students and admins)
   */
  async getAllUsers(): Promise<(Student | Admin)[]> {
    // Try to get from cache first
    const cacheKey = 'users:all'
    const cachedData = await this.cache.get(cacheKey)

    if (cachedData) {
      return JSON.parse(cachedData)
    }

    // If not in cache, get from database
    const students = await this.studentRepo.findAll()
    const admins = await this.adminRepo.findAll()
    const users = [...students, ...admins]

    // Cache the result for 5 minutes
    await this.cache.set(cacheKey, JSON.stringify(users), 5 * 60)

    return users
  }

  /**
   * Create a new admin
   */
  async createAdmin(data: CreateAdminDTO): Promise<Admin> {
    // Check if username already exists
    const existingAdmin = await this.adminRepo.findByUsername(data.username)

    if (existingAdmin) {
      throw new DuplicateError('Username already exists')
    }

    const passwordHash = await hashPassword(data.password)

    // Create the admin
    const admin = await this.adminRepo.create({
      ...data,
      passwordHash,
      role: data.role || 'admin', // Use the provided role or default to 'admin'
    })

    // Invalidate cache
    await this.cache.del('users:all')

    return admin
  }

  /**
   * Create a new student
   */
  async createStudent(data: CreateStudentDTO): Promise<Student> {
    // Check if username already exists (for students)
    const allStudents = await this.studentRepo.findAll()
    if (allStudents.some(s => s.username === data.username)) {
      throw new DuplicateError('Username already exists')
    }

    // Generate a unique code
    const uniqueCode = uuidv4()

    // googleEmail kini opsional untuk student (tidak digunakan untuk autentikasi)
    // Default-nya NULL kecuali secara eksplisit diberikan
    const googleEmail = data.googleEmail || null

    // Check if uniqueCode already exists (extremely unlikely but good practice)
    const existingByUniqueCode = await this.studentRepo.findByUniqueCode(uniqueCode)
    if (existingByUniqueCode) {
      throw new DuplicateError('Generated Unique Code already exists, please try again.')
    }

    // Hash the password
    const passwordHash = await hashPassword(data.password)

    const studentToCreate: Omit<Student, 'id' | 'createdAt' | 'updatedAt' | 'className'> & {
      passwordHash: string
      username: string
    } = {
      ...data, // name, username, classId?, nis?, whatsapp?
      username: data.username,
      passwordHash,
      uniqueCode,
      googleEmail, // Opsional, bisa NULL
      role: 'student', // Add role explicitly
    }

    return this.studentRepo.create(studentToCreate)
  }

  /**
   * Update a student
   */
  async updateStudent(id: number, data: UpdateStudentDTO): Promise<Student> {
    // Check if student exists
    const student = await this.studentRepo.findById(id)

    if (!student) {
      throw new NotFoundError('Student not found')
    }

    // Update the student
    const updatedStudent = await this.studentRepo.update(id, data)

    // Invalidate cache
    await this.cache.del('users:all')
    await this.cache.del(`student:profile:${student.uniqueCode}`)
    await this.cache.del(`student:id:${id}`)

    return updatedStudent
  }

  /**
   * Update an admin
   */
  async updateAdmin(id: number, data: UpdateAdminDTO): Promise<Admin> {
    // Check if admin exists
    const admin = await this.adminRepo.findById(id)

    if (!admin) {
      throw new NotFoundError('Admin not found')
    }

    // Create a copy of the data to avoid modifying the original
    const updateData: any = { ...data }

    // Hash password if provided
    if (data.password) {
      const salt = await bcrypt.genSalt(10)
      updateData.passwordHash = await bcrypt.hash(data.password, salt)
      delete updateData.password // Remove plain text password
    }

    // Update the admin
    const updatedAdmin = await this.adminRepo.update(id, updateData)

    // Invalidate cache
    await this.cache.del('users:all')

    return updatedAdmin
  }

  /**
   * Delete a user (student or admin)
   */
  async deleteUser(
    id: number,
    role: 'student' | 'admin' | 'super_admin',
    deleteAttendanceRecords: boolean = true
  ): Promise<void> {
    if (role === 'student') {
      // Check if student exists
      const student = await this.studentRepo.findById(id)

      if (!student) {
        throw new NotFoundError('Student not found')
      }

      try {
        // Delete attendance records for this student if specified
        if (deleteAttendanceRecords) {
          await this.absenceRepo.deleteAllByUniqueCode(student.uniqueCode)
          console.info(
            `Deleted all attendance records for student ${student.name} (${student.uniqueCode})`
          )
        }

        // Then delete the student
        await this.studentRepo.delete(id)
        console.info(`Student ${student.name} (ID: ${id}) deleted successfully`)

        // Invalidate cache
        await this.cache.del('users:all')
        await this.cache.del(`student:profile:${student.uniqueCode}`)
        await this.cache.del(`student:id:${id}`)
      } catch (error: any) {
        console.error(`Error deleting student ${id}:`, error)
        throw new Error(`Failed to delete student: ${error.message}`)
      }
    } else if (role === 'admin' || role === 'super_admin') {
      // Check if admin exists
      const admin = await this.adminRepo.findById(id)

      if (!admin) {
        throw new NotFoundError('Admin not found')
      }

      // Delete the admin
      await this.adminRepo.delete(id)

      // Invalidate cache
      await this.cache.del('users:all')
    }
  }

  /**
   * Get all classes
   */
  async getAllClasses(): Promise<Class[]> {
    // Try to get from cache first
    const cacheKey = 'classes:all'
    const cachedData = await this.cache.get(cacheKey)

    if (cachedData) {
      return JSON.parse(cachedData)
    }

    // If not in cache, get from database
    const classes = await this.classRepo.findAll()

    // Cache the result for 1 hour
    await this.cache.set(cacheKey, JSON.stringify(classes), 60 * 60)

    return classes
  }

  /**
   * Create a new class
   */
  async createClass(className: string): Promise<Class> {
    // Check if class already exists
    const existingClass = await this.classRepo.findByName(className)

    if (existingClass) {
      throw new DuplicateError('Class already exists')
    }

    // Create the class
    const newClass = await this.classRepo.create(className)

    // Invalidate cache
    await this.cache.del('classes:all')

    return newClass
  }

  /**
   * Update a class
   */
  async updateClass(id: number, className: string): Promise<Class> {
    // Check if class exists
    const existingClass = await this.classRepo.findById(id)
    if (!existingClass) {
      throw new NotFoundError('Class not found')
    }

    // Check if the new name already exists (and isn't the same class)
    const duplicateClass = await this.classRepo.findByName(className)
    if (duplicateClass && duplicateClass.id !== id) {
      throw new DuplicateError('A class with this name already exists')
    }

    // Update the class
    const updatedClass = await this.classRepo.update(id, className)

    // Invalidate cache
    await this.cache.del('classes:all')

    return updatedClass
  }

  /**
   * Delete a class
   */
  async deleteClass(id: number): Promise<void> {
    // Check if class exists
    const existingClass = await this.classRepo.findById(id)
    if (!existingClass) {
      throw new NotFoundError('Class not found')
    }

    // Delete the class
    await this.classRepo.delete(id)

    // Invalidate cache
    await this.cache.del('classes:all')
  }

  /**
   * Find a class by name or create it if it doesn't exist
   */
  async findOrCreateClass(className: string): Promise<Class> {
    // Check if class already exists
    const existingClass = await this.classRepo.findByName(className)

    if (existingClass) {
      return existingClass
    }

    // If not found, create the class
    const newClass = await this.classRepo.create(className)

    // Invalidate cache
    await this.cache.del('classes:all')

    return newClass
  }

  /**
   * Check if a student has attendance records without deleting
   */
  async checkStudentHasAttendanceRecords(
    id: number
  ): Promise<{ hasRecords: boolean; uniqueCode: string; name: string }> {
    // Check if student exists
    const student = await this.studentRepo.findById(id)

    if (!student) {
      throw new NotFoundError('Student not found')
    }

    // Check if the student has attendance records
    const hasAttendanceRecords = await this.studentRepo.hasAttendanceRecords(student.uniqueCode)

    return {
      hasRecords: hasAttendanceRecords,
      uniqueCode: student.uniqueCode,
      name: student.name,
    }
  }

  /**
   * Get a user by ID (can be Student or Admin)
   */
  async getUserById(id: number): Promise<Student | Admin | null> {
    // Try fetching as Student first
    let user: Student | Admin | null = await this.studentRepo.findById(id)
    if (user && user.role === 'student') {
      // Ensure it's actually a student if found by general user ID
      return user
    }
    // If not found as student or not a student, try fetching as Admin
    user = await this.adminRepo.findById(id)
    if (user && (user.role === 'admin' || user.role === 'super_admin')) {
      // Ensure it's an admin or super_admin
      return user
    }
    return null // Not found as either
  }

  /**
   * Change student password by admin (Super Admin only)
   * This method allows Super Admin to change a student's password without requiring the old password
   */
  async changeStudentPasswordByAdmin(
    studentId: number,
    newPassword: string,
    adminId: number
  ): Promise<void> {
    // Verify the student exists
    const student = await this.studentRepo.findById(studentId)

    if (!student) {
      throw new NotFoundError('Student not found')
    }

    // Verify the admin exists and is a Super Admin
    const admin = await this.adminRepo.findById(adminId)

    if (!admin) {
      throw new NotFoundError('Admin not found')
    }

    if (admin.role !== 'super_admin') {
      throw new Error('Only Super Admin can change student passwords')
    }

    // Update the student's password using the repository
    // We use the StudentRepository's updateCredentials method
    const studentRepo = this.studentRepo as any
    if (typeof studentRepo.updateCredentials === 'function') {
      await studentRepo.updateCredentials(studentId, undefined, newPassword)
    } else {
      throw new Error('Password update method not available')
    }

    // Invalidate relevant caches for security
    await this.cache.del(`student:id:${studentId}`)
    await this.cache.del(`student:username:${student.username}`)
    await this.cache.del('users:all')

    // Log the action for audit purposes (without logging the actual password)
    console.info(`Password changed for student ID ${studentId} by Super Admin ID ${adminId}`)
  }
}
