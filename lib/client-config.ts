/**
 * Client-side API Configuration - Security First
 * 
 * Provides secure, production-ready API client configuration
 * with proper error handling and security headers.
 */

import { getApiBaseUrl, getDomainType } from '@/lib/utils/domain'

/**
 * API endpoint configuration
 */
export const API_ENDPOINTS = {
  // Authentication endpoints
  auth: {
    student: {
      login: '/api/auth/student/login',
      logout: '/api/auth/logout',
      checkAuth: '/api/student/check-auth',
    },
    admin: {
      login: '/api/auth/admin/login',
      logout: '/api/auth/logout',
      session: '/api/auth/admin/session',
    },
    common: {
      checkSession: '/api/auth/check-session',
      sessionEvents: '/api/auth/session-events',
      forceLogout: '/api/auth/force-logout',
    },
  },
  
  // Student endpoints
  student: {
    profile: '/api/student/profile',
    whatsapp: {
      sendOtp: '/api/student/whatsapp/send-otp',
      verifyOtp: '/api/student/whatsapp/verify-otp',
    },
  },
  
  // Admin endpoints
  admin: {
    sessions: {
      list: '/api/admin/sessions',
      stats: '/api/admin/sessions/stats',
    },
    users: '/api/users',
    classes: '/api/classes',
    students: {
      list: '/api/students',
      count: '/api/students/count',
      bulkQrDownload: '/api/students/bulk-qr-download',
    },
    admins: '/api/admins',
  },
  
  // Common endpoints
  common: {
    absence: {
      record: '/api/absence/record',
      check: '/api/absence/check',
      reports: '/api/absence/reports',
    },
    refreshSummary: '/api/refresh-summary',
  },
} as const

/**
 * Default fetch options with security headers
 */
export const DEFAULT_FETCH_OPTIONS: RequestInit = {
  credentials: 'include', // Always include cookies for authentication
  headers: {
    'Content-Type': 'application/json',
    'Cache-Control': 'no-cache, no-store, must-revalidate',
    'Pragma': 'no-cache',
  },
}

/**
 * Client API configuration object
 */
export const clientApiConfig = {
  baseUrl: getApiBaseUrl(), // Always empty for relative URLs
  endpoints: API_ENDPOINTS,
  defaultOptions: DEFAULT_FETCH_OPTIONS,
}

/**
 * Secure fetch wrapper with automatic error handling
 * @param url API endpoint URL (relative)
 * @param options Fetch options
 * @returns Promise with response
 */
export async function secureApiCall(
  url: string,
  options: RequestInit = {}
): Promise<Response> {
  // Ensure URL is relative for security
  if (url.startsWith('http://') || url.startsWith('https://')) {
    throw new Error('Absolute URLs not allowed for security reasons')
  }
  
  // Merge with default options
  const mergedOptions: RequestInit = {
    ...DEFAULT_FETCH_OPTIONS,
    ...options,
    headers: {
      ...DEFAULT_FETCH_OPTIONS.headers,
      ...options.headers,
    },
  }
  
  try {
    const response = await fetch(url, mergedOptions)
    
    // Log security events in development
    if (process.env.NODE_ENV === 'development') {
      console.log(`API Call: ${options.method || 'GET'} ${url} - ${response.status}`)
    }
    
    return response
  } catch (error) {
    console.error(`API call failed: ${url}`, error)
    throw error
  }
}

/**
 * Secure API call with JSON response parsing
 * @param url API endpoint URL (relative)
 * @param options Fetch options
 * @returns Promise with parsed JSON response
 */
export async function secureApiCallJson<T = any>(
  url: string,
  options: RequestInit = {}
): Promise<T> {
  const response = await secureApiCall(url, options)
  
  if (!response.ok) {
    const errorData = await response.json().catch(() => ({ error: 'Unknown error' }))
    throw new Error(errorData.error || `HTTP ${response.status}`)
  }
  
  return response.json()
}

/**
 * Get appropriate API endpoints based on current domain
 * @returns Filtered API endpoints for current domain type
 */
export function getContextualEndpoints() {
  const domainType = getDomainType()
  
  switch (domainType) {
    case 'student':
      return {
        auth: API_ENDPOINTS.auth.student,
        student: API_ENDPOINTS.student,
        common: API_ENDPOINTS.common,
      }
    case 'admin':
      return {
        auth: API_ENDPOINTS.auth.admin,
        admin: API_ENDPOINTS.admin,
        common: API_ENDPOINTS.common,
      }
    default:
      return API_ENDPOINTS
  }
}

/**
 * Create a typed API client for specific domain
 * @param domainType Domain type to create client for
 * @returns Typed API client object
 */
export function createApiClient(domainType: 'student' | 'admin' | 'common' = 'common') {
  const endpoints = getContextualEndpoints()
  
  return {
    // Authentication methods
    auth: {
      login: async (credentials: { username: string; password: string }) => {
        const loginEndpoint = domainType === 'student' 
          ? endpoints.auth.login 
          : API_ENDPOINTS.auth.admin.login
          
        return secureApiCallJson(loginEndpoint, {
          method: 'POST',
          body: JSON.stringify(credentials),
        })
      },
      
      logout: async () => {
        return secureApiCallJson(endpoints.auth.logout, {
          method: 'POST',
        })
      },
      
      checkSession: async (role?: string) => {
        return secureApiCallJson(API_ENDPOINTS.auth.common.checkSession, {
          method: 'POST',
          body: JSON.stringify({ role }),
        })
      },
    },
    
    // Generic API call method
    call: secureApiCall,
    callJson: secureApiCallJson,
    
    // Endpoints for reference
    endpoints,
  }
}

/**
 * Default API client instance
 */
export const apiClient = createApiClient()
