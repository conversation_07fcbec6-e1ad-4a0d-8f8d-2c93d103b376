import { NextRequest, NextResponse } from 'next/server'
import { logger, logAPIRequest, logAPIResponse, logAPIError } from '@/lib/utils/logger'

export async function withLogging(
  req: NextRequest,
  handler: (req: NextRequest) => Promise<NextResponse>
) {
  const userId = req.headers.get('x-user-id') || undefined
  const role = req.headers.get('x-user-role') || undefined
  const method = req.method
  const path = new URL(req.url).pathname

  // Get payload for logging (without sensitive data)
  let payload: any
  if (['POST', 'PUT', 'PATCH'].includes(method)) {
    try {
      const clonedReq = req.clone()
      const body = await clonedReq.json()

      // Remove sensitive fields (password, token, etc.)
      if (body) {
        const { password, token, refreshToken, ...safeBody } = body
        payload = safeBody
      }
    } catch (error) {
      // Cannot parse body or no body
    }
  }

  // Log the request
  logAPIRequest(method, path, userId, role, payload)

  const startTime = Date.now()

  try {
    // Handle the request
    const response = await handler(req)

    // Log the response
    const responseTime = Date.now() - startTime
    logAPIResponse(method, path, response.status, responseTime, userId)

    return response
  } catch (error: any) {
    // Log the error
    logAPIError(method, path, error, userId)

    // Return a 500 error response
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
