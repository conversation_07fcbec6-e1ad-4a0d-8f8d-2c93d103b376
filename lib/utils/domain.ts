/**
 * Domain Utility Functions - Security First
 * 
 * Provides secure, production-ready domain handling utilities
 * with comprehensive validation and error handling.
 */

import { clientConfig } from '@/lib/config'

/**
 * Get the current domain from window.location (client-side only)
 * @returns Current hostname or empty string if not in browser
 */
export function getCurrentDomain(): string {
  if (typeof window === 'undefined') {
    return ''
  }
  return window.location.hostname
}

/**
 * Check if current hostname is a student domain
 * @param hostname Optional hostname to check (defaults to current)
 * @returns True if hostname matches student domain pattern
 */
export function isStudentDomain(hostname?: string): boolean {
  const host = hostname || getCurrentDomain()
  if (!host) return false
  
  const studentPrefix = clientConfig.environment.studentDomainPrefix
  return host.startsWith(`${studentPrefix}.`)
}

/**
 * Check if current hostname is an admin domain
 * @param hostname Optional hostname to check (defaults to current)
 * @returns True if hostname matches admin domain pattern
 */
export function isAdminDomain(hostname?: string): boolean {
  const host = hostname || getCurrentDomain()
  if (!host) return false
  
  const adminPrefix = clientConfig.environment.adminDomainPrefix
  return host.startsWith(`${adminPrefix}.`)
}

/**
 * Get the appropriate API base URL for the current domain
 * Always returns relative URLs for security (no CORS issues)
 * @returns Empty string (relative URLs are always used)
 */
export function getApiBaseUrl(): string {
  // SECURITY: Always use relative URLs to avoid CORS issues
  // and ensure API calls work correctly across all subdomains
  return ''
}

/**
 * Validate domain configuration
 * @returns True if domain configuration is valid
 */
export function validateDomainConfig(): boolean {
  try {
    const config = clientConfig.environment
    
    // Check if required domain configuration exists
    if (!config.domain) {
      console.warn('Domain configuration missing: DOMAIN not set')
      return false
    }
    
    if (!config.studentDomain) {
      console.warn('Domain configuration missing: STUDENT_DOMAIN not set')
      return false
    }
    
    if (!config.adminDomain) {
      console.warn('Domain configuration missing: ADMIN_DOMAIN not set')
      return false
    }
    
    // Validate domain format (basic check)
    const domainRegex = /^[a-zA-Z0-9][a-zA-Z0-9-]{0,61}[a-zA-Z0-9](?:\.[a-zA-Z0-9][a-zA-Z0-9-]{0,61}[a-zA-Z0-9])*$/
    
    if (!domainRegex.test(config.domain) && !config.domain.includes('localhost')) {
      console.warn('Invalid domain format:', config.domain)
      return false
    }
    
    return true
  } catch (error) {
    console.error('Domain validation error:', error)
    return false
  }
}

/**
 * Get domain type for current hostname
 * @param hostname Optional hostname to check (defaults to current)
 * @returns Domain type: 'student', 'admin', or 'unknown'
 */
export function getDomainType(hostname?: string): 'student' | 'admin' | 'unknown' {
  if (isStudentDomain(hostname)) {
    return 'student'
  }
  if (isAdminDomain(hostname)) {
    return 'admin'
  }
  return 'unknown'
}

/**
 * Get the appropriate login URL for the current domain
 * @returns Login URL path
 */
export function getLoginUrl(): string {
  const domainType = getDomainType()
  
  switch (domainType) {
    case 'student':
      return '/student'
    case 'admin':
      return '/admin'
    default:
      return '/'
  }
}

/**
 * Get the appropriate home URL for the current domain
 * @returns Home URL path
 */
export function getHomeUrl(): string {
  const domainType = getDomainType()
  
  switch (domainType) {
    case 'student':
      return '/student/home'
    case 'admin':
      return '/admin/home'
    default:
      return '/'
  }
}

/**
 * Security utility: Check if current domain is allowed
 * @returns True if domain is in allowed list
 */
export function isAllowedDomain(): boolean {
  const hostname = getCurrentDomain()
  if (!hostname) return true // Allow server-side rendering
  
  const config = clientConfig.environment
  const allowedDomains = [
    config.domain,
    config.studentDomain,
    config.adminDomain,
    'localhost', // Allow localhost for development
  ].filter(Boolean)
  
  return allowedDomains.some(domain => 
    hostname === domain || hostname.endsWith(`.${domain}`)
  )
}

/**
 * Get domain configuration for debugging (development only)
 * @returns Domain configuration object
 */
export function getDomainConfig() {
  if (clientConfig.environment.isProduction) {
    console.warn('getDomainConfig() should not be used in production')
    return null
  }
  
  return {
    current: getCurrentDomain(),
    type: getDomainType(),
    config: clientConfig.environment,
    isValid: validateDomainConfig(),
    isAllowed: isAllowedDomain(),
  }
}
