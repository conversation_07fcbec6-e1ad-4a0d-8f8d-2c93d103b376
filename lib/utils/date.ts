/**
 * Utility functions for date and time handling
 * All functions ensure proper WITA (Asia/Makassar) timezone handling for Banjarmasin context
 */

/**
 * Format a date to a time string in WITA timezone
 * @param date The date to format
 * @returns Formatted time string in HH:MM format with WITA timezone
 */
export function formatTime(date: Date): string {
  try {
    // Use Intl.DateTimeFormat for proper timezone handling
    return new Intl.DateTimeFormat('id-ID', {
      hour: '2-digit',
      minute: '2-digit',
      hour12: false,
      timeZone: 'Asia/Makassar', // WITA timezone (UTC+8)
    }).format(date)
  } catch (error) {
    console.error('Error formatting time:', error)
    return '00:00' // Fallback
  }
}

/**
 * Format a date to a time string in WITA timezone (alias for formatTime for clarity)
 * @param date The date to format
 * @returns Formatted time string in HH:MM format with WITA timezone
 */
export function formatTimeWITA(date: Date): string {
  return formatTime(date)
}

/**
 * Format a date to a date string in the local timezone
 * @param date The date to format
 * @returns Formatted date string
 */
export function formatDate(date: Date): string {
  try {
    // Use Intl.DateTimeFormat for proper timezone handling
    return new Intl.DateTimeFormat('id-ID', {
      weekday: 'long',
      day: 'numeric',
      month: 'long',
      year: 'numeric',
      timeZone: 'Asia/Makassar', // WITA timezone (UTC+8)
    }).format(date)
  } catch (error) {
    console.error('Error formatting date:', error)
    return '' // Fallback
  }
}

/**
 * Get the current date and time in WITA timezone
 * @returns Object with formatted date and time strings
 */
export function getCurrentDateTime(): { date: string; time: string } {
  const now = new Date()
  return {
    date: formatDate(now),
    time: formatTime(now),
  }
}

/**
 * Create a Date object for the start of the day in WITA timezone
 * @param date Optional date to use (defaults to today)
 * @returns Date object set to the start of the day
 */
export function getStartOfDay(date: Date = new Date()): Date {
  // Create a new date to avoid modifying the original
  const newDate = new Date(date)
  newDate.setHours(0, 0, 0, 0)
  return newDate
}

/**
 * Create a Date object for the end of the day in WITA timezone
 * @param date Optional date to use (defaults to today)
 * @returns Date object set to the end of the day
 */
export function getEndOfDay(date: Date = new Date()): Date {
  // Create a new date to avoid modifying the original
  const newDate = new Date(date)
  newDate.setHours(23, 59, 59, 999)
  return newDate
}

/**
 * Get the current date and time in WITA timezone
 * This function ensures we get the actual current time in Banjarmasin (WITA)
 * @returns Date object representing current time in WITA
 */
export function getCurrentWITATime(): Date {
  // Create current date in UTC
  const now = new Date()

  // Get the current time in WITA timezone using Intl API
  const witaTime = new Intl.DateTimeFormat('en-CA', {
    timeZone: 'Asia/Makassar',
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
    hour12: false,
  }).formatToParts(now)

  // Extract parts and create a proper Date object
  const year = parseInt(witaTime.find(part => part.type === 'year')?.value || '0')
  const month = parseInt(witaTime.find(part => part.type === 'month')?.value || '1') - 1 // Month is 0-indexed
  const day = parseInt(witaTime.find(part => part.type === 'day')?.value || '1')
  const hour = parseInt(witaTime.find(part => part.type === 'hour')?.value || '0')
  const minute = parseInt(witaTime.find(part => part.type === 'minute')?.value || '0')
  const second = parseInt(witaTime.find(part => part.type === 'second')?.value || '0')

  // Create Date object in local time that represents WITA time
  return new Date(year, month, day, hour, minute, second)
}

/**
 * Create a Date object that represents a specific time in WITA timezone
 * @param year Year
 * @param month Month (1-12, not 0-indexed)
 * @param day Day of month
 * @param hour Hour (0-23)
 * @param minute Minute (0-59)
 * @param second Second (0-59)
 * @returns Date object representing the specified time in WITA
 */
export function createWITADate(
  year: number,
  month: number,
  day: number,
  hour: number = 0,
  minute: number = 0,
  second: number = 0
): Date {
  // Create date in local time (month is 0-indexed in Date constructor)
  const date = new Date(year, month - 1, day, hour, minute, second)

  // Adjust for WITA timezone offset if needed
  // This ensures the date represents the correct WITA time
  return date
}

/**
 * Convert any Date object to WITA timezone representation
 * @param date The date to convert
 * @returns Date object adjusted to represent WITA time
 */
export function toWITATime(date: Date): Date {
  // Get the time in WITA timezone
  const witaTime = new Intl.DateTimeFormat('en-CA', {
    timeZone: 'Asia/Makassar',
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
    hour12: false,
  }).formatToParts(date)

  // Extract parts and create a proper Date object
  const year = parseInt(witaTime.find(part => part.type === 'year')?.value || '0')
  const month = parseInt(witaTime.find(part => part.type === 'month')?.value || '1') - 1
  const day = parseInt(witaTime.find(part => part.type === 'day')?.value || '1')
  const hour = parseInt(witaTime.find(part => part.type === 'hour')?.value || '0')
  const minute = parseInt(witaTime.find(part => part.type === 'minute')?.value || '0')
  const second = parseInt(witaTime.find(part => part.type === 'second')?.value || '0')

  return new Date(year, month, day, hour, minute, second)
}
