/**
 * Session debugging utilities
 * Helps diagnose session management issues
 */

import { getRedisCache } from '@/lib/data/cache/redis'

const cache = getRedisCache()

/**
 * Debug session state for a user
 */
export async function debugUserSessions(userId: number): Promise<void> {
  console.log(`\n=== DEBUG SESSION STATE FOR USER ${userId} ===`)
  
  try {
    // Check user sessions index
    const userSessionsKey = `user_sessions:${userId}`
    const userSessionsJson = await cache.get(userSessionsKey)
    
    if (userSessionsJson) {
      const sessionIds = JSON.parse(userSessionsJson) as string[]
      console.log(`User sessions index: ${sessionIds.length} sessions`)
      
      for (const sessionId of sessionIds) {
        const sessionJson = await cache.get(`session:${sessionId}`)
        if (sessionJson) {
          const session = JSON.parse(sessionJson)
          console.log(`  - Session ${sessionId}: device=${session.deviceId}, active=${session.isActive}, expires=${session.expiresAt}`)
          
          // Check device mapping
          const deviceKey = `device_session:${userId}:${session.deviceId}`
          const mappedSessionId = await cache.get(deviceKey)
          console.log(`    Device mapping ${deviceKey}: ${mappedSessionId} ${mappedSessionId === sessionId ? '✓' : '✗'}`);
        } else {
          console.log(`  - Session ${sessionId}: NOT FOUND in Redis`)
        }
      }
    } else {
      console.log('No user sessions index found')
    }
    
    // Check for orphaned device mappings
    console.log('\nChecking for device mappings...')
    // Note: In a real implementation, you'd need to scan for device_session:${userId}:* keys
    
  } catch (error) {
    console.error('Error debugging user sessions:', error)
  }
  
  console.log('=== END DEBUG ===\n')
}

/**
 * Debug device ID generation
 */
export function debugDeviceId(userAgent: string, ipAddress: string): void {
  console.log('\n=== DEBUG DEVICE ID GENERATION ===')
  console.log('User Agent:', userAgent)
  console.log('IP Address:', ipAddress)
  
  // Import the function dynamically to avoid circular dependencies
  import('./session').then(({ generateDeviceId }) => {
    const deviceId = generateDeviceId(userAgent, ipAddress)
    console.log('Generated Device ID:', deviceId)
    console.log('=== END DEBUG ===\n')
  })
}

/**
 * Clean up orphaned sessions for a user
 */
export async function cleanupUserSessions(userId: number): Promise<number> {
  console.log(`\nCleaning up sessions for user ${userId}...`)
  
  let cleanedCount = 0
  
  try {
    // Get user sessions index
    const userSessionsKey = `user_sessions:${userId}`
    const userSessionsJson = await cache.get(userSessionsKey)
    
    if (userSessionsJson) {
      const sessionIds = JSON.parse(userSessionsJson) as string[]
      const validSessionIds: string[] = []
      
      for (const sessionId of sessionIds) {
        const sessionJson = await cache.get(`session:${sessionId}`)
        if (sessionJson) {
          const session = JSON.parse(sessionJson)
          
          // Check if session is expired
          if (new Date(session.expiresAt) < new Date()) {
            console.log(`Removing expired session: ${sessionId}`)
            await cache.del(`session:${sessionId}`)
            await cache.del(`device_session:${userId}:${session.deviceId}`)
            cleanedCount++
          } else {
            validSessionIds.push(sessionId)
          }
        } else {
          console.log(`Removing orphaned session reference: ${sessionId}`)
          cleanedCount++
        }
      }
      
      // Update user sessions index
      if (validSessionIds.length > 0) {
        await cache.set(userSessionsKey, JSON.stringify(validSessionIds), 3600)
      } else {
        await cache.del(userSessionsKey)
      }
    }
    
    console.log(`Cleaned up ${cleanedCount} sessions for user ${userId}`)
  } catch (error) {
    console.error('Error cleaning up user sessions:', error)
  }
  
  return cleanedCount
}

/**
 * Force clean all sessions for a user (emergency cleanup)
 */
export async function forceCleanUserSessions(userId: number): Promise<void> {
  console.log(`\nForce cleaning ALL sessions for user ${userId}...`)
  
  try {
    // Remove user sessions index
    await cache.del(`user_sessions:${userId}`)
    
    // Note: In a real implementation, you'd need to scan for all session keys
    // and device mapping keys for this user and remove them
    
    console.log(`Force cleaned all sessions for user ${userId}`)
  } catch (error) {
    console.error('Error force cleaning user sessions:', error)
  }
}
