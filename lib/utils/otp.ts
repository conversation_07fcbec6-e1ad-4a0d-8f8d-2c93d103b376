/**
 * OTP Utility Functions
 * This file contains utility functions for OTP generation, validation,
 * and rate limiting for the WhatsApp OTP verification system.
 */

import { CacheService } from '@/lib/domain/usecases/auth'

/**
 * Generates a secure 6-digit OTP code
 */
export function generateOTP(): string {
  // Generate a cryptographically secure 6-digit OTP
  return Math.floor(100000 + Math.random() * 900000).toString()
}

/**
 * Validates the rate limit for OTP requests
 * Prevents abuse by limiting requests per phone number
 *
 * @param phoneNumber The phone number requesting OTP
 * @param cache The cache service
 * @returns True if rate limit is not exceeded, false otherwise
 */
export async function validateRateLimit(
  phoneNumber: string,
  cache: CacheService
): Promise<boolean> {
  const rateKey = `rate:otp:${phoneNumber}`
  const attempts = await cache.get(rateKey)

  // Allow 5 OTP requests per hour
  const MAX_ATTEMPTS = 5
  const TTL = 60 * 60 // 1 hour in seconds

  if (!attempts) {
    // First attempt in this time window
    await cache.set(rateKey, '1', TTL)
    return true
  }

  const attemptCount = parseInt(attempts, 10)
  if (attemptCount >= MAX_ATTEMPTS) {
    // Rate limit exceeded
    return false
  }

  // Increment attempt count
  await cache.set(rateKey, (attemptCount + 1).toString(), TTL)
  return true
}

/**
 * Tracks failed OTP verification attempts
 * Prevents brute force attacks by limiting failed attempts
 *
 * @param phoneNumber The phone number being verified
 * @param cache The cache service
 * @returns True if attempt limit is not exceeded, false otherwise
 */
export async function trackFailedAttempt(
  phoneNumber: string,
  cache: CacheService
): Promise<boolean> {
  const failKey = `fail:otp:${phoneNumber}`
  const attempts = await cache.get(failKey)

  // Allow 5 failed OTP verifications per OTP
  const MAX_ATTEMPTS = 5
  const TTL = 10 * 60 // 10 minutes in seconds (longer than OTP validity)

  if (!attempts) {
    // First failed attempt
    await cache.set(failKey, '1', TTL)
    return true
  }

  const attemptCount = parseInt(attempts, 10)
  if (attemptCount >= MAX_ATTEMPTS) {
    // Too many failed attempts
    return false
  }

  // Increment failed attempt count
  await cache.set(failKey, (attemptCount + 1).toString(), TTL)
  return true
}

/**
 * Resets the failed attempt counter after successful verification
 *
 * @param phoneNumber The phone number that was verified
 * @param cache The cache service
 */
export async function resetFailedAttempts(phoneNumber: string, cache: CacheService): Promise<void> {
  const failKey = `fail:otp:${phoneNumber}`
  await cache.del(failKey)
}

/**
 * Formats a phone number to ensure it has the correct format (62XXXXXXXX)
 *
 * @param phoneNumber The phone number to format
 * @returns Formatted phone number
 */
export function formatPhoneNumber(phoneNumber: string): string {
  // Remove any non-digit characters
  let normalized = phoneNumber.replace(/\D/g, '')

  // Remove leading '+' if present
  if (normalized.startsWith('+')) {
    normalized = normalized.substring(1)
  }

  // Ensure it starts with '62'
  if (!normalized.startsWith('62')) {
    if (normalized.startsWith('0')) {
      // Replace leading 0 with 62
      normalized = '62' + normalized.substring(1)
    } else {
      // Add 62 prefix
      normalized = '62' + normalized
    }
  }

  return normalized
}
