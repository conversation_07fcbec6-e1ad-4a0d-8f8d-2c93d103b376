import winston from 'winston'
import 'winston-daily-rotate-file'
import path from 'path'
import fs from 'fs'

// Ensure logs directory exists
const logDir = path.join(process.cwd(), 'logs')
if (!fs.existsSync(logDir)) {
  fs.mkdirSync(logDir, { recursive: true })
}

// Define log format
const logFormat = winston.format.combine(
  winston.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss' }),
  winston.format.errors({ stack: true }),
  winston.format.splat(),
  winston.format.json()
)

// File transport with daily rotation
const fileTransport = new winston.transports.DailyRotateFile({
  dirname: logDir,
  filename: 'shalat-yuk-%DATE%.log',
  datePattern: 'YYYY-MM-DD',
  maxSize: '20m',
  maxFiles: '14d',
  level: 'info',
})

// Console transport for development
const consoleTransport = new winston.transports.Console({
  format: winston.format.combine(
    winston.format.colorize(),
    winston.format.printf(
      ({ level, message, timestamp, ...meta }) =>
        `${timestamp} ${level}: ${message} ${Object.keys(meta).length ? JSON.stringify(meta, null, 2) : ''}`
    )
  ),
})

// Create the logger
export const logger = winston.createLogger({
  level: process.env.NODE_ENV === 'production' ? 'info' : 'debug',
  format: logFormat,
  defaultMeta: { service: 'shalat-yuk' },
  transports: [fileTransport, ...(process.env.NODE_ENV !== 'production' ? [consoleTransport] : [])],
  exitOnError: false,
})

// Helper functions for common log scenarios
export const logAPIRequest = (
  method: string,
  path: string,
  userId?: string | number,
  role?: string,
  payload?: any
) => {
  logger.info(`API ${method} ${path}`, {
    userId,
    role,
    payload: payload ? JSON.stringify(payload) : undefined,
    requestType: 'api',
  })
}

export const logAPIResponse = (
  method: string,
  path: string,
  statusCode: number,
  responseTime: number,
  userId?: string | number
) => {
  logger.info(`API ${method} ${path} - ${statusCode}`, {
    userId,
    statusCode,
    responseTime: `${responseTime}ms`,
    requestType: 'api',
  })
}

export const logAPIError = (method: string, path: string, error: any, userId?: string | number) => {
  logger.error(`API Error ${method} ${path}`, {
    userId,
    error: error.message || String(error),
    stack: error.stack,
    requestType: 'api',
  })
}

export const logAuthEvent = (
  event: 'login' | 'logout' | 'refresh' | 'unauthorized',
  userId?: string | number,
  role?: string,
  details?: any
) => {
  logger.info(`Auth: ${event}`, {
    userId,
    role,
    details,
    eventType: 'auth',
  })
}

export const logSystemEvent = (event: string, details?: any) => {
  logger.info(`System: ${event}`, {
    details,
    eventType: 'system',
  })
}

export const logWarning = (message: string, context?: any) => {
  logger.warn(message, { ...context, eventType: 'warning' })
}

export default logger
