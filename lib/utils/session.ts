import { v4 as uuidv4 } from 'uuid'
import { <PERSON><PERSON>arser } from 'ua-parser-js'

/**
 * Generate a consistent device ID for the same device/browser combination
 * This ensures single-device enforcement works correctly
 */
export function generateDeviceId(userAgent: string, ipAddress: string): string {
  const parser = new UAParser(userAgent)
  const result = parser.getResult()

  // Create a consistent fingerprint from device characteristics
  const deviceFingerprint = [
    result.browser.name || 'unknown',
    result.browser.major || 'unknown', // Use major version only for consistency
    result.os.name || 'unknown',
    result.os.version || 'unknown',
    result.device.type || 'desktop',
    result.device.vendor || 'unknown',
    result.device.model || 'unknown',
    // Include IP address subnet for additional uniqueness but not full IP for privacy
    ipAddress.split('.').slice(0, 3).join('.'), // Use first 3 octets only
  ].join('|')

  // Create a deterministic hash from the fingerprint
  let hash = 0
  for (let i = 0; i < deviceFingerprint.length; i++) {
    const char = deviceFingerprint.charCodeAt(i)
    hash = (hash << 5) - hash + char
    hash = hash & hash // Convert to 32-bit integer
  }

  // Convert to positive hex string
  const deviceId = Math.abs(hash).toString(16).padStart(8, '0')

  // Add device type prefix for easier identification
  const deviceType = result.device.type || 'desktop'
  const prefix = deviceType.substring(0, 3).toLowerCase()

  return `${prefix}_${deviceId}`
}

/**
 * Parse user agent to extract device information
 */
export function parseUserAgent(userAgent: string): {
  deviceType: string
  browser: string
  os: string
  browserVersion?: string
  osVersion?: string
} {
  const parser = new UAParser(userAgent)
  const result = parser.getResult()

  return {
    deviceType: result.device.type || 'desktop',
    browser: result.browser.name || 'Unknown',
    os: result.os.name || 'Unknown',
    browserVersion: result.browser.version,
    osVersion: result.os.version,
  }
}

/**
 * Get client IP address from request headers
 */
export function getClientIpAddress(headers: Headers): string {
  // Check various headers for the real IP address
  const xForwardedFor = headers.get('x-forwarded-for')
  const xRealIp = headers.get('x-real-ip')
  const cfConnectingIp = headers.get('cf-connecting-ip')

  if (xForwardedFor) {
    // x-forwarded-for can contain multiple IPs, take the first one
    return xForwardedFor.split(',')[0].trim()
  }

  if (xRealIp) {
    return xRealIp
  }

  if (cfConnectingIp) {
    return cfConnectingIp
  }

  // Fallback to a default value
  return '127.0.0.1'
}

/**
 * Validate session duration
 */
export function validateSessionDuration(durationSeconds: number): number {
  const MIN_DURATION = 60 // 1 minute
  const MAX_DURATION = 24 * 60 * 60 // 24 hours

  if (durationSeconds < MIN_DURATION) {
    return MIN_DURATION
  }

  if (durationSeconds > MAX_DURATION) {
    return MAX_DURATION
  }

  return durationSeconds
}

/**
 * Format session duration for display
 */
export function formatSessionDuration(durationSeconds: number): string {
  const hours = Math.floor(durationSeconds / 3600)
  const minutes = Math.floor((durationSeconds % 3600) / 60)

  if (hours > 0) {
    return `${hours}h ${minutes}m`
  }

  return `${minutes}m`
}

/**
 * Check if session is about to expire (within 5 minutes)
 */
export function isSessionNearExpiry(expiresAt: Date): boolean {
  const now = new Date()
  const fiveMinutesFromNow = new Date(now.getTime() + 5 * 60 * 1000)

  return expiresAt <= fiveMinutesFromNow
}

/**
 * Calculate time until session expires
 */
export function getTimeUntilExpiry(expiresAt: Date): {
  totalSeconds: number
  hours: number
  minutes: number
  seconds: number
  isExpired: boolean
} {
  const now = new Date()
  const totalSeconds = Math.max(0, Math.floor((expiresAt.getTime() - now.getTime()) / 1000))

  const hours = Math.floor(totalSeconds / 3600)
  const minutes = Math.floor((totalSeconds % 3600) / 60)
  const seconds = totalSeconds % 60

  return {
    totalSeconds,
    hours,
    minutes,
    seconds,
    isExpired: totalSeconds === 0,
  }
}

/**
 * Generate session metadata from request
 */
export function generateSessionMetadata(
  userAgent: string,
  ipAddress: string
): {
  deviceType: string
  browser: string
  os: string
  browserVersion?: string
  osVersion?: string
  location?: string
} {
  const deviceInfo = parseUserAgent(userAgent)

  return {
    ...deviceInfo,
    // Location could be determined from IP address using a geolocation service
    // For now, we'll leave it undefined
    location: undefined,
  }
}

/**
 * Sanitize session data for client response
 */
export function sanitizeSessionForClient(session: any): any {
  // Remove sensitive information before sending to client
  const { userAgent, ipAddress, ...sanitized } = session

  return {
    ...sanitized,
    // Only include partial IP for privacy
    ipAddress: ipAddress ? `${ipAddress.split('.').slice(0, 2).join('.')}.xxx.xxx` : 'Unknown',
  }
}

/**
 * Validate device ID format
 */
export function isValidDeviceId(deviceId: string): boolean {
  // Device ID should be in format: hash-uuid
  const pattern = /^[a-zA-Z0-9]{16}-[a-f0-9]{8}$/
  return pattern.test(deviceId)
}

/**
 * Generate a secure session token
 */
export function generateSessionToken(): string {
  return uuidv4()
}

/**
 * Constants for session management
 */
export const SESSION_CONSTANTS = {
  DEFAULT_DURATION_SECONDS: 3600, // 1 hour
  MAX_SESSIONS_PER_USER: 5,
  CLEANUP_INTERVAL_MINUTES: 15,
  NEAR_EXPIRY_THRESHOLD_MINUTES: 5,
  REFRESH_THRESHOLD_MINUTES: 10, // Refresh session if accessed within 10 minutes of expiry
} as const

/**
 * Session event types for logging
 */
export enum SessionEventType {
  CREATED = 'session_created',
  VALIDATED = 'session_validated',
  REFRESHED = 'session_refreshed',
  INVALIDATED = 'session_invalidated',
  EXPIRED = 'session_expired',
  FORCE_LOGOUT = 'session_force_logout',
  CLEANUP = 'session_cleanup',
}

/**
 * Log session event
 */
export function logSessionEvent(
  eventType: SessionEventType,
  sessionId: string,
  userId: number,
  additionalData?: Record<string, any>
): void {
  console.log(`[Session] ${eventType}`, {
    sessionId,
    userId,
    timestamp: new Date().toISOString(),
    ...additionalData,
  })
}
