import { createClient } from 'redis'
import { serverConfig, IS_DEVELOPMENT } from '@/lib/config'

/**
 * Redis cache service implementation
 */
export class RedisCache {
  private client: ReturnType<typeof createClient> | null = null
  private connected: boolean = false
  private inMemoryCache: Map<string, { value: string; expiry: number }> = new Map()
  private connectionAttempted: boolean = false

  constructor() {
    // We'll initialize the client lazily when needed
    // This allows the application to start even if Redis is not available
  }

  /**
   * Initialize the Redis client if needed
   */
  private initializeClient(): void {
    // Skip if already initialized or if we've already attempted to connect
    if (this.client || this.connectionAttempted) {
      return
    }

    this.connectionAttempted = true

    // Get Redis URL from centralized configuration
    // For development, we're using SSH tunneling to connect to Redis
    // The tunnel script (scripts/dev-tunnel.sh) sets up the connection and updates .env.local
    const redisUrl = serverConfig.redis.url

    if (!redisUrl) {
      if (IS_DEVELOPMENT) {
        console.warn(
          'REDIS_URL is not set. Using in-memory cache. Please run the tunnel script: npm run tunnel'
        )
        this.connected = false
        return
      } else {
        throw new Error('REDIS_URL environment variable is required in production')
      }
    }

    // Create Redis client
    this.client = createClient({
      url: redisUrl,
      socket: {
        reconnectStrategy: retries => {
          // Exponential backoff with a maximum delay of 10 seconds
          const delay = Math.min(Math.pow(2, retries) * 100, 10000)
          return delay
        },
      },
    })

    // Set up event handlers
    this.client.on('error', err => {
      console.error('Redis Client Error:', err)
      this.connected = false
    })

    this.client.on('connect', () => {
      this.connected = true
    })

    this.client.on('reconnecting', () => {})

    // Connect to Redis
    this.connect()
  }

  /**
   * Connect to Redis
   */
  private async connect(): Promise<void> {
    if (!this.client || this.connected) {
      return
    }

    try {
      await this.client.connect()
    } catch (error) {
      console.error('Failed to connect to Redis:', error)
      // Fallback to in-memory cache if Redis connection fails
      this.connected = false
    }
  }

  /**
   * Ensure Redis is connected
   */
  private async ensureConnected(): Promise<boolean> {
    // Initialize client if needed
    this.initializeClient()

    // If we're using in-memory cache, return false
    if (!this.client) {
      return false
    }

    // If already connected, return true
    if (this.connected) {
      return true
    }

    // Try to connect
    try {
      if (!this.client.isOpen) {
        await this.client.connect()
      }
      this.connected = true
      return true
    } catch (error) {
      console.error('Failed to connect to Redis:', error)
      this.connected = false
      return false
    }
  }

  /**
   * Get a value from the cache
   */
  async get(key: string): Promise<string | null> {
    try {
      // Try to ensure Redis is connected
      const isConnected = await this.ensureConnected()

      if (this.client && isConnected) {
        console.log(`Redis GET: ${key}`)
        return await this.client.get(key)
      } else {
        console.log(`In-memory GET: ${key} (Redis not available)`)
        // Fallback to in-memory cache
        const item = this.inMemoryCache.get(key)
        if (!item) {
          return null
        }

        // Check if the item has expired
        if (item.expiry < Date.now()) {
          this.inMemoryCache.delete(key)
          return null
        }

        return item.value
      }
    } catch (error) {
      console.error(`Redis GET error for key ${key}:`, error)

      // Fallback to in-memory cache on error
      console.log(`Fallback to in-memory GET after Redis error: ${key}`)
      const item = this.inMemoryCache.get(key)
      if (!item || item.expiry < Date.now()) {
        return null
      }
      return item.value
    }
  }

  /**
   * Set a value in the cache with TTL
   */
  async set(key: string, value: string, ttlSeconds: number): Promise<void> {
    try {
      // Try to ensure Redis is connected
      const isConnected = await this.ensureConnected()

      if (this.client && isConnected) {
        console.log(`Redis SET: ${key} (TTL: ${ttlSeconds}s)`)
        await this.client.set(key, value, { EX: ttlSeconds })
      } else {
        console.log(`In-memory SET: ${key} (TTL: ${ttlSeconds}s, Redis not available)`)
        // Fallback to in-memory cache
        const expiry = Date.now() + ttlSeconds * 1000
        this.inMemoryCache.set(key, { value, expiry })
      }
    } catch (error) {
      console.error(`Redis SET error for key ${key}:`, error)
      // Fallback to in-memory cache on error
      console.log(`Fallback to in-memory SET after Redis error: ${key}`)
      const expiry = Date.now() + ttlSeconds * 1000
      this.inMemoryCache.set(key, { value, expiry })
    }
  }

  /**
   * Delete a value from the cache
   */
  async del(key: string): Promise<void> {
    try {
      // Try to ensure Redis is connected
      const isConnected = await this.ensureConnected()

      if (this.client && isConnected) {
        console.log(`Redis DEL: ${key}`)
        await this.client.del(key)
      } else {
        console.log(`In-memory DEL: ${key} (Redis not available)`)
        // Fallback to in-memory cache
        this.inMemoryCache.delete(key)
      }
    } catch (error) {
      console.error(`Redis DEL error for key ${key}:`, error)
      // Still try to delete from in-memory cache on error
      console.log(`Fallback to in-memory DEL after Redis error: ${key}`)
      this.inMemoryCache.delete(key)
    }
  }
}

// Singleton instance
let redisCache: RedisCache | null = null

/**
 * Get the Redis cache instance
 */
export function getRedisCache(): RedisCache {
  if (!redisCache) {
    redisCache = new RedisCache()
  }
  return redisCache
}
