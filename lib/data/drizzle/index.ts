import { drizzle } from 'drizzle-orm/postgres-js';
import postgres from 'postgres';
import { serverConfig } from '@/lib/config';

// Fallback untuk development jika DATABASE_URL tidak tersedia
const connectionString = serverConfig.database.url || (() => {
  if (serverConfig.environment.isDevelopment) {
    console.warn('DATABASE_URL tidak ditemukan. Menggunakan koneksi lokal untuk development.');
    return 'postgres://postgres:postgres@localhost:5432/postgres';
  }
  throw new Error('DATABASE_URL environment variable is required');
})();

// Create a postgres client with connection pooling
const client = postgres(connectionString, {
  max: 10, // Maximum number of connections in the pool
  idle_timeout: 20, // Max seconds a client can be idle before being closed
  connect_timeout: 10, // Max seconds to wait for a connection
});

// Create a drizzle instance
export const db = drizzle(client);

// Export the client for raw queries
export { client as pgClient };
