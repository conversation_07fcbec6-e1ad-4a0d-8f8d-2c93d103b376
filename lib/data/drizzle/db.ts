import { drizzle } from 'drizzle-orm/postgres-js';
import postgres from 'postgres';
import * as schema from './schema';
import { serverConfig, IS_DEVELOPMENT } from '@/lib/config';

// Get database connection string from centralized configuration
// For development, we're using SSH tunneling to connect to the database
// The tunnel script (scripts/dev-tunnel.sh) sets up the connection and updates .env.local
const connectionString = serverConfig.database.url;

// Check for missing connection string
if (!connectionString) {
  if (IS_DEVELOPMENT) {
    console.warn('DATABASE_URL is not set. Please run the tunnel script: npm run tunnel');
  } else {
    throw new Error('DATABASE_URL environment variable is required');
  }
}

// Create a function to get the database client
// This allows us to handle the case where connectionString is undefined
function getClient() {
  if (!connectionString) {
    throw new Error('Database connection not available. Please run the tunnel script: npm run tunnel');
  }

  return postgres(connectionString, {
    max: 10, // Maximum number of connections
    idle_timeout: 30, // Idle connection timeout in seconds
    connect_timeout: 10, // Connection timeout in seconds
  });
}

// Create a lazy-loaded client and drizzle instance
// This ensures we only try to connect when the database is actually used
let _client: ReturnType<typeof postgres> | null = null;
let _db: ReturnType<typeof drizzle> | null = null;

// Export a proxy that initializes the client on first use
export const db = new Proxy({} as ReturnType<typeof drizzle>, {
  get: (_, prop) => {
    if (!_db) {
      try {
        _client = getClient();
        _db = drizzle(_client, { schema });
      } catch (error) {
        console.error('Failed to initialize database connection:', error);
        throw error;
      }
    }
    return (_db as any)[prop];
  }
});

// Export the schema
export { schema };
