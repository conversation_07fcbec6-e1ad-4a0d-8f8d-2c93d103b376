import { eq, or } from 'drizzle-orm'
import { db, schema } from '../drizzle/db'
import { Admin, CreateAdminDTO, UpdateAdminDTO } from '../../domain/entities/admin'
import { CacheService } from '../../domain/usecases/auth'
import * as bcrypt from 'bcrypt'

/**
 * Admin repository implementation
 */
export class AdminRepository {
  constructor(private cache: CacheService) {}

  /**
   * Find all admins
   */
  async findAll(): Promise<Admin[]> {
    const admins = await db
      .select()
      .from(schema.users)
      .where(or(eq(schema.users.role, 'admin'), eq(schema.users.role, 'super_admin')))

    return admins.map(this.mapToAdmin)
  }

  /**
   * Find an admin by ID
   */
  async findById(id: number): Promise<Admin | null> {
    const [admin] = await db.select().from(schema.users).where(eq(schema.users.id, id)).limit(1)

    if (!admin) {
      return null
    }

    return this.mapToAdmin(admin)
  }

  /**
   * Find an admin by username
   */
  async findByUsername(username: string): Promise<Admin | null> {
    const [admin] = await db
      .select()
      .from(schema.users)
      .where(eq(schema.users.username, username))
      .limit(1)

    if (!admin) {
      return null
    }

    return this.mapToAdmin(admin)
  }

  /**
   * Create a new admin
   */
  async create(adminData: Omit<Admin, 'id' | 'createdAt' | 'updatedAt'>): Promise<Admin> {
    const [admin] = await db
      .insert(schema.users)
      .values({
        role: adminData.role,
        username: adminData.username,
        name: adminData.name,
        passwordHash: adminData.passwordHash,
        createdAt: new Date(),
        classId: null,
      })
      .returning()

    return this.mapToAdmin(admin)
  }

  /**
   * Update an admin
   */
  async update(
    id: number,
    data: Partial<
      Omit<Admin, 'id' | 'createdAt' | 'updatedAt' | 'role'> & { passwordHash?: string }
    >
  ): Promise<Admin> {
    const updateData: any = {
      updatedAt: new Date(),
    }

    if (data.name) {
      updateData.name = data.name
    }

    if (data.passwordHash) {
      updateData.passwordHash = data.passwordHash
    }

    const [admin] = await db
      .update(schema.users)
      .set(updateData)
      .where(eq(schema.users.id, id))
      .returning()

    return this.mapToAdmin(admin)
  }

  /**
   * Delete an admin
   */
  async delete(id: number): Promise<void> {
    await db.delete(schema.users).where(eq(schema.users.id, id))
  }

  /**
   * Verify an admin's password
   */
  async verifyPassword(admin: Admin, password: string): Promise<boolean> {
    return bcrypt.compare(password, admin.passwordHash)
  }

  /**
   * Map a database user to an Admin entity
   */
  private mapToAdmin(user: any): Admin {
    return {
      id: user.id,
      username: user.username,
      name: user.name,
      passwordHash: user.passwordHash,
      role: user.role,
      createdAt: user.createdAt,
      updatedAt: user.updatedAt,
    }
  }
}
