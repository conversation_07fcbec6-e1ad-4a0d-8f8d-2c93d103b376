import { eq } from 'drizzle-orm'
import { db, schema } from '../drizzle/db'
import { Class } from '../../domain/entities/class'

/**
 * Class repository implementation
 */
export class ClassRepository {
  /**
   * Find all classes
   */
  async findAll(): Promise<Class[]> {
    const classes = await db.select().from(schema.classes)

    return classes.map(this.mapToClass)
  }

  /**
   * Find a class by ID
   */
  async findById(id: number): Promise<Class | null> {
    const [classEntity] = await db
      .select()
      .from(schema.classes)
      .where(eq(schema.classes.id, id))
      .limit(1)

    if (!classEntity) {
      return null
    }

    return this.mapToClass(classEntity)
  }

  /**
   * Find a class by name
   */
  async findByName(name: string): Promise<Class | null> {
    const [classEntity] = await db
      .select()
      .from(schema.classes)
      .where(eq(schema.classes.name, name))
      .limit(1)

    if (!classEntity) {
      return null
    }

    return this.mapToClass(classEntity)
  }

  /**
   * Create a new class
   */
  async create(name: string): Promise<Class> {
    const [classEntity] = await db
      .insert(schema.classes)
      .values({
        name,
        createdAt: new Date(),
      })
      .returning()

    return this.mapToClass(classEntity)
  }

  /**
   * Update a class
   */
  async update(id: number, name: string): Promise<Class> {
    const [updatedClass] = await db
      .update(schema.classes)
      .set({
        name,
        // No need to update createdAt
      })
      .where(eq(schema.classes.id, id))
      .returning()

    return this.mapToClass(updatedClass)
  }

  /**
   * Delete a class
   */
  async delete(id: number): Promise<void> {
    await db.delete(schema.classes).where(eq(schema.classes.id, id))
  }

  /**
   * Map a database class to a Class entity
   */
  private mapToClass(classEntity: any): Class {
    return {
      id: classEntity.id,
      name: classEntity.name,
      createdAt: classEntity.createdAt,
    }
  }
}
