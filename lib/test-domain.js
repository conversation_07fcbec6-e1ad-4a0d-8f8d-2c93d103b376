// Simple test for domain layer
// Run with: node lib/test-domain.js

;
;

// Define test entities
const student = {
  id: 1,
  uniqueCode: 'f47ac10b-58cc-4372-a567-0e02b2c3d479',
  googleEmail: '<EMAIL>',
  name: 'Test Student',
  createdAt: new Date(),
};

const admin = {
  id: 1,
  username: 'admin',
  name: 'Test Admin',
  passwordHash: 'hashed_password',
  createdAt: new Date(),
};

const absence = {
  id: 1,
  uniqueCode: student.uniqueCode,
  type: 'Zuhr',
  recordedAt: new Date(),
  createdAt: new Date(),
};

// Simple mock cache
class MockCache {
  constructor() {
    this.cache = new Map();
    ;
  }
  
  async get(key) {
    return this.cache.get(key) || null;
  }
  
  async set(key, value, ttlSeconds) {
    this.cache.set(key, value);
  }
  
  async del(key) {
    this.cache.delete(key);
  }
}

// Simple mock student repository
class MockStudentRepository {
  constructor() {
    this.students = new Map();
    this.nextId = 1;
    ;
  }
  
  async findByGoogleEmail(googleEmail) {
    for (const student of this.students.values()) {
      if (student.googleEmail === googleEmail) {
        return student;
      }
    }
    return null;
  }
  
  async create(student) {
    const id = this.nextId++;
    const newStudent = {
      id,
      ...student,
      createdAt: new Date(),
    };
    this.students.set(id, newStudent);
    return newStudent;
  }
}

// Simple mock auth use case
class MockAuthUseCase {
  constructor(studentRepo, cache) {
    this.studentRepo = studentRepo;
    this.cache = cache;
    ;
  }
  
  async registerWithGoogle(googleEmail, name) {
    // Check if student exists
    let student = await this.studentRepo.findByGoogleEmail(googleEmail);
    
    if (!student) {
      // Create new student
      const uniqueCode = 'unique-' + Date.now();
      student = await this.studentRepo.create({
        googleEmail,
        name,
        uniqueCode,
      });
    }
    
    // Generate mock tokens
    const token = `mock_token_${student.id}`;
    const refreshToken = `mock_refresh_token_${student.id}`;
    
    // Store refresh token in cache
    await this.cache.set(`auth:refresh:${student.id}`, refreshToken, 60);
    
    return { token, refreshToken, student };
  }
}

// Run test
async function runTest() {
  try {
    ;
    ;
    
    // Initialize dependencies
    const cache = new MockCache();
    const studentRepo = new MockStudentRepository();
    const authUseCase = new MockAuthUseCase(studentRepo, cache);
    
    // Test registerWithGoogle
    ;
    const result = await authUseCase.registerWithGoogle('<EMAIL>', 'Test User');
    
    ;
    ;
    ;
    ;
    ;
    ;
    
    // Test cache
    const cachedToken = await cache.get(`auth:refresh:${result.student.id}`);
    ;
    
    ;
    ;
  } catch (error) {
    console.error('❌ Test failed:', error);
  }
}

// Run the test
runTest();
