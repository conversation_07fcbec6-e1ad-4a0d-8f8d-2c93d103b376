# Product Requirements Document (PRD)

## Prayer Attendance Application for Banjarmasin High School Students

---

## 1. Background and Objectives

**Background:**
A high school in Banjarmasin aims to automate attendance tracking for comprehensive school attendance including prayers (<PERSON>uh<PERSON>, Asr), school entry/exit, and various attendance statuses using a web-based QR Code application. The current manual process is inefficient and lacks real-time reporting. The solution leverages Next.js with API Routes, Drizzle ORM for PostgreSQL, Redis for caching and session management, and n8n Webhook for WhatsApp OTP, hosted on a VPS with EasyPanel. The application adheres to Clean Architecture and programming best practices for clean, scalable, maintainable, and resource-efficient code.

**Objectives:**

- Develop a production-ready web application with a Landing Page, Student App, and Admin App using Next.js (App Router and API Routes) with Clean Architecture.
- Design an informative Landing Page without application access for security, based on modern UI principles.
- Implement the UI using Tailwind CSS, shadcn/ui, and next-themes for light/dark mode, delivering an elegant, reusable, and responsive design.
- Build the backend with Next.js API Routes, Drizzle ORM for PostgreSQL, Redis for caching and session management, and n8n Webhook for WhatsApp OTP.
- For student login use username/password authentication.
- Implement password reset functionality at page reset password via simple step just need old password and new password.
- Implement forget password with sent request to whatsapp and after that input new password
- Generate unique codes for QR code scanning instead of NIS, with optional NIS input in the profile.
- Ensure security (JWT, rate limiting, input validation with Zod), cost-effective scalability, and easy management on EasyPanel.
- Apply Clean Architecture with Presentation, Domain, and Data layers for maintainability.
- Follow programming best practices (clean code, type safety, testing, modularity, logging) for high-quality code.
- Deliver an award-worthy design inspired by Notion, Figma, and Stripe, aligned with Nielsen Norman Group usability principles.
- Support WITA (UTC+8) time for Banjarmasin context.

---

## 2. Stakeholders

- **Students:** Use the Student App to log in with username and password, complete their profile (WhatsApp number, NIS, email), display QR codes with unique codes, and manage profile.
- **Super Admin:** Full system access, can reset passwords for all roles, manage all bulk operations, force logout users.
- **Admin:** QR Scanner for prayers (Zuhr, Asr, Pulang, Izin Tidak Shalat) with audio alerts, view reports with pagination.
- **Guru (Teachers):** QR Scanner for school entry with audio alerts, view reports with pagination.
- **Resepsionis (Receptionist):** QR Scanner for late entry, permits, temporary permits, return from permits, sick leave with audio alerts, view reports.
- **School:** Requires a cost-effective, efficient comprehensive attendance solution.
- **Developers:** Team building the application with Next.js 15, Drizzle ORM, and EasyPanel.
- **VPS Admin:** Manages EasyPanel, PostgreSQL, Redis, and n8n on the VPS.

---

## 3. Project Scope

### In Scope

- Web application: Landing Page, Student App, Admin App.
- Informative Landing Page without links to /student or /admin.
- Student App: Username and password login with session management, WhatsApp OTP verification for profile, Home (QR code), Profile (WhatsApp number with OTP verification, NIS editing, email editing, self password reset).
- Multi-Role Admin App: Role-based login (Super Admin, Admin), Home (QR Scanner with WebRTC camera and audio alerts), Reports with pagination (100 records/page), User Management with bulk operations, Class Management (CRUD), Session Management with force logout.
- Attendance Types: Zuhr (prayer), Asr (prayer), Pulang (departure), Ijin (permission). *(4 implemented types out of 10 originally planned: Masuk, Masuk Terlambat, Izin, Izin Sementara, Kembali dari Izin, Sakit, Shalat Zuhur, Shalat Ashar, Izin Tidak Shalat)*
- Backend: Next.js 15 API Routes with Clean Architecture for authentication, attendance, reports, user management, and session management.
- Database: PostgreSQL 16 on EasyPanel, accessed via Drizzle ORM with enhanced schema.
- Caching: Redis on EasyPanel for profiles, reports, JWT sessions, OTPs, and single-device token enforcement.
- Integration: n8n Webhook for WhatsApp OTP and forgot password.
- Security: JWT, rate limiting, input validation with Zod, HTTPS.
- Deployment: Docker on EasyPanel for the Next.js app.
- Monitoring: Logging with `console.log`, VPS metrics via EasyPanel, optional Sentry if budget allows.
- Frontend: Next.js (App Router), Tailwind CSS, shadcn/ui, next-themes, framer-motion.
- Best Practices: Clean code, type safety, unit testing (Jest), component testing (React Testing Library), linting (ESLint), formatting (Prettier).
- Responsive: Mobile (min-width 320px) to desktop (max-width 1280px).
- Accessibility: WCAG 2.1 Level AA.
- Time: WITA (UTC+8) for all timestamps.

### Out of Scope

- Native mobile application.
- Integration with other school systems (e.g., SIS).
- Features beyond the currently implemented attendance types.
- External cloud services (e.g., AWS, Twilio).

---

## 4. Key Features

### 4.1 Landing Page (Informative)

**Purpose:** Introduce the application without providing access to Student or Admin Apps for security.

**Features:**

- Hero Section: Headline "Modern Prayer Attendance for Future Education", subheadline on efficiency, religious image (mosque/students praying, rounded-lg, border indigo-600).
- About Section: Description of the application in a shadcn/ui Card.
- Features Section: Three shadcn/ui Cards for QR Code, Scanner, and Reports, each with Lucide icons (QrCodeIcon, CameraIcon, FileTextIcon).
- Footer: Text "Banjarmasin High School - 2025 | Contact: +6281234567890".
- Theme Toggle: shadcn/ui Button with SunIcon/MoonIcon for light/dark mode.

**Security:** No buttons/links to /student or /admin.

**Interactions:** Theme toggle, fade-in/scale-in animations with framer-motion on load.

### 4.2 Student App

#### Login

- **UI:** Title "Masuk ke Akun Siswa", username/password fields with "Forgot Password" link.
- **API:**
  - `POST /api/auth/student/login`: Handles username/password login, verifies credentials, returns JWT and refresh token, enforces single-device policy.
- **Interactions:** Login with username and password, click button login, redirects to Home. Client-side Toast "Login successful" on success. Single-device enforcement with automatic logout from other devices.
- **Security:** JWT with password hashing, single-device token stored in Redis.

#### Password Reset

- **UI:** Forgot Password page with WhatsApp OTP recovery, OTP input (6 digits), new password input. Self password reset in Profile with old password and new password fields.
- **API:**
  - `POST /api/auth/password-reset/request`: Sends password reset OTP via n8n Webhook.
  - `POST /api/auth/password-reset/verify`: Verifies OTP, updates password.
  - `POST /api/student/password-reset`: Self password reset with old password verification.
- **Interactions:** Toast notifications for successful OTP sending and password reset. Redirects to login page after successful reset.
- **Security:** OTP stored in Redis with 5-minute TTL, old password verification for self reset.

#### WhatsApp OTP Verification (Profile)

- **UI:** Prompt in Profile for WhatsApp number if missing, shadcn/ui Input with PhoneIcon, shadcn/ui Button "Kirim OTP" (purple). OTP input field (6 digits), shadcn/ui Button "Verifikasi OTP" (purple), "Kembali" (secondary).
- **API:**
  - `POST /api/student/whatsapp/send-otp`: Sends OTP via n8n Webhook, stores OTP in Redis (5-minute TTL).
- `POST /api/student/whatsapp/verify-otp`: Verifies OTP, updates WhatsApp number in database, invalidates profile cache.
- `POST /api/student/whatsapp/check`: Checks if WhatsApp number is already registered.
- **Interactions:** Toast "OTP sent to your WhatsApp" after sending, "WhatsApp number verified" or "Invalid OTP" after verification. Redirects to Home after successful verification.

#### Home

- **UI:** Title "QR Code Absensi", displays unique code (UUID), QR code (encodes unique code using qrcode.react), comprehensive status section for the 4 implemented attendance types (Zuhr, Asr, Pulang, Ijin) with checkmarks/crosses, date/time (WITA, format DD/MM/YYYY HH:mm).
- **API:** `GET /api/student/profile` (fetches profile including unique code, cached in Redis with 5-minute TTL).
- **Interactions:** QR code, status updates real-time via API polling, shadcn/ui Tabs for navigation (HomeIcon active, UserIcon for Profile).

#### Profile

- **UI:** Title "Profil Siswa", fields for Name (read-only), WhatsApp Number (editable, requires OTP verification), NIS (editable, shadcn/ui Input), Email (editable), Class (read-only, set by admin), Self Password Reset section (old password, new password). shadcn/ui Button "Simpan" (purple), "Reset Password" (orange), "Logout" (red).
- **API:**
  - `PATCH /api/student/profile`: Updates WhatsApp number (post-OTP verification), NIS, email, invalidates Redis cache.
  - `POST /api/student/password-reset`: Self password reset with old password verification.
  - `POST /api/auth/logout`: Removes refresh token from Redis, clears client-side session.
- **Interactions:** Client-side form validation with Zod, Toast for feedback ("Profile updated", "Password reset successful", "Logout successful"). Prompt to add WhatsApp number if missing (Dialog with input and OTP flow). Real-time WhatsApp number availability checking.

### 4.3 Multi-Role Admin App

#### Login

- **UI:** Title "Masuk ke Sistem Admin", fields for Username and Password, Role indicator, shadcn/ui Input with icons (UserIcon, LockIcon), shadcn/ui Button "Masuk" (purple).
- **API:** `POST /api/auth/admin/login` (returns JWT and refresh token with role information, caches in Redis, enforces single-device policy).
- **Interactions:** Client-side validation with Zod, Toast "Login successful", navigates to role-appropriate Home. Single-device enforcement.

#### Home (QR Scanner with Audio Alerts)

- **UI:** Title "Scanner QR Code", role-based shadcn/ui Select for attendance types, shadcn/ui Button "Mulai Pemindaian" (purple), WebRTC QR scanner (jsQR), shadcn/ui Card for scan result (name, class, unique code), shadcn/ui Dialog for duplicate attendance and validation alerts, shadcn/ui Tabs for navigation (CameraIcon active).
- **Role-based Attendance Types:**
  - **Super Admin/Admin:** Shalat Zuhur, Shalat Ashar, Pulang, Izin Tidak Shalat
  - **Guru:** Masuk
  - **Resepsionis:** Masuk Terlambat, Izin, Izin Sementara, Kembali dari Izin, Sakit
- **Audio Alerts:**
  - **Positive Alert:** Valid scan sound
  - **Negative Alert:** Duplicate attendance or invalid scan
  - **Validation Alert:** Missing Zuhr/Asr when scanning Pulang
- **API:**
  - `POST /api/absence/record`: Records attendance (unique code, type, WITA timestamp) with role validation.
  - `GET /api/absence/check`: Checks for duplicate attendance and validates prayer sequence.
  - `GET /api/student/{unique_code}`: Fetches student data by unique code (cached in Redis).
- **Interactions:** WebRTC camera activation, audio feedback on scan results, Dialog prompts for duplicate attendance and validation issues.

#### Reports

- **UI:** Title "Laporan Absensi", tabs for "Sekolah" and "Shalat" reports, preset filters (Today/Yesterday/Week/Month/Year), filters for Tanggal and Kelas (shadcn/ui Select), Name/Unique Code search (shadcn/ui Input), paginated shadcn/ui Table (100 records/page) with the 4 implemented attendance types (Zuhr, Asr, Pulang, Ijin), bulk action buttons (Delete Selected, Download CSV), shadcn/ui Button "Export CSV" (green).
- **Attendance Summary:** Cards showing comprehensive statistics for the 4 implemented attendance types (Zuhr, Asr, Pulang, Ijin) with percentages and progress bars.
- **API:** 
  - `GET /api/absence/reports`: Fetches paginated filtered reports (cached in Redis with 1-minute TTL).
  - `DELETE /api/absence/bulk`: Bulk delete selected records.
  - `GET /api/absence/export`: CSV export with data for the 4 implemented attendance types.
- **Pagination:** 100 records per page with navigation controls.
- **Interactions:** Real-time filtering, pagination navigation, bulk selection, CSV export with filename reports_DD-MM-YYYY.csv for the 4 implemented attendance types.

#### User Management

- **UI:** Title "Manajemen User", role-based tabs (Siswa, Admin, Guru, Resepsionis), shadcn/ui Table with pagination, bulk operation buttons (Upload CSV, Delete Batch, Change Class), shadcn/ui Button "Tambah User" (purple), edit/delete icons. Dialog for adding/editing users with role-specific fields.
- **Bulk Operations:**
  - **Students:** CSV upload, batch delete, mass class change
  - **Staff:** CSV upload/delete for Admin, Guru, Resepsionis
- **APIs:**
  - `GET /api/users`: Lists paginated users by role (cached in Redis).
  - `POST /api/users`: Adds user (generates unique code for students).
  - `POST /api/users/bulk`: Bulk operations (upload, delete, class change).
  - `PATCH /api/users/{id}`: Edits user.
  - `DELETE /api/users/{id}`: Deletes user.
- **Interactions:** Client-side form validation, bulk selection, CSV upload/download, Toast for feedback.

#### Class Management

- **UI:** Title "Manajemen Kelas", CRUD interface for classes table, shadcn/ui Table with class names, add/edit/delete operations.
- **API:** 
  - `GET /api/classes`: Lists all classes.
  - `POST /api/classes`: Creates new class.
  - `PATCH /api/classes/{id}`: Updates class.
  - `DELETE /api/classes/{id}`: Deletes class.
- **Interactions:** Form validation, Toast feedback.

#### Session Management

- **UI:** Title "Manajemen Sesi", list of active devices/sessions per user, "Force Logout" buttons, single-device enforcement status.
- **API:**
  - `GET /api/sessions`: Lists active sessions.
  - `POST /api/sessions/force-logout`: Forces logout from specific device.
- **Interactions:** Real-time session monitoring, force logout actions.

#### Profile

- **UI:** Title "Profil", fields for Username and Name, role indicator, shadcn/ui Button "Simpan" (purple), "Logout" (red).
- **API:** `PATCH /api/admin/profile` (updates database).
- **Interactions:** Client-side form validation, Toast for feedback.

---

## 5. Technical Specifications

### 5.1 Technology Stack

- **Frontend and Backend:**

  - Framework: Next.js (App Router, API Routes) with React
  - Styling: Tailwind CSS 3.x with shadcn/ui components (Button, Input, Select, Table, Dialog, Toast, Tabs)
  - Theme Management: next-themes for light/dark/system mode, detects prefers-color-scheme, stores in localStorage
  - Animations: Framer-motion 10.x for transitions and microinteractions
  - QR Code: qrcode.react for Student App Home (encodes unique code)
  - Scanner: jsQR for Admin App Scanner
  - Icons: Lucide Icons from shadcn/ui (PhoneIcon, QrCodeIcon, etc.)
  - Authentication:
    - Students: Username/password with JWT (jsonwebtoken), refresh tokens in Redis
    - Admins: Username/password with JWT (jsonwebtoken), refresh tokens in Redis
  - Database: PostgreSQL on EasyPanel, accessed via Drizzle ORM
  - Caching: Redis on EasyPanel with @upstash/redis
  - Validation: Zod for input validation in API Routes
  - WhatsApp Integration: n8n Webhook for WhatsApp OTP and forgot password
  - Security:
    - Rate limiting with custom middleware
    - HTTPS via EasyPanel (Let's Encrypt)
  - Logging: Standard `console.log`, `console.info`, `console.error` for application events (debug, info, error levels)
  - Error Tracking: Optional Sentry if budget allows
  - Testing: Jest for unit tests, React Testing Library for component tests
  - Linting/Formatting: ESLint with TypeScript rules
  - Prettier for consistent formatting
  - Husky for pre-commit linting/testing
  - Password Hashing: bcrypt for admin passwords

- **Infrastructure:**
  - VPS: EasyPanel for hosting Next.js app, PostgreSQL, Redis, and n8n
  - Database: PostgreSQL on EasyPanel (port 5432)
  - Caching: Redis on EasyPanel (port 6379)
  - Workflow: n8n on EasyPanel for WhatsApp Webhook
  - Container: Docker for Next.js app
  - HTTPS: Managed by EasyPanel with Let's Encrypt
  - Backup: Daily PostgreSQL backups via EasyPanel

### 5.2 Clean Architecture

- **Layers:**
  - Presentation:
    - Frontend: UI components (pages, components) for rendering and interaction
      - Example: `app/student/home/<USER>
    - Backend: API Routes for HTTP requests/responses
      - Example: `app/api/auth/student/login/route.ts`
  - Domain: Entities and business logic (use cases), framework-agnostic
    - Example: `lib/domain/entities/student.ts`, `lib/domain/usecases/auth.ts`
  - Data: Repositories for database (Drizzle ORM) and caching (Redis) access
    - Example: `lib/data/repositories/student.ts`, `lib/data/cache/redis.ts`
- **Dependency Rule:**

  - Presentation depends on Domain
  - Domain is independent (pure TypeScript)
  - Data depends on Domain for entity definitions

- **Folder Structure:**

```text
app/
  page.tsx # Landing Page
  student/ # Student App routes
    page.tsx # Login
    whatsapp-otp/ # WhatsApp OTP Verification
    home/ # Home (QR Code)
    profile/ # Profile
  admin/ # Admin App routes
    page.tsx # Login
    home/ # Scanner
    reports/ # Reports
    user-management/ # User Management
    profile/ # Profile
api/
  auth/
    student/
      logout/route.ts # POST /api/auth/logout
    admin/
      login/route.ts # POST /api/auth/admin/login
    whatsapp/
      send-otp/route.ts # POST /api/student/whatsapp/send-otp
      verify-otp/route.ts # POST /api/student/whatsapp/verify-otp
    student/
      profile/route.ts # GET, PATCH /api/student/profile
      [unique_code]/route.ts # GET /api/student/{unique_code}
    absence/
      record/route.ts # POST /api/absence/record
      check/route.ts # GET /api/absence/check
      reports/route.ts # GET /api/absence/reports
      refresh-summary/ # POST /api/refresh-summary
    users/
      route.ts # GET, POST /api/users
      [id]/route.ts # PATCH, DELETE /api/users/{id}
    admin/
      profile/route.ts # PATCH /api/admin/profile
components/
  ui/ # shadcn/ui components
  common/ # ThemeToggle, BottomNav
lib/
  domain/
    entities/ # Entities
      student.ts # Student interface
      admin.ts # Admin interface
      absence.ts # Absence interface
    usecases/ # Business logic
      auth.ts # login, OTP
      absence.ts # Record absence, check duplicate
      reports.ts # Generate reports
    errors/ # Custom errors
      index.ts # NotFoundError, ValidationError
  data/
    repositories/ # Database access
      student.ts # Student CRUD
      admin.ts # Admin CRUD
      absence.ts # Absence CRUD
    cache/ # Cache access
      redis.ts # Redis utilities
drizzle/
  schema.ts # Drizzle schema
  db.ts # Database connection
utils/
styles/
  globals.css # Tailwind styles
types/
  index.ts # Shared TypeScript types
tests/
  domain/ # Unit tests for Domain
  components/ # Component tests
  data/ # Repository tests
```

### 5.3 Programming Best Practices

- **Clean Code:**
  - DRY: Reusable components (e.g., shadcn/ui Button)
  - KISS: Simple logic in API Routes, business logic in Domain
  - Single Responsibility: One function/component per task
  - Naming: Descriptive (e.g., generateUniqueCode, verifyWhatsAppOtp)
  - Comments: Explain complex logic (e.g., OTP verification)
- **Type Safety:**
  - TypeScript for all code, integrated with Drizzle ORM schema
  - Zod for API input validation (e.g., WhatsApp number format)
  - JSDoc for complex types
- **Error Handling:**
  - Try-catch in API Routes and repositories
  - Standard error response: `{ error: string, status: number }`
  - Custom errors: NotFoundError, ValidationError in `lib/domain/errors/`
- **Testing:**
  - Unit tests for Domain (e.g., generateUniqueCodeUseCase)
  - Component tests for UI
  - Integration tests for API Routes with mocked database/Redis
  - Coverage: >80% for Domain and Data layers
- **Linting/Formatting:**
  - ESLint with TypeScript rules
  - Prettier for consistent formatting
  - Husky for pre-commit linting/testing
- **Logging:**
  - Standard `console.log`, `console.info`, `console.error` for application events (debug, info, error levels)
  - Example: `console.info("Student registered", { email: "<EMAIL>", uniqueCode: "uuid" })`
- **Performance:**
  - Redis caching for profiles/reports (>80% hit rate)
  - Drizzle ORM with indexed queries
  - Lazy-load components with next/dynamic
  - Optimize images with next/image

### 5.4 Database Schema (PostgreSQL)

```sql
CREATE TYPE user_role AS ENUM ('student', 'super_admin', 'admin', 'guru', 'resepsionis');
-- Note: All 10 attendance types defined for future expansion, but currently only 4 are implemented
CREATE TYPE attendance_type AS ENUM ('Masuk', 'Masuk Terlambat', 'Izin', 'Izin Sementara', 'Kembali dari Izin', 'Sakit', 'Shalat Zuhur', 'Shalat Ashar', 'Izin Tidak Shalat', 'Pulang');
-- Currently implemented: 'Shalat Zuhur', 'Shalat Ashar', 'Pulang', 'Ijin' (mapped to 'Izin')

CREATE TABLE classes (
  id BIGINT GENERATED ALWAYS AS IDENTITY PRIMARY KEY,
  name VARCHAR(10) NOT NULL UNIQUE,
  created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
  CONSTRAINT chk_class_name CHECK (name ~ '^[XVI]{1,2}\s(IPA|IPS)\s[1-3]$')
);

CREATE TABLE users (
  id BIGINT GENERATED ALWAYS AS IDENTITY PRIMARY KEY,
  role user_role NOT NULL,
  unique_code VARCHAR(36) UNIQUE, -- UUID for QR code (students only)
  email VARCHAR(255), -- Email for students (editable)
  nim VARCHAR(10), -- NIM for students (editable)
  username VARCHAR(50) UNIQUE, -- For admins and students with password authentication
  name VARCHAR(100) NOT NULL CHECK (name <> ''),
  whatsapp VARCHAR(15), -- Verified via OTP for students (unique constraint for students)
  class_id BIGINT REFERENCES classes(id),
  password_hash VARCHAR(255), -- For admins and students with password authentication
  created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMPTZ,
  CONSTRAINT chk_nim_format CHECK (nim IS NULL OR nim ~ '^[A-Za-z0-9]{1,10}$'),
  CONSTRAINT chk_role_data CHECK (
    (role = 'student' AND unique_code IS NOT NULL AND username IS NOT NULL AND password_hash IS NOT NULL) OR
    (role IN ('super_admin', 'admin', 'guru', 'resepsionis') AND unique_code IS NULL AND username IS NOT NULL AND password_hash IS NOT NULL)
  )
);

CREATE TABLE absences (
  id BIGINT GENERATED ALWAYS AS IDENTITY PRIMARY KEY,
  unique_code VARCHAR(36) NOT NULL REFERENCES users(unique_code),
  type attendance_type NOT NULL,
  recorded_at TIMESTAMPTZ NOT NULL,
  created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
  CONSTRAINT fk_unique_code FOREIGN KEY (unique_code) REFERENCES users(unique_code) ON DELETE RESTRICT
);

-- Sessions table for single-device enforcement
CREATE TABLE user_sessions (
  id BIGINT GENERATED ALWAYS AS IDENTITY PRIMARY KEY,
  user_id BIGINT NOT NULL REFERENCES users(id),
  session_token VARCHAR(255) NOT NULL UNIQUE,
  device_info TEXT,
  created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
  last_active TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP
);

CREATE MATERIALIZED VIEW attendance_summary AS
WITH pivoted AS (
  SELECT
    DATE(recorded_at) AS summary_date,
    unique_code,
    MAX(CASE WHEN type = 'Masuk' THEN TRUE ELSE FALSE END) AS masuk,
    MAX(CASE WHEN type = 'Masuk Terlambat' THEN TRUE ELSE FALSE END) AS masuk_terlambat,
    MAX(CASE WHEN type = 'Izin' THEN TRUE ELSE FALSE END) AS izin,
    MAX(CASE WHEN type = 'Izin Sementara' THEN TRUE ELSE FALSE END) AS izin_sementara,
    MAX(CASE WHEN type = 'Kembali dari Izin' THEN TRUE ELSE FALSE END) AS kembali_dari_izin,
    MAX(CASE WHEN type = 'Sakit' THEN TRUE ELSE FALSE END) AS sakit,
    MAX(CASE WHEN type = 'Shalat Zuhur' THEN TRUE ELSE FALSE END) AS shalat_zuhur,
    MAX(CASE WHEN type = 'Shalat Ashar' THEN TRUE ELSE FALSE END) AS shalat_ashar,
    MAX(CASE WHEN type = 'Izin Tidak Shalat' THEN TRUE ELSE FALSE END) AS izin_tidak_shalat,
    MAX(CASE WHEN type = 'Pulang' THEN TRUE ELSE FALSE END) AS pulang,
    MAX(recorded_at) AS last_updated
  FROM absences
  GROUP BY DATE(recorded_at), unique_code
)
SELECT
  p.summary_date,
  u.unique_code,
  u.name,
  c.name AS class_name,
  p.masuk,
  p.masuk_terlambat,
  p.izin,
  p.izin_sementara,
  p.kembali_dari_izin,
  p.sakit,
  p.shalat_zuhur,
  p.shalat_ashar,
  p.izin_tidak_shalat,
  p.pulang,
  p.last_updated AS updated_at
FROM pivoted p
JOIN users u ON p.unique_code = u.unique_code
JOIN classes c ON u.class_id = c.id
WITH NO DATA;

CREATE INDEX idx_users_role ON users(role);
CREATE INDEX idx_users_class_id ON users(class_id) WHERE class_id IS NOT NULL;
CREATE INDEX idx_users_unique_code ON users(unique_code) WHERE unique_code IS NOT NULL;
CREATE INDEX idx_absences_unique_code_recorded_at ON absences(unique_code, recorded_at);
CREATE INDEX idx_attendance_summary_date ON attendance_summary(summary_date);
CREATE INDEX idx_user_sessions_user_id ON user_sessions(user_id);
CREATE INDEX idx_user_sessions_token ON user_sessions(session_token);
```

### 5.5 Database Integration

- Drizzle ORM: Maps PostgreSQL schema to TypeScript for type-safe queries.
- Unique Code: Generated with uuid library (v4) upon Admin make user, stored in users.unique_code.
- Materialized View: Refreshed on-demand via `POST /api/refresh-summary` if data is older than 5 minutes.
- Performance:
  - Indexes on unique_code, recorded_at for fast queries.
  - Redis caching for reports and profiles.

### 5.6 Username & Password Authentication

- **Authentication for All Users:**
  - Username/Password: JWT with bcrypt for password hashing.
  - Single-Device Enforcement: Redis-based session management.
  - Forget Password: WhatsApp OTP via n8n Webhook.
  - Reset password: Self-reset (students) or Super Admin reset (all roles)
- **Configuration:**
  - Environment variables: JWT_SECRET, N8N_WHATSAPP_WEBHOOK_URL.
  - Custom JWT implementation with role-based access control.
- **Flow:**
  - Username/Password: User enters credentials → Verifies against database → Issues JWT token.
  - Forget Password: User requests reset → Sends OTP/link → Verifies token → Updates password.
  - Reset Password: User input old password and new password on page reset password
  - Stores refresh token in Redis (TTL: 7 days).

### 5.7 Redis Usage

- Profile Caching: `GET /api/student/profile` (TTL: 5 minutes, key: student:profile:{unique_code})
- Reports Caching: `GET /api/absence/reports` (TTL: 1 minute, key: absence:reports:{date}:{class})
- JWT Sessions: Refresh tokens (TTL: 7 days, key: auth:refresh:{user_id})
- Single-Device Tokens: Active session tokens (TTL: 24 hours, key: session:{user_id})
- OTP: Temporary OTPs (TTL: 5 minutes, key: otp:{whatsapp})
- Force Logout: Invalidated sessions (key: logout:{session_token})

### 5.8 Security

- **Authentication:**
  - All Roles: Username/password with JWT (jsonwebtoken), refresh tokens in Redis
  - Single-Device Enforcement: Session tokens in Redis with automatic logout
  - Role-Based Access Control: Super Admin, Admin, Guru, Resepsionis, Student
- **Rate Limiting:** 100 requests/15 minutes/IP
- **Validation:** Zod for WhatsApp number, OTP, and optional NIS
- **HTTPS:** Managed by EasyPanel
- **Data Sanitization:** Drizzle ORM prevents SQL injection

### 5.9 Color Palette, Typography, Animations, Accessibility

- **Color Palette:**
  - Primary: indigo-600 (hover: indigo-700)
  - Neutral: Light (slate-50, white), Dark (slate-900, slate-800)
  - QR Code: White background, black foreground, border indigo-600
- **Typography:** Inter font, sizes from 12px (caption) to 48px (H1)
- **Animations:** Fade-in (pages), scale-in (Dialog), slide-in (Toast)
- **Accessibility:** WCAG 2.1 Level AA, ARIA labels, keyboard navigation

---

## 6. Functional Specifications

### Student App

- **Login:** Single-device login with automatic logout from other devices
- **WhatsApp OTP:** Input WhatsApp number, send OTP via n8n Webhook, verify OTP, update profile
- **Home:** Displays unique code, QR code, comprehensive attendance status (4 implemented types out of 10 planned)
- **Profile:** Edit WhatsApp (with OTP), edit NIS, edit email, self password reset, class display

### Multi-Role Admin App

- **Role-Based Scanner:** Different attendance types per role with audio alerts
- **Comprehensive Reports:** Paginated reports (100/page) with bulk operations
- **User Management:** Bulk operations, CSV upload/download, mass class changes
- **Class Management:** CRUD operations for classes table
- **Session Management:** Force logout, single-device enforcement monitoring

---

## 7. Non-Functional Requirements

- **Performance:** API < 200ms, page load < 2 seconds, scalable for 1,000+ users with pagination
- **Security:** JWT, rate limiting, HTTPS
- **Reliability:** 99.9% uptime, error rate < 0.1%
- **Maintainability:** Clean Architecture, >80% test coverage, comprehensive docs

---

## 8. Assumptions and Constraints

- **Assumptions:**
  - Username and password credentials are provided by the admin
  - EasyPanel supports n8n, PostgreSQL, and Redis
  - Audio alerts supported in modern browsers
- **Constraints:**
  - Limited VPS resources (2GB RAM, 2 CPUs)
  - n8n stability and WhatsApp API limitations
  - Single-device enforcement requires Redis reliability

---

## 9. Success Criteria

- Stable deployment on EasyPanel with 99.9% uptime
- Multi-role authentication with single-device enforcement
- Comprehensive attendance system with audio alerts (4 types implemented out of 10 planned)
- WhatsApp OTP verification via n8n Webhook and self password reset
- Paginated reports with bulk operations and CSV export
- Role-based QR scanning with validation logic
- Elegant, responsive UI with light/dark mode
- Clean Architecture adherence with >80% test coverage
- Budget delivery within IDR 6,000,000 over 8 weeks

---

## 10. Planned Enhancements and System Expansion

### 10.1 Extended Attendance Type Coverage

**Comprehensive Attendance System Completion:**
- **School Entry Tracking:** Masuk (regular entry) and Masuk Terlambat (late entry) for complete daily attendance monitoring
- **Permission Management:** Izin (general permission), Izin Sementara (temporary permission), and Kembali dari Izin (return from permission) for flexible absence handling
- **Health Monitoring:** Sakit (sick leave) tracking with proper documentation and reporting
- **Prayer Attendance Enhancement:** Complete Izin Tidak Shalat (prayer exemption) implementation with validation logic

**Implementation Benefits:**
- Complete coverage of all 10 originally planned attendance types
- Enhanced reporting capabilities with comprehensive attendance analytics
- Improved school administration efficiency through automated tracking
- Better compliance with educational attendance requirements

### 10.2 Advanced Reporting and Analytics

**Enhanced Data Visualization:**
- **Dashboard Analytics:** Real-time attendance statistics with interactive charts and graphs
- **Trend Analysis:** Monthly and yearly attendance patterns with predictive insights
- **Class Performance Metrics:** Comparative analysis across different classes and time periods
- **Individual Student Tracking:** Detailed attendance history with behavioral pattern recognition

**Export and Integration Capabilities:**
- **Multiple Export Formats:** PDF reports, Excel spreadsheets, and JSON data exports
- **Automated Report Generation:** Scheduled daily, weekly, and monthly reports via email
- **Parent Notification System:** Automated WhatsApp notifications for attendance updates
- **School Information System Integration:** API endpoints for third-party system connectivity

### 10.3 Enhanced User Experience and Interface

**Mobile-First Optimization:**
- **Progressive Web App (PWA):** Offline capability and native app-like experience
- **Touch-Optimized Interface:** Improved mobile scanner interface with gesture controls
- **Voice Commands:** Audio-based navigation for accessibility enhancement
- **Biometric Integration:** Fingerprint and face recognition for additional security

**Advanced Scanner Features:**
- **Batch Scanning:** Multiple QR code processing for group attendance
- **Smart Camera Controls:** Auto-focus, flash control, and multiple camera support
- **Offline Mode:** Local storage capability for areas with poor connectivity
- **Scanner History:** Recent scans cache with retry functionality

### 10.4 Security and Performance Enhancements

**Advanced Security Measures:**
- **Two-Factor Authentication:** SMS and email-based 2FA for admin accounts
- **Audit Logging:** Comprehensive activity tracking with tamper-proof logs
- **Role-Based Permissions:** Granular permission system with custom role creation
- **Data Encryption:** End-to-end encryption for sensitive student information

**Performance Optimization:**
- **Caching Strategy Enhancement:** Multi-level caching with intelligent cache invalidation
- **Database Optimization:** Query optimization and indexing improvements
- **CDN Integration:** Static asset delivery optimization
- **Load Balancing:** Horizontal scaling capability for high-traffic periods

### 10.5 Administrative Tools and Automation

**Bulk Operations Enhancement:**
- **Smart Import/Export:** Intelligent data mapping with validation and error handling
- **Automated User Management:** Bulk user creation from school enrollment systems
- **Class Schedule Integration:** Automatic attendance type scheduling based on timetables
- **Backup and Recovery:** Automated daily backups with one-click restore functionality

**Workflow Automation:**
- **Attendance Rules Engine:** Configurable business rules for attendance validation
- **Notification Workflows:** Customizable alert systems for various attendance scenarios
- **Report Scheduling:** Automated report generation and distribution
- **System Health Monitoring:** Proactive system monitoring with alert notifications

### 10.6 Integration and Extensibility

**Third-Party Integrations:**
- **School Management Systems:** Seamless integration with existing school software
- **Payment Gateways:** Integration for fee management based on attendance
- **Communication Platforms:** Multi-channel notification system (WhatsApp, Email, SMS)
- **Cloud Storage:** Integration with Google Drive, OneDrive for backup and sharing

**API Development:**
- **RESTful API Expansion:** Comprehensive API for external system integration
- **Webhook Support:** Real-time data synchronization with external systems
- **GraphQL Implementation:** Flexible data querying for complex reporting needs
- **SDK Development:** Software development kits for third-party developers

### 10.7 Compliance and Documentation

**Educational Standards Compliance:**
- **Government Reporting:** Automated compliance reports for educational authorities
- **Data Privacy Compliance:** GDPR and local privacy law adherence
- **Accessibility Standards:** WCAG 2.1 AAA compliance for inclusive design
- **Security Certifications:** ISO 27001 and SOC 2 compliance preparation

**Documentation and Training:**
- **User Manual Development:** Comprehensive guides for all user roles
- **Video Training Materials:** Interactive tutorials and training videos
- **API Documentation:** Complete developer documentation with examples
- **System Administration Guide:** Detailed deployment and maintenance procedures

### 10.8 Scalability and Infrastructure

**Infrastructure Enhancements:**
- **Microservices Architecture:** Service-oriented architecture for better scalability
- **Container Orchestration:** Kubernetes deployment for high availability
- **Database Clustering:** PostgreSQL clustering for improved performance
- **Redis Clustering:** Distributed caching for enhanced performance

**Monitoring and Observability:**
- **Application Performance Monitoring:** Real-time performance tracking and alerting
- **Log Aggregation:** Centralized logging with advanced search capabilities
- **Metrics Dashboard:** Comprehensive system metrics visualization
- **Error Tracking:** Advanced error monitoring and debugging tools

### 10.9 Quality Assurance and Testing

**Testing Framework Enhancement:**
- **End-to-End Testing:** Comprehensive user journey testing automation
- **Performance Testing:** Load testing and stress testing implementation
- **Security Testing:** Automated vulnerability scanning and penetration testing
- **Cross-Browser Testing:** Compatibility testing across all major browsers

**Quality Metrics:**
- **Code Coverage:** Maintain >90% test coverage across all modules
- **Performance Benchmarks:** Sub-100ms API response times for critical endpoints
- **Uptime Targets:** 99.99% availability with disaster recovery procedures
- **User Experience Metrics:** Continuous UX monitoring and improvement

### 10.10 Innovation and Future Technologies

**Emerging Technology Integration:**
- **Artificial Intelligence:** Smart attendance pattern analysis and anomaly detection
- **Machine Learning:** Predictive analytics for attendance forecasting
- **Blockchain:** Immutable attendance records for enhanced security
- **IoT Integration:** Smart classroom sensors for automated attendance tracking

**Research and Development:**
- **Facial Recognition:** Camera-based attendance without QR codes
- **Geofencing:** Location-based attendance validation
- **Wearable Integration:** Smartwatch and fitness tracker connectivity
- **Voice Recognition:** Audio-based student identification and attendance

These planned enhancements represent a comprehensive roadmap for system evolution, ensuring the ShalatYuk application remains at the forefront of educational technology while maintaining its core focus on reliable, secure, and user-friendly attendance management.
