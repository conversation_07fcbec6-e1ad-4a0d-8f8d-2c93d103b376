# ShalatYuk - Cursor MCP Integration

This directory contains Cursor configuration files, including Cursor rules and Model Context Protocol (MCP) setup.

## Cursor Rules

The `rules/` directory contains `.mdc` files that define coding standards and best practices for different aspects of the project:

- `ui-development.mdc`: Guidelines for UI component development
- `api-development.mdc`: Guidelines for API route development
- `domain-data-logic.mdc`: Guidelines for domain and data layer structure
- `project-standards.mdc`: Project-wide coding standards
- `e2e-testing-playwright.mdc`: Guidelines for e2e testing with Playwright

## Model Context Protocol (MCP)

The `mcp.json` file configures MCP servers that extend Cursor's AI capabilities with project-specific tools.

### Available Tools

The MCP server provides the following tools:

#### Prayer-Related Tools

- `get_prayer_times`: Get prayer times for a specific location
- `find_nearby_mosques`: Find mosques near a specific location

#### User Management Tools

- `get_user_statistics`: Get statistics for a user
- `analyze_code_structure`: Analyze the code structure of a specific component
- `generate_code_template`: Generate code templates for entities, repositories, or API routes

### Using MCP Tools in Cursor

When using Cursor, you can access these tools by asking the AI to use them. For example:

- "Analyze the user management feature"
- "Generate an entity template for Mosque"
- "What are the prayer times for Jakarta?"

The AI will automatically use the appropriate MCP tool to respond.
