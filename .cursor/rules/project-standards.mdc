---
description: 
globs: 
alwaysApply: true
---
-   **General Coding Practices (PRD 5.3)**:
    -   **Clean Code**:
        -   DRY (Don't Repeat Yourself): Utilize reusable components and functions.
        -   KISS (Keep It Simple, Stupid): Prefer simple logic, especially in API Routes; complex logic in Domain.
        -   Single Responsibility: Each function/component should have one primary task.
        -   Naming: Use descriptive names for variables, functions, classes (e.g., `generateUniqueCode`, `verifyWhatsAppOtp`).
        -   Comments: Explain complex or non-obvious logic. Avoid redundant comments.
    -   **Type Safety**:
        -   Use TypeScript for all code.
        -   Leverage Zod for robust input validation (API, forms).
        -   Use JSDoc for documenting complex types if needed.
    -   **Error Handling**:
        -   Implement `try-catch` in API Routes and repositories.
        -   Use standard error responses for APIs: `{ error: string, status: number }`.
        -   Employ custom errors from `lib/domain/errors/` for better context.
    -   **Logging (Using Console)**:
        -   Use `console.log()`, `console.info()`, `console.warn()`, `console.error()` for application event logging.
        -   Log important events, errors, and state changes with relevant context (PRD 5.1, 5.3).
        -   Optional Sentry for error tracking if budget allows (PRD 3., 5.1).
    -   **Documentation Access with Context7**:
        -   Always use Context7 to access official documentation for the project's tech stack.
        -   Before implementing features, consult Context7 for best practices and correct implementation patterns.
        -   Prefer official documentation from Context7 over third-party blogs or StackOverflow solutions.
        -   When working with Next.js, React, Tailwind, shadcn/ui, Drizzle ORM, Redis, or any other library, always use Context7 to obtain accurate implementation details.
        -   Include relevant Context7 source references in code comments for complex implementations.

-   **Utilities (`lib/utils/`)**:
    -   House shared, reusable functions (e.g., JWT utilities, Google OAuth helpers). The dedicated logger utility `logger.ts` is removed in favor of standard console logging.
    -   Ensure utilities are well-tested and documented.

-   **Shared TypeScript Types (`types/index.ts`)**:
    -   Define shared TypeScript interfaces and types here to avoid duplication and maintain consistency (PRD 5.2).

-   **Styling (`styles/globals.css`, `tailwind.config.ts`, `postcss.config.mjs`)**:
    -   Use Tailwind CSS with `shadcn/ui` (PRD 5.1).
    -   Follow the color palette (Primary: indigo-600, Neutrals: slate) and typography (Inter font) defined in PRD 5.9.
    -   Ensure styles are responsive and support light/dark themes.

-   **Configuration Files**:
    -   `next.config.mjs`, `tsconfig.json`, `package.json`, `.eslintrc.json`, `middleware.ts`, `components.json`.
    -   Maintain these files according to project needs and best practices for each tool.
    -   Environment variables for sensitive data (Google OAuth, JWT secret, n8n keys, DB credentials) (PRD 5.6).

-   **Testing (`tests/`) (PRD 3., 5.1, 5.3)**:
    -   Unit tests for Domain Layer (use cases, entities) with Jest (>80% coverage).
    -   Component tests for UI components (e.g., `GoogleLoginButton`) with React Testing Library.
    -   Integration tests for API Routes with mocked database/Redis.
    -   Use ESLint (TypeScript rules) and Prettier for linting and formatting.
    -   Employ Husky for pre-commit hooks (linting, testing).

-   **Feature Implementation Verification**:
    -   **Mandatory Code Review**: MANDATORY - Before implementing ANY feature, thoroughly examine all existing code in API routes, domain entities, use cases, repositories, and UI components to fully understand the data flow and existing patterns.
    -   **API Endpoint Verification**: MANDATORY - Check all existing API endpoints that might be related to your feature. Understand their parameters, return types, and response formats.
    -   **Data Source Verification**: MANDATORY - For features displaying statistics or aggregated data, verify the correct data sources by checking database schema, existing queries, and repositories.
    -   **Code Analysis**: Before implementing any feature, thoroughly review existing code in API routes, domain entities, use cases, and repositories to understand data flow and existing patterns.
    -   **End-to-End Understanding**: Understand the complete flow from database to UI for any existing related features.
    -   **API Integration**: Check API endpoint implementations including parameters, return types, and error handling.
    -   **Existing Utilities**: Confirm if there are existing utilities, hooks, or components already built for similar use cases.
    -   **Domain Logic Validation**: Ensure business logic alignment between UI implementation and domain layer.
    -   **End-to-End Testing**: Before considering any feature complete, manually test it from end to end.
    -   **API Path Verification**: Ensure API endpoints match expected URL structures and patterns.
    -   **Error Handling Verification**: Test error scenarios and ensure they are properly handled.
    -   **Console Error Monitoring**: Check for console errors and fix them before completing implementation.
    -   **Data Validation**: Verify that data is properly validated, transformed, and displayed.
    -   **Edge Cases**: Test boundary conditions and unusual inputs to ensure robustness.
    -   **Browser Compatibility**: Test in different browsers if possible (at minimum, the latest Chrome).
    -   **Responsive Design**: Verify functionality on both desktop and mobile viewports.
    -   **Data Consistency**: Ensure that related data across different parts of the application stays in sync.
    -   **Caching Behavior**: Verify that cache invalidation works correctly when data changes.

-   **Documentation and Setup**:
    -   `README.md`: Keep updated with project setup, build, and run instructions.
    -   `prd.md`: The primary source of truth for requirements and specifications. Refer to it often.
    -   `.gitignore`: Ensure unnecessary files are not committed.
    -   `scripts/`: Utility scripts for development, build, or deployment tasks.

-   **Database Migrations (`drizzle/`)**:
    -   Manage database schema changes using Drizzle Kit migrations.
    -   Store migration SQL files in the `drizzle` directory.
    -   Follow procedures for generating and applying migrations.

-   **Public Assets (`public/`)**:
    -   Store static assets like images, fonts, etc.
    -   Optimize assets for web performance.

-   **Time Zone**:
    -   All timestamps should be in WITA (UTC+8) for Banjarmasin context (PRD 1., 3.).

-   **Performance (PRD 5.3, 7.)**:
    -   Aim for API response < 200ms, page load < 2 seconds.
    -   Utilize Redis caching effectively (>80% hit rate for profiles/reports).
    -   Ensure Drizzle ORM queries are optimized with necessary indexes.

-   **Authentication**:
    -   Students: Username/password with JWT and refresh tokens in Redis. Google email is only an optional field in the student profile, not used for authentication.
