---
description: 
globs: 
alwaysApply: true
---
-   **Domain Layer (`lib/domain/`) - General Principles**:
    -   The Domain Layer must be independent of frameworks and UI (pure TypeScript) (PRD 5.2).
    -   Contains business logic (Use Cases) and business objects (Entities).
    -   Follow Single Responsibility Principle for use cases and entities (PRD 5.3).
    -   Ensure code is DRY and KISS (PRD 5.3).

-   **Domain Entities (`lib/domain/entities/`)**:
    -   Define clear TypeScript interfaces for core entities (e.g., `Student`, `Admin`, `Absence`) (PRD 5.2, Sample Code 10.).
    -   For students, Google email is only an optional field in the entity/profile, not used for authentication.
    -   Entities should encapsulate data structures and fundamental business rules directly related to them.

-   **Domain Use Cases (`lib/domain/usecases/`)**:
    -   Implement application-specific business rules and orchestrate actions involving entities and repositories.
    -   Examples: `AuthUseCases` (username/password login, OTP logic), `AbsenceUseCases`, `ReportsUseCases` (PRD 5.2, Sample Code 10.).
    -   Use cases should depend on abstractions (interfaces for repositories defined in domain) rather than concrete data layer implementations if strict Clean Architecture is followed, though PRD example shows direct repository usage. For this project, direct usage as in PRD is acceptable.

-   **Domain Custom Errors (`lib/domain/errors/`)**:
    -   Define custom error classes (e.g., `NotFoundError`, `ValidationError`) to provide more specific error information (PRD 5.2, 5.3).

-   **Data Layer (`lib/data/`) - General Principles**:
    -   Responsible for data persistence and retrieval.
    -   Implements interfaces defined by the Domain Layer (if applicable) or provides concrete data access methods.
    -   Data Layer depends on Domain for entity definitions (PRD 5.2).

-   **Data Repositories (`lib/data/repositories/`)**:
    -   Encapsulate logic for accessing and manipulating data from PostgreSQL using Drizzle ORM.
    -   Examples: `StudentRepository` (CRUD for students), `AdminRepository`, `AbsenceRepository` (PRD 5.2, Sample Code 10.).
    -   Should handle database specific operations and map database results to domain entities.

-   **Data Cache (`lib/data/cache/`)**:
    -   Implement Redis caching logic using `@upstash/redis` (PRD 5.1).
    -   Provide utilities for setting, getting, and deleting cache entries (e.g., `RedisCache` class in PRD sample 10.).
    -   Refer to PRD 5.7 for specific caching strategies (profiles, reports, JWT sessions, OTPs).

-   **Data Drizzle ORM (`lib/data/drizzle/`, `drizzle.config.ts`)**:
    -   `schema.ts`: Define Drizzle ORM schema mirroring PostgreSQL tables (`classes`, `users`, `absences`) and types (`user_role`, `attendance_type`) (PRD 5.2, 5.4).
        -   Ensure constraints (`chk_class_name`, `chk_nis_format`, `chk_role_data`) are correctly mapped or handled.
        -   Define relations between tables.
    -   `db.ts`: Configure and export the Drizzle ORM database connection instance (PRD 5.2).
    -   `drizzle.config.ts`: Configuration for Drizzle Kit migrations.
    -   Database schema includes `attendance_summary` materialized view. Logic for refreshing this view (`POST /api/refresh-summary`) will be handled by an API calling a use case/repository method (PRD 5.4, 5.5).
    -   Ensure indexes (`idx_users_role`, `idx_users_class_id`, etc.) are defined in the schema or migrations for performance (PRD 5.4).

-   **Type Safety**:
    -   Utilize TypeScript rigorously in both Domain and Data layers.
    -   Drizzle ORM schema provides type safety for database interactions (PRD 5.3, 5.5).
