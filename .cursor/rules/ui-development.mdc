---
description: 
globs: 
alwaysApply: true
---
-   **General UI Principles**:
    -   Adhere to modern UI/UX principles, inspired by Notion, Figma, <PERSON>e, and Nielsen Norman Group usability guidelines (see PRD 4.1, 1.).
    -   Implement responsive design for mobile (min-width 320px) to desktop (max-width 1280px) (PRD 3.).
    -   Ensure WCAG 2.1 Level AA accessibility (PRD 3.).
    -   Use Tailwind CSS for styling and shadcn/ui components extensively (PRD 5.1).
    -   Implement light/dark mode using `next-themes` (PRD 4.1, 5.1).
    -   Use Framer Motion for animations and microinteractions (PRD 4.1, 5.1).
    -   Lazy-load components with `next/dynamic` where appropriate (PRD 5.3).
    -   Optimize images with `next/image` (PRD 5.3).

-   **Landing Page (`app/page.tsx`)**:
    -   Informative only, no direct links to `/student` or `/admin` apps for security (PRD 4.1).
    -   Include Hero Section, About Section (Card), Features Section (3 Cards with Lucide icons), Footer, and Theme Toggle (PRD 4.1).

-   **Student App (`app/student/`, `components/student/`)**:
    -   Key features: Username/password Login, WhatsApp OTP Verification, Home (QR Code), Profile (PRD 4.2).
    -   UI elements from shadcn/ui: Button, Input, Dialog, Toast, Tabs, Icons (PRD 4.2).

-   **Admin App (`app/admin/`, `components/admin/`)**:
    -   Key features: Login, Home (Scanner with jsQR), Reports, User Management, Profile (PRD 4.3).
    -   UI elements from shadcn/ui: Select, Button, Card, Dialog, Table, Input, Tabs, Icons (PRD 4.3).

-   **Shared Components (`components/common/`, `components/ui/`)**:
    -   Develop reusable components (e.g., ThemeToggle, BottomNav) (PRD 5.2, 5.3).
    -   Ensure shadcn/ui components are used consistently.
    -   Use the `ColoredProgress` component (`components/ui/colored-progress.tsx`) for displaying progress bars with custom colors.
    -   Implement visualization components that support both light and dark mode with appropriate contrast ratios.

-   **Custom React Hooks (`hooks/`)**:
    -   Create custom hooks for reusable component logic to keep components DRY.
    -   Ensure hooks are well-documented regarding their purpose, parameters, and return values.
