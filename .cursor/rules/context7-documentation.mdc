---
description: 
globs: 
alwaysApply: true
---
### Context7 Documentation Guidelines for ShalatYuk

-   **Using Context7 for Documentation**:
    -   **Always Use Context7**: When you need information about a library, framework, or technology used in ShalatYuk, use Context7 to get accurate, up-to-date documentation directly from official sources.
    
    -   **Retrieving Documentation**:
        -   First, resolve the library name using `mcp_context7_resolve-library-id`.
        -   Then, fetch the documentation with `mcp_context7_get-library-docs` using the resolved library ID.
        -   For specific topics, use the `topic` parameter to narrow down results.
    
    -   **Prioritized Technologies**:
        -   **Next.js**: For all app router, server components, data fetching, API routes, and middleware concerns.
        -   **Drizzle ORM**: For database schema definition, queries, migrations, and relationships.
        -   **Redis/Upstash**: For caching and session storage implementations.
        -   **Tailwind CSS**: For styling components and responsive design.
        -   **shadcn/ui**: For UI component implementations and customizations.
        -   **Auth.js (next-auth)**: For admin authentication flows only (if used). Student authentication is only via username/password with JWT and refresh tokens. Google email is only an optional profile field, not used for login.
        -   **<PERSON><PERSON>**: For input validation and type safety.
        -   **React**: For component patterns, hooks, and state management.
    
    -   **Implementation Pattern**:
        -   Before implementing a feature, use Context7 to understand the recommended patterns.
        -   Document the source of the pattern in code comments.
        -   Example: `// Based on Next.js App Router documentation from Context7`
    
    -   **Troubleshooting**:
        -   When facing issues, first consult Context7 for common solutions before resorting to other sources.
        -   If Context7 doesn't provide a solution, document why and the alternative source used.
    
    -   **Version Compatibility**:
        -   Use Context7 to verify compatibility between different libraries in our stack.
        -   Document any version-specific implementation details.

-   **Context7 Usage Examples**:
    -   **Next.js App Router**:
        ```
        // Get Next.js documentation about app router
        mcp_context7_resolve-library-id(libraryName: "vercel/nextjs")
        mcp_context7_get-library-docs(context7CompatibleLibraryID: "vercel/nextjs", topic: "app router")
        ```
    
    -   **Drizzle ORM Schema**:
        ```
        // Get Drizzle ORM documentation about schema definition
        mcp_context7_resolve-library-id(libraryName: "drizzle-orm")
        mcp_context7_get-library-docs(context7CompatibleLibraryID: "drizzle-team/drizzle-orm", topic: "schema")
        ```
    
    -   **Redis Caching**:
        ```
        // Get Upstash Redis documentation
        mcp_context7_resolve-library-id(libraryName: "upstash/redis")
        mcp_context7_get-library-docs(context7CompatibleLibraryID: "upstash/redis")
        ```
    
    -   **shadcn/ui Components**:
        ```
        // Get shadcn/ui documentation about specific components
        mcp_context7_resolve-library-id(libraryName: "shadcn/ui")
        mcp_context7_get-library-docs(context7CompatibleLibraryID: "shadcn/ui", topic: "components")
        ```

-   **Best Practices**:
    -   Always try to implement solutions based on official documentation first.
    -   Keep documentation tokens reasonable (default: 10000) for focused results.
    -   When facing complex integrations between multiple libraries, get docs for each library and understand how they interact.
    -   For performance optimization concerns, specifically request documentation about performance best practices.
