---
description: 
globs: 
alwaysApply: true
---
-   **Architecture & Design**:
    -   Implement API Routes as the Presentation Layer in Clean Architecture (PRD 5.2).
    -   Business logic should reside in Domain Layer (Use Cases, `lib/domain/usecases/`) (PRD 5.2).
    -   API Routes should primarily handle HTTP request/response, input validation, and calling use cases.
    -   Keep API route handlers concise and focused (KISS principle, PRD 5.3).

-   **Authentication & Authorization**:
    -   Students: Username/password authentication with <PERSON>WT (`jsonwebtoken`) and refresh tokens in Redis (PRD 4.2, 5.1, 5.6).
        -   `POST /api/auth/student/login` (verifies credentials, returns JWT and refresh token).
        -   Google email is an optional field in the student profile, not used for login.
    -   Admins: Username/password with JWT (`jsonwebtoken`) and refresh tokens in Redis (PRD 4.3, 5.1).
        -   `POST /api/auth/admin/login`.
    -   Logout: `POST /api/auth/logout` (removes refresh token from Redis) (PRD 4.2).
    -   Use JWT and refresh tokens for session management in API routes (no next-auth for students).

-   **Input Validation & Security**:
    -   Use Zod for all input validation in API Routes (PRD 1., 4.2, 5.1, 5.3, 5.8).
    -   Implement rate limiting (e.g., 100 requests/15 minutes/IP using custom middleware) (PRD 1., 5.1, 5.8).
    -   Ensure HTTPS is enforced (via EasyPanel) (PRD 5.1, 5.8).
    -   Drizzle ORM helps prevent SQL injection (PRD 5.8).

-   **Data Handling & Caching**:
    -   Use Drizzle ORM for PostgreSQL interactions (PRD 5.1).
    -   Utilize Redis for caching profiles, reports, JWT sessions, and OTPs with appropriate TTLs (PRD 4.2, 4.3, 5.1, 5.7).
        -   Student Profile (GET `/api/student/profile`): Cache key `student:profile:{unique_code}`, TTL 5 mins.
        -   Absence Reports (GET `/api/absence/reports`): Cache key `absence:reports:{date}:{class}`, TTL 1 min.
        -   Refresh Tokens: Key `auth:refresh:{user_id}`, TTL 7 days.
        -   OTP: Key `otp:{whatsapp}`, TTL 5 mins.
    -   Invalidate cache appropriately upon data mutation (e.g., profile update, OTP verification) (PRD 4.2).

-   **Specific API Groups & Endpoints (refer to PRD 4.2, 4.3, 5.2 for details)**:
    -   **WhatsApp OTP**:
        -   `POST /api/student/whatsapp/send-otp` (sends OTP via n8n, stores in Redis).
        -   `POST /api/student/whatsapp/verify-otp` (verifies OTP, updates DB, invalidates cache).
    -   **Student Profile**:
        -   `GET /api/student/profile` (fetches profile, uses cache).
        -   `PATCH /api/student/profile` (updates WhatsApp post-OTP, NIS; invalidates cache).
        -   `GET /api/student/{unique_code}` (fetches student data, uses cache).
    -   **Absence Recording & Checking**:
        -   `POST /api/absence/record` (records attendance with WITA timestamp).
        -   `GET /api/absence/check` (checks for duplicate attendance, uses cache).
    -   **Absence Reports**:
        -   `GET /api/absence/reports` (fetches filtered reports from `attendance_summary` view, uses cache).
        -   `POST /api/refresh-summary` (refreshes materialized view if data > 5 mins old).
    -   **User Management (Admin)**:
        -   `GET /api/users` (lists users, uses cache).
        -   `POST /api/users` (adds user, generates unique code for students).
        -   `PATCH /api/users/{id}` (edits user).
        -   `DELETE /api/users/{id}` (deletes user).
    -   **Admin Profile**:
        -   `PATCH /api/admin/profile` (updates admin details in DB).

-   **Error Handling**:
    -   Use `try-catch` blocks.
    -   Return standard error responses: `{ error: string, status: number }` (PRD 5.3).
    -   Utilize custom errors (`NotFoundError`, `ValidationError`) from `lib/domain/errors/` (PRD 5.3).

-   **Logging**:
    -   Use Winston for structured JSON logging (debug, info, error levels) for all significant API events (PRD 5.1, 5.3). Example: `logger.info("Student logged in", { email: "...", uniqueCode: "..." })`.
    -   **Logging (Using Console)**:
      -   Use `console.log()`, `console.info()`, `console.warn()`, `console.error()` for application event logging (debug, info, error levels) for all significant API events (PRD 5.1, 5.3).
      -   Example: `console.info("Student logged in", { email: "...", uniqueCode: "..." })`.
