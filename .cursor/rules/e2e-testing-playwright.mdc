---
description: 
globs: 
alwaysApply: true
---
### E2E Testing Guidelines for ShalatYuk with Playwright MCP

When tasked with performing end-to-end tests on the ShalatYuk application, utilize the configured Playwright MCP tool. Formulate your requests to the Playwright MCP tool clearly, for example:

*   "Using Playwright, navigate to `[URL]`."
*   "Using Playwright, type `[text]` into the element with selector `[CSS selector or clear description]`."
*   "Using Playwright, click the element `[CSS selector or clear description, e.g., the button with text 'Submit']`."
*   "Using Playwright, select the option `[option value or text]` from the dropdown with selector `[CSS selector]`."
*   "Using Playwright, verify that the text `[expected text]` is visible on the page."
*   "Using Playwright, verify that the element `[CSS selector or clear description]` exists."
*   "Using Playwright, take a snapshot of the current page."
*   "Using Playwright, get the current URL and verify it is `[expected URL]`."

**General Instructions for Test Scenarios:**

1.  **Reference PRD**: Always refer to `prd.md` for detailed feature specifications, UI elements, expected behaviors, and API interactions to ensure tests cover the requirements.
2.  **Target URLs**: Clearly specify the full URL for navigation (e.g., `http://localhost:3000/admin`, `http://localhost:3000/student/profile`).
3.  **Element Selectors**:
    *   Prioritize stable selectors: `data-testid`, `id`.
    *   If not available, use clear descriptions: "the button with text 'Login'", "the input field labeled 'Username'".
    *   The Playwright MCP tool will interpret these descriptions to find elements.
4.  **Test Data**: Use appropriate test data. For example:
    *   Admin login: Use valid and invalid credentials.
    *   User creation: Use unique names/usernames for new users.
5.  **Verifications**:
    *   Clearly state what needs to be verified (e.g., "verify a success toast message appears with text 'User added'", "verify the new student 'Test Student' is now listed in the table").
    *   Verify UI changes, data persistence (by checking if new/updated data appears), and navigation.
6.  **Positive and Negative Paths**: Test both successful scenarios and error conditions (e.g., submitting a form with invalid data, attempting actions without proper authentication).
7.  **Screenshots/Snapshots**: Request snapshots at key steps, especially after an action or before a verification, to aid in debugging or confirming UI state.

**Key Test Areas & Example Scenarios (ShalatYuk):**

**I. Admin Application (`http://localhost:3000/admin`)**

   a.  **Login:**
       *   Test with valid admin credentials. Expected: Successful login, navigation to admin dashboard/home.
       *   Test with invalid credentials. Expected: Error message, user remains on login page.

   b.  **User Management (`/admin/users` - Requires Admin Login):**
       *   **View Users**: Verify that the user table loads and displays existing users.
       *   **Add New Student**:
           1.  "Using Playwright, navigate to `/admin/users`."
           2.  "Using Playwright, click the 'Tambah User' button."
           3.  "Using Playwright, select 'Student' from the 'Role' dropdown."
           4.  "Using Playwright, type `Test Student Playwright` into the 'Nama' input field."
           5.  "Using Playwright, type `X IPA 1` (or a valid Class ID) into the 'ID Kelas' input field."
           6.  "Using Playwright, click the 'Tambah' button in the dialog."
           7.  "Using Playwright, verify the toast message 'User Ditambahkan' appears."
           8.  "Using Playwright, verify that 'Test Student Playwright' is now visible in the user table."
           9.  "Using Playwright, take a snapshot."
       *   **Add New Admin**: Similar to adding a student, but select 'Admin' role and fill in Username and Password fields.
       *   **Edit User**:
           1.  "Using Playwright, find the user 'Test Student Playwright' in the table and click its 'Edit' button."
           2.  "Using Playwright, change the 'Nama' field to `Test Student Updated`."
           3.  "Using Playwright, click the 'Simpan' button in the dialog."
           4.  "Using Playwright, verify the toast message 'User Diperbarui' appears."
           5.  "Using Playwright, verify that 'Test Student Updated' is now visible in the user table."
       *   **Delete User**:
           1.  "Using Playwright, find the user 'Test Student Updated' in the table and click its 'Delete' button."
           2.  "Using Playwright, confirm the deletion when prompted." (Playwright MCP needs to handle dialogs/confirmations)
           3.  "Using Playwright, verify the toast message 'User Dihapus' appears."
           4.  "Using Playwright, verify that 'Test Student Updated' is no longer visible in the user table."

   c.  **Scanner Page (`/admin/home`):**
       *   Verify navigation to the page.
       *   Verify key UI elements are present (e.g., attendance type select, "Mulai Pemindaian" button).

   d.  **Reports Page (`/admin/reports`):**
       *   Verify navigation to the page.
       *   Verify filter elements and report table structure are present.

**II. Student Application (`http://localhost:3000/student`)**

    a.  **Login:**
        *   "Using Playwright, navigate to `/student`."
        *   "Using Playwright, type `[username]` into the username input field."
        *   "Using Playwright, type `[password]` into the password input field."
        *   "Using Playwright, click the 'Login' button."
        *   "Using Playwright, verify the page redirects to the student home page (e.g., `/student/home`)."

    b.  **Profile Page (`/student/profile` - Requires Login):**
        *   (Assuming a way to mock login or set session for testing)
        *   Verify user information is displayed correctly.
        *   Attempt to update editable fields like NIS (if OTP is not strictly enforced for this field update in testing environment).

    c.  **Home Page (`/student/home` - Requires Login):**
        *   Verify unique code and QR code placeholder/image are displayed.
        *   Verify attendance status section is present.

**Remember to start your Playwright MCP server before asking Cursor to run these tests.**
The AI will request approval for each Playwright action. Review the arguments carefully before approving.
