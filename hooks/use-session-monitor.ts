/**
 * Hook for real-time session monitoring
 * Automatically checks session validity and handles force logout
 */

import { useEffect, useRef, useCallback } from 'react'
import { useRouter } from 'next/navigation'
import { toast } from 'sonner'

interface SessionMonitorOptions {
  role: 'admin' | 'student'
  intervalMs?: number // Default: 60 seconds (production safe)
  onSessionInvalid?: () => void
  enabled?: boolean
}

export function useSessionMonitor({
  role,
  intervalMs = 60000, // PRODUCTION FIX: Check every 60 seconds (less aggressive)
  onSessionInvalid,
  enabled = true,
}: SessionMonitorOptions) {
  const router = useRouter()
  const intervalRef = useRef<NodeJS.Timeout | null>(null)
  const isCheckingRef = useRef(false)
  const lastCheckRef = useRef<number>(0)

  const checkSession = useCallback(async () => {
    // Prevent multiple concurrent checks and rate limiting
    const now = Date.now()
    if (isCheckingRef.current) {
      console.log('Session check already in progress, skipping...')
      return
    }
    if (now - lastCheckRef.current < 5000) {
      console.log('Rate limit: Session check too frequent, skipping...')
      return
    }

    try {
      isCheckingRef.current = true
      lastCheckRef.current = now
      console.log(`Starting session check for role: ${role}`)

      // First check for session invalidation events (faster response)
      const eventsResponse = await fetch('/api/auth/session-events', {
        method: 'GET',
        headers: {
          'Cache-Control': 'no-cache, no-store, must-revalidate',
          Pragma: 'no-cache',
        },
        credentials: 'include',
      })

      // Only logout if we get 401 AND there are actual invalidation events
      if (eventsResponse.status === 401) {
        const eventsData = await eventsResponse.json()
        if (eventsData.hasInvalidationEvents === true) {
          console.log('Session invalidation event detected - immediate logout')
          await handleSessionInvalidation('force_logout')
          return
        }
      }

      // If events response is OK, check the response data
      if (eventsResponse.ok) {
        const eventsData = await eventsResponse.json()
        if (eventsData.hasInvalidationEvents === true) {
          console.log('Session invalidation event detected via OK response - immediate logout')
          await handleSessionInvalidation('force_logout')
          return
        }
      }

      // Then do regular session check
      const response = await fetch('/api/auth/check-session', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Cache-Control': 'no-cache, no-store, must-revalidate',
          Pragma: 'no-cache',
        },
        body: JSON.stringify({ role }),
        credentials: 'include',
      })

      if (response.status === 401) {
        console.log('Session check returned 401 - session invalid')
        const responseData = await response.json()
        console.log('Session check 401 response:', responseData)
        await handleSessionInvalidation('session_expired')
        return
      }

      // Session is valid, continue monitoring
      if (response.ok) {
        const responseData = await response.json()
        console.log('Session check passed:', responseData)
      } else {
        console.warn('Session check returned unexpected status:', response.status)
      }
    } catch (error) {
      console.error('Session check failed with error:', error)
      // Don't redirect on network errors, just log
      // Only redirect if we have clear evidence that session is invalid
    } finally {
      isCheckingRef.current = false
    }
  }, [role, router, onSessionInvalid])

  const handleSessionInvalidation = useCallback(
    async (reason: 'force_logout' | 'session_expired') => {
      // Stop monitoring immediately
      if (intervalRef.current) {
        clearInterval(intervalRef.current)
        intervalRef.current = null
      }

      // Clear any local storage
      if (typeof window !== 'undefined') {
        localStorage.removeItem('user')
        localStorage.removeItem('session')
        localStorage.removeItem('admin')
        localStorage.removeItem('student')
      }

      // Force clear cookies by calling force logout endpoint
      try {
        await fetch('/api/auth/force-logout', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ role }),
          credentials: 'include',
        })
      } catch (cookieError) {
        console.error('Failed to clear cookies:', cookieError)
      }

      // Show appropriate notification
      const message =
        reason === 'force_logout'
          ? 'Your session has been terminated by an administrator. Please login again.'
          : 'Your session has expired or been terminated. Please login again.'

      toast.error(message)

      // Call custom handler if provided
      if (onSessionInvalid) {
        onSessionInvalid()
      }

      // Redirect to login with appropriate error flag
      const loginPath = role === 'admin' ? '/admin' : '/student'
      router.push(`${loginPath}?error=${reason}`)
    },
    [role, router, onSessionInvalid]
  )

  useEffect(() => {
    if (!enabled) return

    // Start monitoring
    intervalRef.current = setInterval(checkSession, intervalMs)

    // Cleanup on unmount
    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current)
        intervalRef.current = null
      }
    }
  }, [checkSession, intervalMs, enabled])

  // Also check on window focus (user returns to tab) and visibility change
  useEffect(() => {
    if (!enabled) return

    const handleFocus = () => {
      // Reset rate limiting on focus for immediate check
      lastCheckRef.current = 0
      checkSession()
    }

    const handleVisibilityChange = () => {
      if (!document.hidden) {
        // Reset rate limiting when tab becomes visible for immediate check
        lastCheckRef.current = 0
        checkSession()
      }
    }

    window.addEventListener('focus', handleFocus)
    document.addEventListener('visibilitychange', handleVisibilityChange)

    return () => {
      window.removeEventListener('focus', handleFocus)
      document.removeEventListener('visibilitychange', handleVisibilityChange)
    }
  }, [checkSession, enabled])

  // Manual session check function
  const manualCheck = useCallback(() => {
    return checkSession()
  }, [checkSession])

  return {
    checkSession: manualCheck,
  }
}

/**
 * Hook for admin session monitoring
 */
export function useAdminSessionMonitor(options?: Omit<SessionMonitorOptions, 'role'>) {
  return useSessionMonitor({
    ...options,
    role: 'admin',
  })
}

/**
 * Hook for student session monitoring
 */
export function useStudentSessionMonitor(options?: Omit<SessionMonitorOptions, 'role'>) {
  return useSessionMonitor({
    ...options,
    role: 'student',
  })
}
