# Student Password Change Feature Test Plan

## Overview
This document outlines the test plan for the newly implemented Student Password Change feature.

## Features Implemented

### 1. Username Column in Student Table ✅
- **Location**: `/app/admin/users/page.tsx`
- **Changes**: Added Username column between ID/Kode Unik and Nama columns
- **Functionality**: Sortable, responsive, handles null values with '-'

### 2. Password Change in Edit Form ✅
- **Location**: `/app/admin/users/page.tsx` (edit form)
- **API**: `/app/api/users/[id]/route.ts` (PATCH endpoint)
- **Domain**: `/lib/domain/usecases/user.ts` (changeStudentPasswordByAdmin method)
- **Functionality**: Super Admin can change student password via edit form

## Manual Testing Checklist

### Feature 1: Username Column Display
- [ ] **Basic Display**
  - [ ] Username column appears between ID/Kode Unik and Nama
  - [ ] Column header shows "Username" with sort indicator
  - [ ] Username values display correctly for students with usernames
  - [ ] Shows '-' for students without usernames

- [ ] **Sorting**
  - [ ] Click Username column header to sort ascending
  - [ ] Click again to sort descending
  - [ ] Sort indicator (↑/↓) shows correctly
  - [ ] Sorting works correctly with null/undefined usernames

- [ ] **Responsive Design**
  - [ ] Column displays properly on desktop
  - [ ] Column displays properly on tablet
  - [ ] Column displays properly on mobile
  - [ ] Table scrolls horizontally if needed

- [ ] **Search Functionality**
  - [ ] Search includes username in results
  - [ ] Can find students by typing username
  - [ ] Search works with partial username matches

### Feature 2: Password Change via Edit Form
- [ ] **Access Control**
  - [ ] Only Super Admin can see password field in edit form
  - [ ] Regular admin cannot access student management page
  - [ ] Unauthorized users get proper error messages

- [ ] **Form Display**
  - [ ] Password field appears in edit mode only (not add mode)
  - [ ] Field has placeholder: "Kosongkan jika tidak ingin mengubah password"
  - [ ] Field is optional (can be left empty)
  - [ ] Help text shows: "Kosongkan jika tidak ingin mengubah password. Password minimal 6 karakter."

- [ ] **Form Validation**
  - [ ] Empty password field is accepted (no change)
  - [ ] Password less than 6 characters shows error
  - [ ] Password 6+ characters is accepted
  - [ ] Error message displays correctly below field
  - [ ] Form submission blocked if validation fails

- [ ] **API Integration**
  - [ ] Form submits successfully with password
  - [ ] Form submits successfully without password
  - [ ] Loading indicator shows during submission
  - [ ] Success toast appears after successful change
  - [ ] Error toast appears if API fails

- [ ] **Security**
  - [ ] Password is not logged in console
  - [ ] Only Super Admin can change passwords
  - [ ] Password is properly hashed in database
  - [ ] Cache is invalidated after password change

## Test Scenarios

### Scenario 1: Username Column Basic Functionality
1. Login as Super Admin
2. Navigate to Student Management page
3. Verify Username column is visible
4. Check various students have correct username display
5. Test sorting by clicking column header
6. Test search functionality with usernames

### Scenario 2: Password Change Happy Path
1. Login as Super Admin
2. Navigate to Student Management page
3. Click Edit on a student
4. Enter new password (8+ characters)
5. Click Save
6. Verify success message
7. Try logging in as that student with new password

### Scenario 3: Password Change Validation
1. Login as Super Admin
2. Navigate to Student Management page
3. Click Edit on a student
4. Enter short password (< 6 characters)
5. Try to save
6. Verify error message appears
7. Correct password and save successfully

### Scenario 4: Optional Password Field
1. Login as Super Admin
2. Navigate to Student Management page
3. Click Edit on a student
4. Leave password field empty
5. Change other fields (name, class)
6. Save form
7. Verify other changes saved without affecting password

### Scenario 5: Authorization Testing
1. Try accessing as regular admin (should fail)
2. Try accessing as student (should fail)
3. Verify only Super Admin can access the features

## Expected Results

### Username Column
- Column displays correctly in all screen sizes
- Sorting works properly
- Search includes username field
- Null values handled gracefully

### Password Change
- Form validation works correctly
- API calls succeed/fail appropriately
- Security measures are in place
- User experience is smooth with proper feedback

## Test Data Requirements

### Students with Different Username States
- Student with username: "student123"
- Student with null username
- Student with empty string username
- Student with special characters in username

### Test Passwords
- Valid password: "password123" (8+ chars)
- Invalid password: "123" (< 6 chars)
- Empty password: "" (should be allowed for no change)

## Automated Testing (Future)

### Unit Tests Needed
- Password validation logic
- Username column sorting
- API endpoint validation
- Domain layer password change method

### Integration Tests Needed
- Complete password change flow
- Username display and sorting
- Form submission with various inputs
- Error handling scenarios

## Notes
- All tests should be performed in a development environment
- Test with different user roles to verify access control
- Test with various data states (empty, null, special characters)
- Verify no sensitive data is logged or exposed
