# Session Management System

## Overview

The Session Management System provides robust session tracking, single-device enforcement, and administrative control over user sessions. It follows Clean Architecture principles and integrates seamlessly with the existing authentication system.

## Features

- **Single-Device Enforcement**: Users can only be logged in from one device at a time
- **Session Monitoring**: Real-time tracking of active sessions
- **Force Logout**: Super admins can force logout users from all devices
- **Session Statistics**: Comprehensive analytics on session usage
- **Device Fingerprinting**: Track sessions by device and browser
- **Automatic Cleanup**: Expired sessions are automatically cleaned up
- **Security**: Enhanced JWT tokens with session validation

## Architecture

### Domain Layer
- `SessionData`: Core session entity with metadata
- `SessionRepository`: Interface for session storage operations
- `SessionUseCases`: Business logic for session management

### Data Layer
- `RedisSessionRepository`: Redis-based session storage implementation
- Session indexing for efficient querying
- TTL-based automatic cleanup

### Application Layer
- `EnhancedAuthUseCases`: Authentication with session management
- Enhanced JWT tokens with device and session information
- Session validation middleware

### Presentation Layer
- `/api/admin/sessions`: Session management API endpoints
- `/admin/sessions`: Super admin UI for session monitoring
- `/api/user/sessions`: User's own session management

## API Endpoints

### Admin Endpoints (Super Admin Only)

#### GET /api/admin/sessions
List sessions with optional filtering.

**Query Parameters:**
- `userId`: Filter by user ID
- `role`: Filter by role (student, admin, super_admin)
- `isActive`: Filter by active status (true/false)
- `deviceId`: Filter by device ID
- `limit`: Number of results (default: 50)
- `offset`: Pagination offset (default: 0)
- `createdAfter`: Filter sessions created after date
- `createdBefore`: Filter sessions created before date

**Response:**
```json
{
  "success": true,
  "data": {
    "sessions": [
      {
        "sessionId": "uuid",
        "userId": 123,
        "userName": "John Doe",
        "role": "student",
        "deviceId": "device-uuid",
        "ipAddress": "192.168.1.xxx",
        "deviceType": "desktop",
        "browser": "Chrome",
        "createdAt": "2024-01-01T00:00:00Z",
        "lastAccessedAt": "2024-01-01T01:00:00Z",
        "expiresAt": "2024-01-01T02:00:00Z",
        "isActive": true
      }
    ],
    "pagination": {
      "total": 100,
      "limit": 50,
      "offset": 0,
      "hasMore": true
    }
  }
}
```

#### DELETE /api/admin/sessions
Invalidate a specific session or force logout a user.

**Request Body (Session Invalidation):**
```json
{
  "sessionId": "session-uuid"
}
```

**Request Body (Force Logout):**
```json
{
  "userId": 123
}
```

#### GET /api/admin/sessions/stats
Get session statistics.

**Response:**
```json
{
  "success": true,
  "data": {
    "totalSessions": 150,
    "activeSessions": 120,
    "expiredSessions": 30,
    "sessionsByRole": {
      "student": 100,
      "admin": 15,
      "super_admin": 5
    }
  }
}
```

### User Endpoints

#### GET /api/user/sessions
Get current user's active sessions.

**Response:**
```json
{
  "success": true,
  "data": {
    "sessions": [
      {
        "sessionId": "uuid",
        "deviceType": "desktop",
        "browser": "Chrome",
        "ipAddress": "192.168.1.xxx",
        "createdAt": "2024-01-01T00:00:00Z",
        "lastAccessedAt": "2024-01-01T01:00:00Z",
        "expiresAt": "2024-01-01T02:00:00Z",
        "isActive": true
      }
    ],
    "currentSessionId": "current-session-uuid"
  }
}
```

#### DELETE /api/user/sessions
Logout from other sessions (keep current session active).

**Request Body:**
```json
{
  "currentSessionId": "current-session-uuid"
}
```

## Enhanced Authentication Flow

### Login Process
1. User provides credentials
2. System validates credentials
3. Device ID is generated from user agent and IP
4. Existing session on same device is invalidated
5. New session is created in Redis
6. Enhanced JWT token is generated with session info
7. Session metadata is stored

### Token Validation
1. JWT token is verified
2. Session ID is extracted from token
3. Session is validated in Redis
4. Session last accessed time is updated
5. Token is refreshed if near expiry

### Logout Process
1. Session is invalidated in Redis
2. Refresh tokens are cleared
3. Auth cookies are cleared
4. Session event is logged

## Configuration

### Environment Variables
```env
REDIS_URL=redis://localhost:6379
JWT_SECRET=your-jwt-secret
```

### Session Constants
```typescript
export const SESSION_CONSTANTS = {
  DEFAULT_DURATION_SECONDS: 3600, // 1 hour
  MAX_SESSIONS_PER_USER: 5,
  CLEANUP_INTERVAL_MINUTES: 15,
  NEAR_EXPIRY_THRESHOLD_MINUTES: 5,
  REFRESH_THRESHOLD_MINUTES: 10,
} as const
```

## Security Considerations

### Device Fingerprinting
- Device ID is generated from user agent and IP address
- Provides basic device identification for single-device enforcement
- Not cryptographically secure but sufficient for basic session management

### Session Storage
- Sessions are stored in Redis with TTL
- Sensitive information is not exposed to client
- IP addresses are partially masked in client responses

### Token Security
- JWT tokens include session ID for validation
- Tokens are automatically refreshed near expiry
- Refresh tokens are invalidated on logout

## Monitoring and Logging

### Session Events
- Session creation, validation, refresh, invalidation
- Force logout events
- Failed authentication attempts
- Session cleanup operations

### Metrics
- Active session count by role
- Session duration statistics
- Device type distribution
- Geographic distribution (if location data available)

## Troubleshooting

### Common Issues

#### Redis Connection Issues
- Check REDIS_URL environment variable
- Verify Redis server is running
- Check network connectivity
- Review Redis logs

#### Session Validation Failures
- Verify JWT secret is consistent
- Check session TTL configuration
- Review Redis key expiration
- Validate device ID generation

#### Performance Issues
- Monitor Redis memory usage
- Review session cleanup frequency
- Check session index efficiency
- Consider session data optimization

### Debug Mode
Enable debug logging by setting:
```env
NODE_ENV=development
```

## Testing

Run the session management test suite:
```bash
npx ts-node lib/test-session-management.ts
```

The test suite covers:
- Session creation and validation
- Device fingerprinting
- Session listing and filtering
- Force logout functionality
- Session statistics
- Error handling

## Future Enhancements

### Planned Features
- Geographic location tracking
- Advanced device fingerprinting
- Session analytics dashboard
- Real-time session monitoring
- Session transfer between devices
- Suspicious activity detection

### Performance Optimizations
- Session data compression
- Batch session operations
- Improved indexing strategies
- Caching layer optimization
