import { 
  <PERSON><PERSON><PERSON>, 
  Create<PERSON>essionD<PERSON>, 
  Session<PERSON><PERSON><PERSON><PERSON>, 
  <PERSON><PERSON><PERSON><PERSON>,
  SessionValidationResult 
} from '../entities/session'

/**
 * Session repository interface for managing user sessions
 * This interface defines the contract for session storage and retrieval
 */
export interface SessionRepository {
  /**
   * Create a new session
   * @param sessionData Data for creating the session
   * @returns Promise resolving to the created session
   */
  createSession(sessionData: CreateSessionDTO): Promise<SessionData>

  /**
   * Get a session by session ID
   * @param sessionId The session identifier
   * @returns Promise resolving to session data or null if not found
   */
  getSession(sessionId: string): Promise<SessionData | null>

  /**
   * Get a session by user ID and device ID
   * @param userId The user identifier
   * @param deviceId The device identifier
   * @returns Promise resolving to session data or null if not found
   */
  getSessionByUserAndDevice(userId: number, deviceId: string): Promise<SessionData | null>

  /**
   * Get all active sessions for a user
   * @param userId The user identifier
   * @returns Promise resolving to array of active sessions
   */
  getActiveSessionsForUser(userId: number): Promise<SessionData[]>

  /**
   * Update session last accessed time
   * @param sessionId The session identifier
   * @returns Promise resolving to updated session data or null if not found
   */
  updateLastAccessed(sessionId: string): Promise<SessionData | null>

  /**
   * Invalidate a specific session
   * @param sessionId The session identifier
   * @returns Promise resolving to true if session was invalidated, false if not found
   */
  invalidateSession(sessionId: string): Promise<boolean>

  /**
   * Invalidate all sessions for a user
   * @param userId The user identifier
   * @returns Promise resolving to number of sessions invalidated
   */
  invalidateAllUserSessions(userId: number): Promise<number>

  /**
   * Invalidate all sessions for a user except the current one
   * @param userId The user identifier
   * @param currentSessionId The current session to keep active
   * @returns Promise resolving to number of sessions invalidated
   */
  invalidateOtherUserSessions(userId: number, currentSessionId: string): Promise<number>

  /**
   * Validate a session and optionally refresh it
   * @param sessionId The session identifier
   * @param refreshIfValid Whether to refresh the session if it's valid
   * @returns Promise resolving to validation result
   */
  validateSession(sessionId: string, refreshIfValid?: boolean): Promise<SessionValidationResult>

  /**
   * List sessions with optional filtering
   * @param filter Filter criteria for sessions
   * @returns Promise resolving to array of session summaries
   */
  listSessions(filter?: SessionFilter): Promise<SessionSummary[]>

  /**
   * Get session count by filter criteria
   * @param filter Filter criteria for sessions
   * @returns Promise resolving to total count of matching sessions
   */
  getSessionCount(filter?: SessionFilter): Promise<number>

  /**
   * Clean up expired sessions
   * @returns Promise resolving to number of sessions cleaned up
   */
  cleanupExpiredSessions(): Promise<number>

  /**
   * Get session statistics
   * @returns Promise resolving to session statistics
   */
  getSessionStats(): Promise<{
    totalSessions: number
    activeSessions: number
    expiredSessions: number
    sessionsByRole: Record<string, number>
  }>

  /**
   * Force logout a user from all devices
   * @param userId The user identifier
   * @returns Promise resolving to number of sessions invalidated
   */
  forceLogoutUser(userId: number): Promise<number>

  /**
   * Check if user has active sessions
   * @param userId The user identifier
   * @returns Promise resolving to true if user has active sessions
   */
  hasActiveSessions(userId: number): Promise<boolean>

  /**
   * Get user's current device sessions
   * @param userId The user identifier
   * @returns Promise resolving to array of device sessions
   */
  getUserDeviceSessions(userId: number): Promise<Array<{
    deviceId: string
    sessionId: string
    lastAccessedAt: Date
    deviceType?: string
    browser?: string
  }>>
}
