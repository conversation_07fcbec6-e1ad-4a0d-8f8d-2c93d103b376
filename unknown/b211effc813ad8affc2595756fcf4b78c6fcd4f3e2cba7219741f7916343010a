/**
 * Simple test script for session management functionality
 * Run with: npx ts-node lib/test-session-management.ts
 */

import { RedisSessionRepository } from './data/repositories/redis-session-repository'
import { SessionUseCases } from './domain/usecases/session'
import { getRedisCache } from './data/cache/redis'
import { CreateSessionDTO } from './domain/entities/session'
import { generateDeviceId, generateSessionMetadata } from './utils/session'

// Mock repositories for testing
class MockStudentRepository {
  async findById(id: number) {
    return {
      id,
      name: `Test Student ${id}`,
      uniqueCode: 'test-code',
      googleEmail: '<EMAIL>',
      username: 'testuser',
      passwordHash: 'hash',
      role: 'student' as const,
      createdAt: new Date()
    }
  }
}

class MockAdminRepository {
  async findById(id: number) {
    return {
      id,
      username: `admin${id}`,
      name: `Test Admin ${id}`,
      passwordHash: 'hash',
      role: 'super_admin' as const,
      createdAt: new Date()
    }
  }
}

async function testSessionManagement() {
  console.log('🧪 Testing Session Management System...\n')

  // Initialize dependencies
  const cache = getRedisCache()
  const sessionRepo = new RedisSessionRepository(cache)
  const studentRepo = new MockStudentRepository()
  const adminRepo = new MockAdminRepository()
  const sessionUseCases = new SessionUseCases(
    sessionRepo,
    studentRepo as any,
    adminRepo as any,
    cache
  )

  try {
    // Test 1: Create a session
    console.log('📝 Test 1: Creating a session...')
    const userAgent = 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36'
    const ipAddress = '*************'
    const deviceId = generateDeviceId(userAgent, ipAddress)
    const metadata = generateSessionMetadata(userAgent, ipAddress)

    const sessionData: CreateSessionDTO = {
      userId: 1,
      role: 'student',
      deviceId,
      ipAddress,
      userAgent,
      durationSeconds: 3600, // 1 hour
      metadata
    }

    const session = await sessionUseCases.createSession(sessionData)
    console.log('✅ Session created successfully:', {
      sessionId: session.sessionId,
      userId: session.userId,
      role: session.role,
      deviceType: session.metadata?.deviceType,
      browser: session.metadata?.browser
    })

    // Test 2: Validate the session
    console.log('\n🔍 Test 2: Validating the session...')
    const validation = await sessionUseCases.validateSession(session.sessionId)
    console.log('✅ Session validation result:', {
      isValid: validation.isValid,
      refreshed: validation.refreshed
    })

    // Test 3: Get user sessions
    console.log('\n👤 Test 3: Getting user sessions...')
    const userSessions = await sessionUseCases.getUserSessions(1)
    console.log('✅ User sessions:', userSessions.length)

    // Test 4: Create another session for the same user (different device)
    console.log('\n📱 Test 4: Creating session for different device...')
    const mobileUserAgent = 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_7_1 like Mac OS X)'
    const mobileDeviceId = generateDeviceId(mobileUserAgent, ipAddress)
    const mobileMetadata = generateSessionMetadata(mobileUserAgent, ipAddress)

    const mobileSessionData: CreateSessionDTO = {
      userId: 1,
      role: 'student',
      deviceId: mobileDeviceId,
      ipAddress,
      userAgent: mobileUserAgent,
      durationSeconds: 3600,
      metadata: mobileMetadata
    }

    const mobileSession = await sessionUseCases.createSession(mobileSessionData)
    console.log('✅ Mobile session created:', {
      sessionId: mobileSession.sessionId,
      deviceType: mobileSession.metadata?.deviceType
    })

    // Test 5: List all sessions (as super admin)
    console.log('\n📋 Test 5: Listing sessions as super admin...')
    const allSessions = await sessionUseCases.listSessions({}, 1)
    console.log('✅ Total sessions found:', allSessions.length)

    // Test 6: Get session statistics
    console.log('\n📊 Test 6: Getting session statistics...')
    const stats = await sessionUseCases.getSessionStats(1)
    console.log('✅ Session statistics:', stats)

    // Test 7: Invalidate a session
    console.log('\n🗑️ Test 7: Invalidating a session...')
    const invalidated = await sessionUseCases.invalidateSession(session.sessionId)
    console.log('✅ Session invalidated:', invalidated)

    // Test 8: Try to validate invalidated session
    console.log('\n❌ Test 8: Validating invalidated session...')
    const invalidValidation = await sessionUseCases.validateSession(session.sessionId)
    console.log('✅ Invalidated session validation:', {
      isValid: invalidValidation.isValid,
      error: invalidValidation.error
    })

    // Test 9: Force logout user
    console.log('\n🚪 Test 9: Force logout user...')
    const loggedOutCount = await sessionUseCases.forceLogoutUser(1)
    console.log('✅ Sessions logged out:', loggedOutCount)

    console.log('\n🎉 All tests completed successfully!')

  } catch (error) {
    console.error('❌ Test failed:', error)
  }
}

// Run tests if this file is executed directly
if (require.main === module) {
  testSessionManagement()
    .then(() => {
      console.log('\n✨ Session management test completed')
      process.exit(0)
    })
    .catch((error) => {
      console.error('💥 Test suite failed:', error)
      process.exit(1)
    })
}

export { testSessionManagement }
