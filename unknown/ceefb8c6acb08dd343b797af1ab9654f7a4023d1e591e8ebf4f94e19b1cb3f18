import { NextRequest, NextResponse } from 'next/server'
import { authenticateWithSession, createResponseWithRefreshedToken } from '@/lib/middleware/enhanced-auth'
import { SessionUseCases } from '@/lib/domain/usecases/session'
import { RedisSessionRepository } from '@/lib/data/repositories/redis-session-repository'
import { StudentRepository } from '@/lib/data/repositories/student'
import { AdminRepository } from '@/lib/data/repositories/admin'
import { getRedisCache } from '@/lib/data/cache/redis'
import { sanitizeSessionForClient } from '@/lib/utils/session'
import { z } from 'zod'

// Initialize dependencies
const cache = getRedisCache()
const sessionRepo = new RedisSessionRepository(cache)
const studentRepo = new StudentRepository(cache)
const adminRepo = new AdminRepository(cache)
const sessionUseCases = new SessionUseCases(sessionRepo, studentRepo, adminRepo, cache)

const logoutOtherSessionsSchema = z.object({
  currentSessionId: z.string().min(1, 'Current session ID is required'),
})

/**
 * GET /api/user/sessions
 * Get current user's active sessions
 */
export async function GET(request: NextRequest) {
  try {
    // Authenticate user
    const authResult = await authenticateWithSession(request)

    // Get user's sessions
    const sessions = await sessionUseCases.getUserSessions(authResult.id)

    // Sanitize sessions for client (remove sensitive info)
    const sanitizedSessions = sessions.map(session => sanitizeSessionForClient(session))

    // Create response
    const response = NextResponse.json({
      success: true,
      data: {
        sessions: sanitizedSessions,
        currentSessionId: authResult.sessionId
      }
    })

    // Handle token refresh if needed
    const role = authResult.role === 'student' ? 'student' : 'admin'
    return createResponseWithRefreshedToken(response, authResult, role)
  } catch (error) {
    console.error('Error getting user sessions:', error)
    
    if (error instanceof Error) {
      if (error.message.includes('Authentication required')) {
        return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
      }
    }

    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

/**
 * DELETE /api/user/sessions
 * Logout from other sessions (keep current session active)
 */
export async function DELETE(request: NextRequest) {
  try {
    // Authenticate user
    const authResult = await authenticateWithSession(request)

    // Parse request body
    const body = await request.json()

    // Validate request
    const validationResult = logoutOtherSessionsSchema.safeParse(body)
    if (!validationResult.success) {
      return NextResponse.json(
        { 
          error: 'Invalid request body',
          details: validationResult.error.errors
        },
        { status: 400 }
      )
    }

    // Logout from other sessions
    const invalidatedCount = await sessionRepo.invalidateOtherUserSessions(
      authResult.id,
      validationResult.data.currentSessionId
    )

    // Create response
    const response = NextResponse.json({
      success: true,
      message: `Logged out from ${invalidatedCount} other session(s)`,
      invalidatedCount
    })

    // Handle token refresh if needed
    const role = authResult.role === 'student' ? 'student' : 'admin'
    return createResponseWithRefreshedToken(response, authResult, role)
  } catch (error) {
    console.error('Error logging out from other sessions:', error)
    
    if (error instanceof Error) {
      if (error.message.includes('Authentication required')) {
        return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
      }
    }

    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
