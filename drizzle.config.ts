import * as dotenv from 'dotenv';

// Load environment variables from .env and .env.local
try {
  dotenv.config({ path: '.env' });
  dotenv.config({ path: '.env.local', override: true });
} catch (error) {
  console.error('Error loading environment variables:', error);
}

// Get database connection string from environment variables
const connectionString = process.env.DATABASE_URL;

if (!connectionString) {
  console.warn('Warning: DATABASE_URL is not set in environment variables.');
  console.warn('Please run the tunnel script: npm run tunnel');
  console.warn('Or set DATABASE_URL manually in .env.local');
}

// Export the configuration
export default {
  schema: './lib/data/drizzle/schema.ts',
  out: './drizzle/migrations',
  dialect: 'postgresql',
  dbCredentials: {
    // Gunakan connectionString dari environment variable
    // Jika tidak tersedia, berikan pesan error yang jelas
    connectionString: connectionString || (() => {
      console.error('DATABASE_URL tidak ditemukan di environment variables.');
      console.error('Pastikan Anda men<PERSON> script tunnel atau menyediakan DATABASE_URL di .env.local');
      return '';
    })(),
  },
  // Configuration for Drizzle Studio
  studio: {
    // Enable migration history
    migrationHistory: true,
  },
};
