"use client"

import { useRouter } from "next/navigation"
import { Home, User } from "lucide-react"

interface StudentBottomNavProps {
  activeTab: "home" | "profile"
}

export function StudentBottomNav({ activeTab }: StudentBottomNavProps) {
  const router = useRouter()

  return (
    <div className="fixed bottom-0 left-0 right-0 h-16 bg-white dark:bg-slate-800 border-t border-slate-200 dark:border-slate-700 flex items-center justify-around md:hidden">
      <button
        onClick={() => router.push("/student/home")}
        className={`flex flex-col items-center justify-center w-full h-full transition-colors ${
          activeTab === "home"
            ? "text-indigo-600 dark:text-indigo-400"
            : "text-slate-500 dark:text-slate-400 hover:bg-indigo-50 dark:hover:bg-slate-700"
        }`}
      >
        <Home className="h-5 w-5" />
        <span className="text-xs mt-1">Home</span>
      </button>

      {/* <button
        onClick={() => router.push("/student/history")}
        className={`flex flex-col items-center justify-center w-full h-full transition-colors ${
          activeTab === "history"
            ? "text-indigo-600 dark:text-indigo-400"
            : "text-slate-500 dark:text-slate-400 hover:bg-indigo-50 dark:hover:bg-slate-700"
        }`}
      >
        <History className="h-5 w-5" />
        <span className="text-xs mt-1">Riwayat</span>
      </button> */}

      <button
        onClick={() => router.push("/student/profile")}
        className={`flex flex-col items-center justify-center w-full h-full transition-colors ${
          activeTab === "profile"
            ? "text-indigo-600 dark:text-indigo-400"
            : "text-slate-500 dark:text-slate-400 hover:bg-indigo-50 dark:hover:bg-slate-700"
        }`}
      >
        <User className="h-5 w-5" />
        <span className="text-xs mt-1">Profil</span>
      </button>
    </div>
  )
}
