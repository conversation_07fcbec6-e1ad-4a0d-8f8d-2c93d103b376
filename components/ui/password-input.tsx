'use client'

import { useState, forwardRef } from 'react'
import { Eye, EyeOff } from 'lucide-react'
import { Input } from '@/components/ui/input'
import { Button } from '@/components/ui/button'
import { cn } from '@/lib/utils'

export interface PasswordInputProps
  extends Omit<React.InputHTMLAttributes<HTMLInputElement>, 'type'> {
  /**
   * Whether to show the password visibility toggle button
   * @default true
   */
  showToggle?: boolean
  /**
   * Custom class name for the container
   */
  containerClassName?: string
  /**
   * Custom class name for the toggle button
   */
  toggleClassName?: string
}

/**
 * Password input component with visibility toggle functionality
 * Follows clean architecture principles and provides consistent UX
 */
const PasswordInput = forwardRef<HTMLInputElement, PasswordInputProps>(
  (
    {
      className,
      containerClassName,
      toggleClassName,
      showToggle = true,
      disabled,
      ...props
    },
    ref
  ) => {
    const [showPassword, setShowPassword] = useState(false)

    const togglePasswordVisibility = () => {
      setShowPassword(prev => !prev)
    }

    return (
      <div className={cn('relative', containerClassName)}>
        <Input
          type={showPassword ? 'text' : 'password'}
          className={cn('pr-10', className)}
          ref={ref}
          disabled={disabled}
          {...props}
        />
        {showToggle && (
          <Button
            type="button"
            variant="ghost"
            size="sm"
            className={cn(
              'absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent',
              toggleClassName
            )}
            onClick={togglePasswordVisibility}
            disabled={disabled}
            tabIndex={-1}
            aria-label={showPassword ? 'Hide password' : 'Show password'}
          >
            {showPassword ? (
              <EyeOff className="h-4 w-4 text-slate-500" />
            ) : (
              <Eye className="h-4 w-4 text-slate-500" />
            )}
          </Button>
        )}
      </div>
    )
  }
)

PasswordInput.displayName = 'PasswordInput'

export { PasswordInput }