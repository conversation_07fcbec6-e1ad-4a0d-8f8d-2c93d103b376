'use client'

import * as React from 'react'
import * as ProgressPrimitive from '@radix-ui/react-progress'

import { cn } from '@/lib/utils'

interface ColoredProgressProps extends React.HTMLAttributes<HTMLDivElement> {
  value: number
  max?: number
  indicatorColor?: string
}

export function ColoredProgress({
  value,
  max = 100,
  indicatorColor = 'bg-primary',
  className,
  ...props
}: ColoredProgressProps) {
  const percentage = (Math.min(Math.max(0, value), max) / max) * 100

  return (
    <div className={cn('h-2 w-full overflow-hidden rounded-full', className)} {...props}>
      <div
        className={cn('h-full transition-all', indicatorColor)}
        style={{ width: `${percentage}%` }}
        role="progressbar"
        aria-valuenow={value}
        aria-valuemin={0}
        aria-valuemax={max}
      />
    </div>
  )
}
