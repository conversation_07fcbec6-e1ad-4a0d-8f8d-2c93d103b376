'use client'

import { useRouter } from 'next/navigation'
import { useEffect, useState } from 'react'
import { Spinner } from '@/components/ui/spinner'

interface StudentAuthCheckProps {
  children: React.ReactNode
}

export function StudentAuthCheck({ children }: StudentAuthCheckProps) {
  const [authStatus, setAuthStatus] = useState<'loading' | 'authenticated' | 'unauthenticated'>(
    'loading'
  )
  const router = useRouter()

  useEffect(() => {
    const checkAuth = async () => {
      try {
        // Check if we're authenticated by calling a protected API endpoint
        const response = await fetch('/api/student/check-auth', {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
          },
          credentials: 'include', // Important: include cookies
        })

        if (response.ok) {
          setAuthStatus('authenticated')
        } else {
          
          setAuthStatus('unauthenticated')
          router.push('/student')
        }
      } catch (error) {
        console.error('Auth check error:', error)
        setAuthStatus('unauthenticated')
        router.push('/student')
      }
    }

    checkAuth()
  }, [router])

  // Show loading state while checking authentication
  if (authStatus === 'loading') {
    return (
      <div className="flex h-screen w-full items-center justify-center">
        <Spinner size="lg" />
      </div>
    )
  }

  // If authenticated, render children
  if (authStatus === 'authenticated') {
    return <>{children}</>
  }

  // Don't render anything while redirecting to login
  return null
}
